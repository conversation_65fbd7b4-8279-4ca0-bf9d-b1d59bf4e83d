/* Pet Care Website - Enhanced Animations & Effects */

/* Background Enhancements */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-attachment: fixed !important;
    min-height: 100vh !important;
    font-family: 'Se<PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Page Background Animation */
.page-bg {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: -1 !important;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%) !important;
    animation: backgroundShift 20s ease-in-out infinite !important;
}

@keyframes backgroundShift {
    0%, 100% { 
        background-image: 
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    }
    50% { 
        background-image: 
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 20% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%);
    }
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%) !important;
    position: relative !important;
    overflow: hidden !important;
}

.hero-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="paws" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23paws)"/></svg>') !important;
    opacity: 0.3 !important;
}

/* Floating Animations */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
}

.floating {
    animation: float 6s ease-in-out infinite !important;
}

.floating-delayed {
    animation: float 6s ease-in-out infinite !important;
    animation-delay: 2s !important;
}

/* Pulse Animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s ease-in-out infinite !important;
}

/* Bounce Heart */
@keyframes bounceHeart {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.bounce-heart {
    animation: bounceHeart 1.5s ease-in-out infinite !important;
}

/* Wiggle Animation */
@keyframes wiggle {
    0%, 7% { transform: rotateZ(0); }
    15% { transform: rotateZ(-15deg); }
    20% { transform: rotateZ(10deg); }
    25% { transform: rotateZ(-10deg); }
    30% { transform: rotateZ(6deg); }
    35% { transform: rotateZ(-4deg); }
    40%, 100% { transform: rotateZ(0); }
}

.wiggle {
    animation: wiggle 2s ease-in-out infinite !important;
}

/* Glow Effect */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8), 0 0 30px rgba(102, 126, 234, 0.6); }
}

.glow {
    animation: glow 2s ease-in-out infinite !important;
}

/* Slide Animations */
@keyframes slideInLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
    0% { transform: translateY(100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out !important;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out !important;
}

.slide-in-up {
    animation: slideInUp 0.8s ease-out !important;
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.25) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37) !important;
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Service Cards */
.service-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    border-radius: 20px !important;
    overflow: hidden !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02) !important;
    box-shadow: 0 25px 50px rgba(0,0,0,0.2) !important;
}

.service-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent) !important;
    transition: left 0.5s !important;
}

.service-card:hover::before {
    left: 100% !important;
}

/* Cards */
.card {
    border-radius: 20px !important;
    overflow: hidden !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

/* Buttons */
.btn {
    border-radius: 30px !important;
    font-weight: 600 !important;
    padding: 12px 30px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent) !important;
    transition: left 0.5s !important;
}

.btn:hover::before {
    left: 100% !important;
}

.btn-lg {
    padding: 18px 40px !important;
    font-size: 1.1rem !important;
}

/* Gradients */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.bg-gradient-cute {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
}

.bg-gradient-pet {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
}

/* Navbar */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.navbar-brand {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Hover Effects */
.hover-lift:hover {
    transform: translateY(-10px) scale(1.02) !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.hover-glow:hover {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1), 0 0 25px rgba(102, 126, 234, 0.3) !important;
    transition: all 0.3s ease !important;
}

/* Page Enter Animation */
.page-enter {
    animation: pageEnter 0.8s ease-out !important;
}

@keyframes pageEnter {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Sparkle Effect */
@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

.sparkle {
    position: absolute !important;
    width: 10px !important;
    height: 10px !important;
    background: radial-gradient(circle, #fff 0%, transparent 70%) !important;
    border-radius: 50% !important;
    animation: sparkle 2s ease-in-out infinite !important;
}
