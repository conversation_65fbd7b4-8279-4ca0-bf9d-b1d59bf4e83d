@model RealEstateWebsite.Models.ViewModels.AdminDashboardViewModel

@{
    ViewData["Title"] = "Trang Quản Trị";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<!-- Dashboard Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
        <p class="mb-0 text-muted">Chào mừng trở lại, @ViewContext.HttpContext.Session.GetString("UserFullName")!</p>
    </div>
    <div>
        <span class="badge bg-success">Online</span>
        <small class="text-muted ms-2">Cập nhật: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</small>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tổng Bất Động Sản
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Statistics.TotalProperties</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-home fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Đã Bán/Thuê
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Statistics.SoldProperties</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Người Dùng
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Statistics.TotalUsers</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Liên Hệ Mới
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Statistics.NewContacts</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Properties Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Thống Kê Bất Động Sản Theo Tháng</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="propertiesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Phân Loại BDS</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="categoriesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <!-- Recent Properties -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Bất Động Sản Mới Nhất</h6>
            </div>
            <div class="card-body">
                @if (Model.RecentProperties.Any())
                {
                    @foreach (var property in Model.RecentProperties.Take(5))
                    {
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <img src="@(!string.IsNullOrEmpty(property.MainImage) ? property.MainImage : "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80")" 
                                     class="rounded" style="width: 60px; height: 60px; object-fit: cover;" alt="@property.Title">
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">@property.Title</h6>
                                <p class="text-muted mb-1 small">@property.Address, @property.City</p>
                                <span class="badge bg-primary">@property.Price.ToString("N0") VNĐ</span>
                            </div>
                            <div>
                                <a asp-controller="Properties" asp-action="Details" asp-route-id="@property.PropertyId" 
                                   class="btn btn-sm btn-outline-primary">Xem</a>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <p class="text-muted">Chưa có bất động sản nào.</p>
                }
                <div class="text-center">
                    <a asp-controller="Admin" asp-action="Properties" class="btn btn-primary btn-sm">Xem Tất Cả</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Contacts -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Liên Hệ Mới Nhất</h6>
            </div>
            <div class="card-body">
                @if (Model.RecentContacts.Any())
                {
                    @foreach (var contact in Model.RecentContacts.Take(5))
                    {
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">@contact.FullName</h6>
                                <p class="text-muted mb-1 small">@contact.Email</p>
                                <small class="text-muted">@contact.CreatedDate.ToString("dd/MM/yyyy HH:mm")</small>
                            </div>
                            <div>
                                <a asp-controller="Admin" asp-action="ContactDetails" asp-route-id="@contact.ContactId" 
                                   class="btn btn-sm btn-outline-info">Xem</a>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <p class="text-muted">Chưa có liên hệ nào.</p>
                }
                <div class="text-center">
                    <a asp-controller="Admin" asp-action="Contacts" class="btn btn-info btn-sm">Xem Tất Cả</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Thao Tác Nhanh</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a asp-controller="Admin" asp-action="CreateProperty" class="btn btn-success btn-block">
                            <i class="fas fa-plus"></i> Thêm BDS Mới
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a asp-controller="Admin" asp-action="Properties" class="btn btn-primary btn-block">
                            <i class="fas fa-list"></i> Quản Lý BDS
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a asp-controller="Admin" asp-action="Users" class="btn btn-info btn-block">
                            <i class="fas fa-users"></i> Quản Lý User
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a asp-controller="Admin" asp-action="Settings" class="btn btn-warning btn-block">
                            <i class="fas fa-cog"></i> Cài Đặt
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Properties Chart
        const propertiesCtx = document.getElementById('propertiesChart').getContext('2d');
        const propertiesChart = new Chart(propertiesCtx, {
            type: 'line',
            data: {
                labels: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'],
                datasets: [{
                    label: 'Bất động sản mới',
                    data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });

        // Categories Chart
        const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
        const categoriesChart = new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Nhà Phố', 'Chung Cư', 'Biệt Thự', 'Đất Nền', 'Văn Phòng'],
                datasets: [{
                    data: [30, 25, 20, 15, 10],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
}
