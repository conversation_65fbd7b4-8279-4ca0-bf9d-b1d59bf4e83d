@model PetCareWebsite.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Đăng Nhập";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0 glass-effect">
                <div class="card-header bg-gradient-primary text-white text-center py-4">
                    <h3 class="mb-0 floating">
                        🔐 Đăng Nhập
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Email" class="form-label fw-bold gradient-text">
                                📧 @Html.DisplayNameFor(m => m.Email)
                            </label>
                            <input asp-for="Email" class="form-control form-control-lg glass-effect" placeholder="Nhập email của bạn" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label fw-bold gradient-text">
                                🔒 @Html.DisplayNameFor(m => m.Password)
                            </label>
                            <input asp-for="Password" class="form-control form-control-lg glass-effect" placeholder="Nhập mật khẩu" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                @Html.DisplayNameFor(m => m.RememberMe)
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg pulse">
                                🚀 Đăng Nhập
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">
                        Chưa có tài khoản? 
                        <a asp-action="Register" class="text-primary fw-bold">Đăng ký ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
