/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --text-color: #334155;
    --text-muted: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ===== HERO SECTION STYLES ===== */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-carousel {
    position: relative;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: all 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
}

/* Animated particles background */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(-10px) rotate(-1deg); }
}

/* Hero content animations */
.animate-fade-in {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 20px rgba(0,0,0,0.3);
}

.gradient-text {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

.hero-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.95;
    text-shadow: 1px 1px 10px rgba(0,0,0,0.3);
}

/* Hero features */
.hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.hero-amenities, .hero-tech {
    display: flex;
    gap: 1.5rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.amenity-item, .tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 16px;
    border-radius: 15px;
    min-width: 100px;
    text-align: center;
    transition: all 0.3s ease;
}

.amenity-item:hover, .tech-item:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-5px);
}

.amenity-item i, .tech-item i {
    font-size: 1.5rem;
    color: #fbbf24;
}

/* Hero stats */
.hero-stats {
    display: flex;
    gap: 3rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: #fbbf24;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 10px rgba(0,0,0,0.3);
    animation: countUp 2s ease-out;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== CAROUSEL NAVIGATION ===== */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-nav:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.carousel-nav.prev {
    left: 40px;
}

.carousel-nav.next {
    right: 40px;
}

.carousel-dots {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.dot.active {
    background: #fbbf24;
    transform: scale(1.3);
}

.dot:hover {
    background: rgba(255,255,255,0.7);
    transform: scale(1.2);
}

/* ===== FLOATING SEARCH FORM ===== */
.floating-search {
    position: absolute;
    bottom: -80px;
    left: 0;
    right: 0;
    z-index: 4;
}

.search-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    max-width: 1000px;
    margin: 0 auto;
    animation: slideUp 1s ease-out 0.5s both;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-header {
    text-align: center;
    margin-bottom: 2rem;
}

.search-header h3 {
    color: var(--dark-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.search-header h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.search-header p {
    color: var(--text-muted);
    margin: 0;
}

.search-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 5px;
    margin-bottom: 2rem;
    position: relative;
}

.search-tabs input[type="radio"] {
    display: none;
}

.tab-label {
    flex: 1;
    text-align: center;
    padding: 15px 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.search-tabs input[type="radio"]:checked + .tab-label {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
}

.search-fields {
    display: grid;
    gap: 1.5rem;
}

.field-group {
    position: relative;
}

.input-group-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: var(--primary-color);
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.btn-search {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    border: none;
    border-radius: 15px;
    padding: 18px 40px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    margin-top: 1rem;
}

.btn-search:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.3);
    background: linear-gradient(135deg, #1d4ed8 0%, var(--primary-color) 100%);
}

/* ===== WHY CHOOSE US SECTION ===== */
.why-choose-us {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.why-choose-us::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.section-title {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1.2;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.section-description {
    font-size: 1.2rem;
    color: var(--text-muted);
    line-height: 1.7;
    margin-bottom: 3rem;
}

.why-features {
    margin-bottom: 3rem;
}

.feature-box {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: var(--text-muted);
    margin: 0;
    line-height: 1.6;
}

.why-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-box {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    flex: 1;
    min-width: 150px;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
}

.stat-box .stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-box .stat-text {
    color: var(--text-muted);
    font-weight: 600;
}

/* ===== IMAGE GRID ===== */
.why-images {
    position: relative;
    height: 600px;
}

.image-grid {
    position: relative;
    height: 100%;
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
}

.grid-item {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.grid-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.2);
}

.grid-item.large {
    grid-row: 1 / 3;
}

.grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.grid-item:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 2rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.grid-item:hover .image-overlay {
    transform: translateY(0);
}

.floating-element {
    position: absolute;
    z-index: 2;
    animation: floatUpDown 3s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

.floating-card {
    background: white;
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--dark-color);
}

.floating-card i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* ===== FEATURED PROPERTIES SECTION ===== */
.featured-properties {
    padding: 120px 0;
    background: white;
    position: relative;
}

.featured-properties::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 10% 90%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 90% 10%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 1;
}

/* ===== ENHANCED PROPERTY CARDS ===== */
.property-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    position: relative;
    border: 1px solid rgba(0,0,0,0.05);
}

.property-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 60px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.property-image {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.property-card:hover .property-image img {
    transform: scale(1.1);
}

.property-badges {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 2;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.property-badges .badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

.property-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    backdrop-filter: blur(5px);
}

.property-card:hover .property-overlay {
    opacity: 1;
}

.property-overlay .btn {
    transform: translateY(20px);
    transition: transform 0.4s ease 0.1s;
}

.property-card:hover .property-overlay .btn {
    transform: translateY(0);
}

.property-content {
    padding: 2rem;
}

.property-price {
    font-size: 1.8rem;
    font-weight: 900;
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    display: flex;
    align-items: baseline;
    gap: 5px;
}

.price-unit {
    font-size: 1rem;
    color: var(--text-muted);
    font-weight: 500;
}

.property-title {
    margin-bottom: 1rem;
}

.property-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.3rem;
    transition: color 0.3s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.property-title a:hover {
    color: var(--primary-color);
}

.property-location {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.property-location i {
    color: var(--danger-color);
    font-size: 1.1rem;
}

.property-features {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
    flex-wrap: wrap;
}

.property-features span {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
}

.property-features i {
    color: var(--primary-color);
}

.property-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 2px solid #f1f5f9;
}

.property-agent {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 500;
}

.agent-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.property-date {
    font-size: 0.85rem;
    color: var(--text-muted);
    background: #f8f9fa;
    padding: 6px 12px;
    border-radius: 15px;
    font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 3.5rem;
    }

    .section-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .hero-features {
        gap: 1rem;
    }

    .floating-search {
        bottom: -60px;
    }

    .search-card {
        margin: 0 20px;
        padding: 1.5rem;
    }

    .why-stats {
        gap: 1rem;
    }

    .image-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
    }

    .grid-item.large {
        grid-row: auto;
        height: 300px;
    }

    .grid-item.small {
        height: 200px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 100vh;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .hero-features, .hero-amenities, .hero-tech {
        justify-content: center;
        gap: 0.5rem;
    }

    .feature-item, .amenity-item, .tech-item {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .carousel-nav {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .carousel-nav.prev {
        left: 20px;
    }

    .carousel-nav.next {
        right: 20px;
    }

    .floating-search {
        bottom: -40px;
    }

    .search-card {
        margin: 0 15px;
        padding: 1rem;
        border-radius: 15px;
    }

    .search-header h3 {
        font-size: 1.5rem;
    }

    .tab-label {
        padding: 12px 15px;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .why-choose-us {
        padding: 80px 0;
    }

    .featured-properties {
        padding: 80px 0;
    }

    .feature-box {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .why-stats {
        flex-direction: column;
    }

    .why-images {
        margin-top: 3rem;
        height: auto;
    }

    .floating-element {
        display: none;
    }

    .property-features {
        gap: 0.5rem;
    }

    .property-features span {
        font-size: 0.8rem;
        padding: 6px 10px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.9rem;
    }

    .search-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-label {
        padding: 10px;
    }

    .btn-search {
        padding: 15px 30px;
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .property-card {
        margin-bottom: 2rem;
    }

    .property-content {
        padding: 1.5rem;
    }

    .property-price {
        font-size: 1.5rem;
    }

    .property-title a {
        font-size: 1.1rem;
    }
}

/* ===== SCROLL ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* ===== LOADING ANIMATIONS ===== */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* ===== SMOOTH SCROLLING ===== */
html {
    scroll-behavior: smooth;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1d4ed8, var(--primary-color));
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 3;
}

.carousel-nav:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-50%) scale(1.1);
}

.carousel-nav.prev {
    left: 30px;
}

.carousel-nav.next {
    right: 30px;
}

.carousel-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: white;
    transform: scale(1.2);
}

.search-overlay {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
    padding: 2rem 0;
}

/* ===== PROPERTY CARD STYLES ===== */
.property-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.property-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
    transform: scale(1.05);
}

.property-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.property-badges .badge {
    margin-right: 5px;
    font-size: 0.75rem;
    padding: 5px 10px;
}

.property-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.property-card:hover .property-overlay {
    opacity: 1;
}

.property-content {
    padding: 1.5rem;
}

.property-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--danger-color);
    margin-bottom: 0.5rem;
}

.price-unit {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 400;
}

.property-title {
    margin-bottom: 0.75rem;
}

.property-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.property-title a:hover {
    color: var(--primary-color);
}

.property-location {
    color: var(--text-muted);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.property-location i {
    margin-right: 5px;
    color: var(--danger-color);
}

.property-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.property-features span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.property-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.property-agent {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.agent-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.property-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #ffffff;
}

/* ===== HEADER STYLES ===== */
.main-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    box-shadow: var(--shadow);
}

.top-bar {
    background: var(--dark-color);
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.top-bar .contact-info span {
    margin-right: 20px;
}

.top-bar .contact-info i {
    margin-right: 5px;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--success-color);
    color: white;
    transform: translateY(-2px);
}

.main-nav {
    padding: 15px 0;
    background: white;
}

.navbar-brand {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-color) !important;
    padding: 10px 15px !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: var(--light-color);
}

.navbar-nav .nav-link i {
    margin-right: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 12px;
    padding: 10px 0;
    margin-top: 10px;
}

.dropdown-item {
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.navbar-actions .btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== UTILITY CLASSES ===== */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.card {
    border: none;
    border-radius: 16px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .top-bar {
        text-align: center;
    }

    .top-bar .contact-info {
        margin-bottom: 10px;
    }

    .navbar-actions {
        margin-top: 15px;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-padding {
        padding: 50px 0;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}