using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models
{
    public class Favorite
    {
        public int FavoriteId { get; set; }

        [Required]
        public int PropertyId { get; set; }

        [Required(ErrorMessage = "Session ID là bắt buộc")]
        [StringLength(100, ErrorMessage = "Session ID không được vượt quá 100 ký tự")]
        public string SessionId { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation property
        public Property Property { get; set; } = null!;

        // Optional: User information if implementing user system
        [StringLength(100)]
        public string? UserEmail { get; set; }

        [StringLength(100)]
        public string? UserName { get; set; }
    }
}
