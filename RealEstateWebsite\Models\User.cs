using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models
{
    public class User
    {
        public int UserId { get; set; }

        [Required(ErrorMessage = "Tên đăng nhập là bắt buộc")]
        [StringLength(50, ErrorMessage = "Tên đăng nhập không được vượt quá 50 ký tự")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email là bắt buộc")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [StringLength(100, ErrorMessage = "Email không được vượt quá 100 ký tự")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
        [StringLength(255, ErrorMessage = "Mật khẩu không được vượt quá 255 ký tự")]
        public string PasswordHash { get; set; } = string.Empty;

        [Required(ErrorMessage = "Họ tên là bắt buộc")]
        [StringLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "Số điện thoại không được vượt quá 20 ký tự")]
        public string? Phone { get; set; }

        [StringLength(500, ErrorMessage = "Địa chỉ không được vượt quá 500 ký tự")]
        public string? Address { get; set; }

        [StringLength(200, ErrorMessage = "Avatar không được vượt quá 200 ký tự")]
        public string? Avatar { get; set; }

        public UserRole Role { get; set; } = UserRole.Customer;

        public bool IsActive { get; set; } = true;

        public bool EmailConfirmed { get; set; } = false;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        // Additional profile information
        public DateTime? DateOfBirth { get; set; }

        public Gender? Gender { get; set; }

        [StringLength(100)]
        public string? Occupation { get; set; }

        [StringLength(100)]
        public string? Company { get; set; }

        // Preferences for property search
        public decimal? PreferredBudgetMin { get; set; }
        public decimal? PreferredBudgetMax { get; set; }

        [StringLength(200)]
        public string? PreferredLocation { get; set; }

        public PropertyType? PreferredPropertyType { get; set; }

        // Social media links
        [StringLength(200)]
        public string? FacebookUrl { get; set; }

        [StringLength(200)]
        public string? LinkedInUrl { get; set; }

        // Security
        [StringLength(100)]
        public string? SecurityQuestion { get; set; }

        [StringLength(255)]
        public string? SecurityAnswerHash { get; set; }

        // Email verification
        [StringLength(100)]
        public string? EmailVerificationToken { get; set; }

        public DateTime? EmailVerificationExpiry { get; set; }

        // Password reset
        [StringLength(100)]
        public string? PasswordResetToken { get; set; }

        public DateTime? PasswordResetExpiry { get; set; }
    }

    public enum UserRole
    {
        Customer = 1,         // Khách hàng
        Agent = 2,           // Nhân viên tư vấn
        Admin = 3,           // Quản trị viên
        SuperAdmin = 4       // Quản trị viên cấp cao
    }

    public enum Gender
    {
        Male = 1,            // Nam
        Female = 2,          // Nữ
        Other = 3            // Khác
    }
}
