#include "Graph.h"

namespace GraphUtils {

// Tạo đồ thị mẫu 1 (đơn giản)
Graph createSampleGraph1() {
    Graph g(4);
    
    std::cout << "\n=== ĐỒ THỊ MẪU 1 (ĐƠN GIẢN) ===\n";
    std::cout << "Mô tả: Mạng đơn giản với 4 đỉnh\n";
    std::cout << "Đỉnh: 0, 1, 2, 3\n";
    std::cout << "Cạnh:\n";
    std::cout << "0 -> 1: 16\n";
    std::cout << "0 -> 2: 13\n";
    std::cout << "1 -> 2: 10\n";
    std::cout << "1 -> 3: 12\n";
    std::cout << "2 -> 1: 4\n";
    std::cout << "2 -> 3: 14\n\n";
    
    g.addEdge(0, 1, 16);
    g.addEdge(0, 2, 13);
    g.addEdge(1, 2, 10);
    g.addEdge(1, 3, 12);
    g.addEdge(2, 1, 4);
    g.addEdge(2, 3, 14);
    
    return g;
}

// Tạo đồ thị mẫu 2 (phức tạp hơn)
Graph createSampleGraph2() {
    Graph g(6);
    
    std::cout << "\n=== ĐỒ THỊ MẪU 2 (PHỨC TẠP) ===\n";
    std::cout << "Mô tả: Mạng phức tạp với 6 đỉnh\n";
    std::cout << "Đỉnh: 0, 1, 2, 3, 4, 5\n";
    std::cout << "Cạnh:\n";
    std::cout << "0 -> 1: 10\n";
    std::cout << "0 -> 2: 8\n";
    std::cout << "1 -> 2: 5\n";
    std::cout << "1 -> 3: 5\n";
    std::cout << "2 -> 4: 10\n";
    std::cout << "3 -> 2: 7\n";
    std::cout << "3 -> 4: 8\n";
    std::cout << "3 -> 5: 10\n";
    std::cout << "4 -> 5: 10\n\n";
    
    g.addEdge(0, 1, 10);
    g.addEdge(0, 2, 8);
    g.addEdge(1, 2, 5);
    g.addEdge(1, 3, 5);
    g.addEdge(2, 4, 10);
    g.addEdge(3, 2, 7);
    g.addEdge(3, 4, 8);
    g.addEdge(3, 5, 10);
    g.addEdge(4, 5, 10);
    
    return g;
}

// Tạo đồ thị mẫu 3 (mạng giao thông)
Graph createSampleGraph3() {
    Graph g(8);
    
    std::cout << "\n=== ĐỒ THỊ MẪU 3 (MẠNG GIAO THÔNG) ===\n";
    std::cout << "Mô tả: Mô phỏng mạng giao thông thành phố với 8 nút giao thông\n";
    std::cout << "Đỉnh: 0(Bắc), 1(Đông Bắc), 2(Đông), 3(Đông Nam), 4(Nam), 5(Tây Nam), 6(Tây), 7(Tây Bắc)\n";
    std::cout << "Cạnh (đường giao thông với lưu lượng xe/giờ):\n";
    std::cout << "0 -> 1: 20 (Bắc -> Đông Bắc)\n";
    std::cout << "0 -> 7: 15 (Bắc -> Tây Bắc)\n";
    std::cout << "1 -> 2: 25 (Đông Bắc -> Đông)\n";
    std::cout << "1 -> 3: 10 (Đông Bắc -> Đông Nam)\n";
    std::cout << "2 -> 3: 30 (Đông -> Đông Nam)\n";
    std::cout << "3 -> 4: 20 (Đông Nam -> Nam)\n";
    std::cout << "4 -> 5: 25 (Nam -> Tây Nam)\n";
    std::cout << "5 -> 6: 15 (Tây Nam -> Tây)\n";
    std::cout << "6 -> 7: 20 (Tây -> Tây Bắc)\n";
    std::cout << "7 -> 0: 10 (Tây Bắc -> Bắc)\n";
    std::cout << "1 -> 7: 12 (Đông Bắc -> Tây Bắc)\n";
    std::cout << "2 -> 6: 18 (Đông -> Tây)\n";
    std::cout << "3 -> 5: 22 (Đông Nam -> Tây Nam)\n\n";
    
    g.addEdge(0, 1, 20);
    g.addEdge(0, 7, 15);
    g.addEdge(1, 2, 25);
    g.addEdge(1, 3, 10);
    g.addEdge(2, 3, 30);
    g.addEdge(3, 4, 20);
    g.addEdge(4, 5, 25);
    g.addEdge(5, 6, 15);
    g.addEdge(6, 7, 20);
    g.addEdge(7, 0, 10);
    g.addEdge(1, 7, 12);
    g.addEdge(2, 6, 18);
    g.addEdge(3, 5, 22);
    
    return g;
}

// Hiển thị thông tin về thuật toán
void displayAlgorithmInfo() {
    std::cout << "\n=== THÔNG TIN THUẬT TOÁN FORD-FULKERSON ===\n\n";
    
    std::cout << "1. KHÁI NIỆM CƠ BẢN:\n";
    std::cout << "   - Bài toán luồng cực đại: Tìm luồng lớn nhất từ nguồn đến đích\n";
    std::cout << "   - Luồng: Lượng dữ liệu/vật chất có thể truyền qua mạng\n";
    std::cout << "   - Khả năng thông qua: Giới hạn tối đa của mỗi cạnh\n\n";
    
    std::cout << "2. THUẬT TOÁN FORD-FULKERSON:\n";
    std::cout << "   - Ý tưởng: Liên tục tìm đường tăng luồng và cập nhật\n";
    std::cout << "   - Đường tăng luồng: Đường từ nguồn đến đích trong mạng dư\n";
    std::cout << "   - Mạng dư: Mạng với khả năng còn lại sau khi trừ luồng hiện tại\n\n";
    
    std::cout << "3. THUẬT TOÁN EDMONDS-KARP:\n";
    std::cout << "   - Cài đặt cụ thể của Ford-Fulkerson\n";
    std::cout << "   - Sử dụng BFS để tìm đường tăng luồng ngắn nhất\n";
    std::cout << "   - Độ phức tạp: O(VE²)\n\n";
    
    std::cout << "4. CÁC BƯỚC THỰC HIỆN:\n";
    std::cout << "   Bước 1: Khởi tạo luồng = 0\n";
    std::cout << "   Bước 2: Tìm đường tăng luồng bằng BFS\n";
    std::cout << "   Bước 3: Nếu không tìm thấy -> Dừng\n";
    std::cout << "   Bước 4: Tính luồng tăng thêm (min trên đường)\n";
    std::cout << "   Bước 5: Cập nhật mạng dư\n";
    std::cout << "   Bước 6: Quay lại Bước 2\n\n";
    
    std::cout << "5. ĐỊNH LÝ MAX-FLOW MIN-CUT:\n";
    std::cout << "   - Giá trị luồng cực đại = Khả năng lát cắt tối thiểu\n";
    std::cout << "   - Lát cắt: Phân chia đỉnh thành 2 tập (chứa nguồn và đích)\n";
    std::cout << "   - Ứng dụng: Tìm điểm nghẽn trong mạng\n\n";
    
    std::cout << "6. ỨNG DỤNG THỰC TẾ:\n";
    std::cout << "   - Mạng giao thông: Tối ưu lưu lượng xe\n";
    std::cout << "   - Mạng viễn thông: Phân bổ băng thông\n";
    std::cout << "   - Chuỗi cung ứng: Tối ưu vận chuyển\n";
    std::cout << "   - Bài toán ghép cặp: Matching trong đồ thị\n\n";
}

// Hiển thị hướng dẫn sử dụng
void displayUsageGuide() {
    std::cout << "\n=== HƯỚNG DẪN SỬ DỤNG CHƯƠNG TRÌNH ===\n\n";
    
    std::cout << "1. NHẬP ĐỒ THỊ:\n";
    std::cout << "   - Chọn menu 4 để nhập đồ thị mới\n";
    std::cout << "   - Nhập số cạnh\n";
    std::cout << "   - Với mỗi cạnh, nhập: đỉnh_nguồn đỉnh_đích khả_năng\n";
    std::cout << "   - Ví dụ: 0 1 10 (cạnh từ đỉnh 0 đến đỉnh 1 với khả năng 10)\n\n";
    
    std::cout << "2. XEM MA TRẬN KỀ:\n";
    std::cout << "   - Chọn menu 1 để hiển thị ma trận kề\n";
    std::cout << "   - Ma trận[i][j] = khả năng thông qua từ đỉnh i đến đỉnh j\n\n";
    
    std::cout << "3. TÌM LUỒNG CỰC ĐẠI:\n";
    std::cout << "   - Chọn menu 2\n";
    std::cout << "   - Nhập đỉnh nguồn và đỉnh đích\n";
    std::cout << "   - Chương trình sẽ hiển thị từng bước của thuật toán\n\n";
    
    std::cout << "4. TÌM LÁT CẮT TỐI THIỂU:\n";
    std::cout << "   - Chọn menu 3\n";
    std::cout << "   - Nhập đỉnh nguồn và đỉnh đích\n";
    std::cout << "   - Xem các cạnh tạo thành lát cắt tối thiểu\n\n";
    
    std::cout << "5. DEMO VỚI ĐỒ THỊ MẪU:\n";
    std::cout << "   - Chọn menu 5 để thử nghiệm với đồ thị có sẵn\n";
    std::cout << "   - 3 đồ thị mẫu với độ phức tạp khác nhau\n\n";
    
    std::cout << "6. LƯU Ý:\n";
    std::cout << "   - Đỉnh được đánh số từ 0\n";
    std::cout << "   - Khả năng thông qua phải > 0\n";
    std::cout << "   - Đồ thị có hướng\n";
    std::cout << "   - Nguồn và đích phải khác nhau\n\n";
}

} // namespace GraphUtils
