using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PetCareWebsite.Data;
using PetCareWebsite.Models;
using PetCareWebsite.ViewModels;

namespace PetCareWebsite.Controllers;

[Authorize]
public class PetsController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;

    public PetsController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
    {
        _context = context;
        _userManager = userManager;
    }

    public async Task<IActionResult> Index()
    {
        var userId = _userManager.GetUserId(User);
        var pets = await _context.Pets
            .Where(p => p.UserId == userId)
            .OrderBy(p => p.Name)
            .ToListAsync();

        return View(pets);
    }

    public async Task<IActionResult> Details(int id)
    {
        var userId = _userManager.GetUserId(User);
        var pet = await _context.Pets
            .Include(p => p.Appointments)
            .ThenInclude(a => a.Service)
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == userId);

        if (pet == null)
        {
            return NotFound();
        }

        return View(pet);
    }

    public IActionResult Create()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(PetViewModel model)
    {
        if (ModelState.IsValid)
        {
            var userId = _userManager.GetUserId(User);
            var pet = new Pet
            {
                Name = model.Name,
                Species = model.Species,
                Breed = model.Breed,
                Age = model.Age,
                Gender = model.Gender,
                Weight = model.Weight,
                Description = model.Description,
                UserId = userId!
            };

            _context.Pets.Add(pet);
            await _context.SaveChangesAsync();
            
            TempData["Success"] = "Thông tin thú cưng đã được thêm thành công!";
            return RedirectToAction(nameof(Index));
        }

        return View(model);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var userId = _userManager.GetUserId(User);
        var pet = await _context.Pets
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == userId);

        if (pet == null)
        {
            return NotFound();
        }

        var model = new PetViewModel
        {
            Id = pet.Id,
            Name = pet.Name,
            Species = pet.Species,
            Breed = pet.Breed,
            Age = pet.Age,
            Gender = pet.Gender,
            Weight = pet.Weight,
            Description = pet.Description
        };

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, PetViewModel model)
    {
        if (id != model.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            var userId = _userManager.GetUserId(User);
            var pet = await _context.Pets
                .FirstOrDefaultAsync(p => p.Id == id && p.UserId == userId);

            if (pet == null)
            {
                return NotFound();
            }

            pet.Name = model.Name;
            pet.Species = model.Species;
            pet.Breed = model.Breed;
            pet.Age = model.Age;
            pet.Gender = model.Gender;
            pet.Weight = model.Weight;
            pet.Description = model.Description;

            _context.Update(pet);
            await _context.SaveChangesAsync();
            
            TempData["Success"] = "Thông tin thú cưng đã được cập nhật thành công!";
            return RedirectToAction(nameof(Index));
        }

        return View(model);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var userId = _userManager.GetUserId(User);
        var pet = await _context.Pets
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == userId);

        if (pet == null)
        {
            return NotFound();
        }

        return View(pet);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var userId = _userManager.GetUserId(User);
        var pet = await _context.Pets
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == userId);

        if (pet != null)
        {
            _context.Pets.Remove(pet);
            await _context.SaveChangesAsync();
            TempData["Success"] = "Thông tin thú cưng đã được xóa thành công!";
        }

        return RedirectToAction(nameof(Index));
    }
}
