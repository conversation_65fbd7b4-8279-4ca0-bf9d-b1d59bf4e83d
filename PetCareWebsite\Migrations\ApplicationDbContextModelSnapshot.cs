﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PetCareWebsite.Data;

#nullable disable

namespace PetCareWebsite.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .Has<PERSON>ax<PERSON>ength(256)
                        .HasColumnType("TEXT");

                    b.<PERSON>("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("PetCareWebsite.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FullName")
                        .HasColumnType("TEXT");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("PetCareWebsite.Models.Appointment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AppointmentDate")
                        .HasColumnType("TEXT");

                    b.Property<TimeSpan>("AppointmentTime")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("PetId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ServiceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("TotalAmount")
                        .HasPrecision(10, 2)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("PetId");

                    b.HasIndex("ServiceId");

                    b.HasIndex("UserId");

                    b.ToTable("Appointments");
                });

            modelBuilder.Entity("PetCareWebsite.Models.Pet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("Age")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Breed")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Species")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Weight")
                        .HasPrecision(5, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Pets");
                });

            modelBuilder.Entity("PetCareWebsite.Models.Service", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("DurationMinutes")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasPrecision(10, 2)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Services");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(2978),
                            Description = "Dịch vụ tắm rửa cơ bản cho thú cưng với dầu gội chuyên dụng, làm sạch tai, cắt móng và sấy khô.",
                            DurationMinutes = 60,
                            ImageUrl = "/images/services/basic-bath.jpg",
                            IsActive = true,
                            Name = "Tắm rửa cơ bản",
                            Price = 150000m
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3081),
                            Description = "Dịch vụ cắt tỉa lông theo yêu cầu, tạo kiểu dáng đẹp cho thú cưng với các công cụ chuyên nghiệp.",
                            DurationMinutes = 90,
                            ImageUrl = "/images/services/grooming.jpg",
                            IsActive = true,
                            Name = "Cắt tỉa lông chuyên nghiệp",
                            Price = 200000m
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3083),
                            Description = "Kiểm tra sức khỏe tổng quát, đo nhiệt độ, kiểm tra tim mạch, hô hấp và tư vấn dinh dưỡng.",
                            DurationMinutes = 45,
                            ImageUrl = "/images/services/health-check.jpg",
                            IsActive = true,
                            Name = "Khám sức khỏe tổng quát",
                            Price = 300000m
                        },
                        new
                        {
                            Id = 4,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3085),
                            Description = "Tiêm các loại vaccine cần thiết để bảo vệ thú cưng khỏi các bệnh truyền nhiễm nguy hiểm.",
                            DurationMinutes = 30,
                            ImageUrl = "/images/services/vaccination.jpg",
                            IsActive = true,
                            Name = "Tiêm phòng định kỳ",
                            Price = 250000m
                        },
                        new
                        {
                            Id = 5,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3087),
                            Description = "Dịch vụ spa cao cấp bao gồm massage, aromatherapy và các liệu pháp thư giãn cho thú cưng.",
                            DurationMinutes = 120,
                            ImageUrl = "/images/services/spa.jpg",
                            IsActive = true,
                            Name = "Spa thư giãn cao cấp",
                            Price = 400000m
                        },
                        new
                        {
                            Id = 6,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3089),
                            Description = "Làm sạch răng, loại bỏ cao răng và kiểm tra sức khỏe răng miệng cho thú cưng.",
                            DurationMinutes = 45,
                            ImageUrl = "/images/services/dental-care.jpg",
                            IsActive = true,
                            Name = "Vệ sinh răng miệng",
                            Price = 180000m
                        },
                        new
                        {
                            Id = 7,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3091),
                            Description = "Cắt móng chân an toàn và chính xác, tránh làm tổn thương thú cưng.",
                            DurationMinutes = 20,
                            ImageUrl = "/images/services/nail-trimming.jpg",
                            IsActive = true,
                            Name = "Cắt móng chân chuyên nghiệp",
                            Price = 80000m
                        },
                        new
                        {
                            Id = 8,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3093),
                            Description = "Kiểm tra và điều trị ve rận, bọ chét và các ký sinh trùng ngoài da.",
                            DurationMinutes = 60,
                            ImageUrl = "/images/services/flea-treatment.jpg",
                            IsActive = true,
                            Name = "Điều trị ve rận",
                            Price = 220000m
                        },
                        new
                        {
                            Id = 9,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3094),
                            Description = "Tư vấn chế độ dinh dưỡng phù hợp với từng loại thú cưng và độ tuổi.",
                            DurationMinutes = 30,
                            ImageUrl = "/images/services/nutrition.jpg",
                            IsActive = true,
                            Name = "Tư vấn dinh dưỡng",
                            Price = 150000m
                        },
                        new
                        {
                            Id = 10,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3096),
                            Description = "Khám chuyên sâu các bệnh lý phức tạp với bác sĩ thú y chuyên khoa.",
                            DurationMinutes = 60,
                            ImageUrl = "/images/services/specialist.jpg",
                            IsActive = true,
                            Name = "Khám chuyên khoa",
                            Price = 500000m
                        },
                        new
                        {
                            Id = 11,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3098),
                            Description = "Dịch vụ chăm sóc và theo dõi thú cưng sau các ca phẫu thuật.",
                            DurationMinutes = 90,
                            ImageUrl = "/images/services/post-surgery.jpg",
                            IsActive = true,
                            Name = "Chăm sóc sau phẫu thuật",
                            Price = 350000m
                        },
                        new
                        {
                            Id = 12,
                            CreatedAt = new DateTime(2025, 7, 18, 0, 31, 38, 729, DateTimeKind.Local).AddTicks(3100),
                            Description = "Tắm với các loại thuốc chuyên dụng để điều trị các bệnh về da.",
                            DurationMinutes = 75,
                            ImageUrl = "/images/services/medicated-bath.jpg",
                            IsActive = true,
                            Name = "Tắm thuốc trị liệu",
                            Price = 280000m
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("PetCareWebsite.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("PetCareWebsite.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PetCareWebsite.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("PetCareWebsite.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PetCareWebsite.Models.Appointment", b =>
                {
                    b.HasOne("PetCareWebsite.Models.Pet", "Pet")
                        .WithMany("Appointments")
                        .HasForeignKey("PetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("PetCareWebsite.Models.Service", "Service")
                        .WithMany("Appointments")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("PetCareWebsite.Models.ApplicationUser", "User")
                        .WithMany("Appointments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Pet");

                    b.Navigation("Service");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PetCareWebsite.Models.Pet", b =>
                {
                    b.HasOne("PetCareWebsite.Models.ApplicationUser", "User")
                        .WithMany("Pets")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("PetCareWebsite.Models.ApplicationUser", b =>
                {
                    b.Navigation("Appointments");

                    b.Navigation("Pets");
                });

            modelBuilder.Entity("PetCareWebsite.Models.Pet", b =>
                {
                    b.Navigation("Appointments");
                });

            modelBuilder.Entity("PetCareWebsite.Models.Service", b =>
                {
                    b.Navigation("Appointments");
                });
#pragma warning restore 612, 618
        }
    }
}
