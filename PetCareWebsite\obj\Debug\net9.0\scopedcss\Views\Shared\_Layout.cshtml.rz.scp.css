/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-k4lbrv62nz] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-k4lbrv62nz] {
  color: #0077cc;
}

.btn-primary[b-k4lbrv62nz] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-k4lbrv62nz], .nav-pills .show > .nav-link[b-k4lbrv62nz] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-k4lbrv62nz] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-k4lbrv62nz] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-k4lbrv62nz] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-k4lbrv62nz] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-k4lbrv62nz] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
