# ĐỒ ÁN NGHIÊN CỨU: BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ

## THÔNG TIN ĐỒ ÁN
- **Đ<PERSON> tài**: <PERSON><PERSON><PERSON><PERSON> cứu bài toán luồng cực đại trên đồ thị có trọng số và cài đặt minh họa
- **Công cụ**: C++
- **Thu<PERSON>t toán chính**: Ford-Fulkerson với BFS (Edmonds-Karp)

---

## 1. TỔNG QUAN VỀ BÀI TOÁN LUỒNG CỰC ĐẠI

### 1.1. Định nghĩa bài toán
Bài toán luồng cực đại (Maximum Flow Problem) là một trong những bài toán cơ bản và quan trọng nhất trong lý thuyết đồ thị và tối ưu hóa mạng.

**Phát biểu bài toán:**
- Cho một mạng luồng G = (V, E) với:
  - V: tập đỉnh
  - E: tập cạnh có hướng
  - s: đỉnh nguồn (source)
  - t: đỉnh đích (sink)
  - c(u,v): k<PERSON><PERSON> năng thông qua (capacity) của cạnh (u,v)

- **<PERSON><PERSON><PERSON> tiêu**: Tìm luồng cực đại từ s đến t sao cho:
  1. Ràng buộc khả năng thông qua: 0 ≤ f(u,v) ≤ c(u,v)
  2. Bảo toàn luồng tại mọi đỉnh trung gian: Σf(u,v) = Σf(v,w)

### 1.2. Khái niệm cơ bản

#### Luồng (Flow)
Luồng f là một hàm f: V × V → R thỏa mãn:
- **Ràng buộc khả năng**: f(u,v) ≤ c(u,v) với mọi u,v ∈ V
- **Tính phản đối xứng**: f(u,v) = -f(v,u) với mọi u,v ∈ V  
- **Bảo toàn luồng**: Σf(u,v) = 0 với mọi u ∈ V\{s,t}

#### Mạng dư (Residual Network)
Mạng dư Gf = (V, Ef) được định nghĩa từ mạng gốc G và luồng f:
- **Khả năng dư**: cf(u,v) = c(u,v) - f(u,v)
- **Tập cạnh dư**: Ef = {(u,v) ∈ V × V : cf(u,v) > 0}

#### Đường tăng luồng (Augmenting Path)
Đường tăng luồng là một đường đi đơn từ s đến t trong mạng dư Gf.

---

## 2. ĐỊNH LÝ FORD-FULKERSON

### 2.1. Phát biểu định lý
**Định lý Ford-Fulkerson**: Trong một mạng luồng, ba điều kiện sau là tương đương:
1. f là luồng cực đại
2. Mạng dư Gf không chứa đường tăng luồng
3. |f| = c(S,T) với một lát cắt (S,T) nào đó

### 2.2. Khái niệm lát cắt (Cut)
- **Lát cắt (S,T)**: Phân hoạch tập đỉnh V thành hai tập S và T = V\S sao cho s ∈ S và t ∈ T
- **Khả năng lát cắt**: c(S,T) = Σ c(u,v) với u ∈ S, v ∈ T
- **Lát cắt tối thiểu**: Lát cắt có khả năng nhỏ nhất

### 2.3. Ý nghĩa định lý
Định lý chứng minh rằng giá trị luồng cực đại bằng khả năng lát cắt tối thiểu, tạo cơ sở lý thuyết cho các thuật toán tìm luồng cực đại.

---

## 3. THUẬT TOÁN FORD-FULKERSON

### 3.1. Ý tưởng chính
```
1. Khởi tạo luồng f = 0
2. Lặp:
   a. Tìm đường tăng luồng P từ s đến t trong Gf
   b. Nếu không tồn tại P thì dừng
   c. Tính luồng tăng thêm: Δ = min{cf(u,v) : (u,v) ∈ P}
   d. Cập nhật luồng: f = f + Δ
3. Trả về f
```

### 3.2. Thuật toán Edmonds-Karp
Edmonds-Karp là cài đặt cụ thể của Ford-Fulkerson sử dụng BFS để tìm đường tăng luồng ngắn nhất.

**Độ phức tạp**: O(VE²)

### 3.3. Pseudocode
```
function EdmondsKarp(G, s, t):
    maxFlow = 0
    while true:
        parent = BFS(G, s, t)  // Tìm đường tăng luồng
        if parent[t] == -1:    // Không tìm thấy đường
            break
        
        // Tính luồng tăng thêm
        pathFlow = ∞
        v = t
        while v != s:
            u = parent[v]
            pathFlow = min(pathFlow, capacity[u][v])
            v = u
        
        // Cập nhật mạng dư
        v = t
        while v != s:
            u = parent[v]
            capacity[u][v] -= pathFlow
            capacity[v][u] += pathFlow
            v = u
        
        maxFlow += pathFlow
    
    return maxFlow
```

---

## 4. ỨNG DỤNG THỰC TẾ

### 4.1. Mạng giao thông
- Tối ưu hóa lưu lượng giao thông
- Thiết kế hệ thống đường bộ
- Quản lý tắc nghẽn

### 4.2. Mạng viễn thông
- Định tuyến dữ liệu
- Phân bổ băng thông
- Thiết kế mạng

### 4.3. Chuỗi cung ứng
- Tối ưu hóa vận chuyển
- Quản lý kho bãi
- Phân phối hàng hóa

### 4.4. Các bài toán khác
- Bài toán ghép cặp (Bipartite Matching)
- Bài toán cắt tối thiểu (Min-Cut)
- Phân tích độ tin cậy mạng

---

## 5. PHÂN TÍCH THUẬT TOÁN

### 5.1. Tính đúng đắn
Thuật toán Ford-Fulkerson đảm bảo tìm được luồng cực đại dựa trên định lý Ford-Fulkerson.

### 5.2. Độ phức tạp
- **Ford-Fulkerson tổng quát**: O(E × |f*|) với |f*| là giá trị luồng cực đại
- **Edmonds-Karp**: O(VE²)
- **Dinic**: O(V²E)
- **Push-Relabel**: O(V³)

### 5.3. Ưu và nhược điểm

**Ưu điểm:**
- Đơn giản, dễ hiểu và cài đặt
- Tính đúng đắn được chứng minh
- Áp dụng được cho nhiều bài toán thực tế

**Nhược điểm:**
- Độ phức tạp có thể cao với dữ liệu lớn
- Hiệu suất phụ thuộc vào cách chọn đường tăng luồng

---

## 6. KẾT LUẬN

Bài toán luồng cực đại là một chủ đề quan trọng trong khoa học máy tính với nhiều ứng dụng thực tế. Thuật toán Ford-Fulkerson và các biến thể của nó cung cấp giải pháp hiệu quả cho bài toán này.

Đồ án này đã nghiên cứu:
- Lý thuyết cơ bản về luồng và mạng
- Định lý Ford-Fulkerson và ý nghĩa
- Cài đặt thuật toán Edmonds-Karp
- Ứng dụng trong thực tế

Chương trình minh họa được phát triển bằng C++ giúp người dùng hiểu rõ hơn về cách thức hoạt động của thuật toán.
