using Microsoft.EntityFrameworkCore;
using RealEstateWebsite.Models;

namespace RealEstateWebsite.Data
{
    public class RealEstateContext : DbContext
    {
        public RealEstateContext(DbContextOptions<RealEstateContext> options) : base(options)
        {
        }

        public DbSet<Property> Properties { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<PropertyImage> PropertyImages { get; set; }
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<Favorite> Favorites { get; set; }
        public DbSet<User> Users { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Property configuration
            modelBuilder.Entity<Property>(entity =>
            {
                entity.HasKey(e => e.PropertyId);
                entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Area).HasColumnType("decimal(10,2)");
                entity.Property(e => e.Latitude).HasColumnType("decimal(10,8)");
                entity.Property(e => e.Longitude).HasColumnType("decimal(11,8)");

                entity.HasOne(e => e.Category)
                      .WithMany(e => e.Properties)
                      .HasForeignKey(e => e.CategoryId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.Title);
                entity.HasIndex(e => e.City);
                entity.HasIndex(e => e.District);
                entity.HasIndex(e => e.Price);
                entity.HasIndex(e => e.Area);
                entity.HasIndex(e => e.CreatedDate);
            });

            // Category configuration
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasKey(e => e.CategoryId);
                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.Slug).IsUnique();
            });

            // PropertyImage configuration
            modelBuilder.Entity<PropertyImage>(entity =>
            {
                entity.HasKey(e => e.PropertyImageId);
                entity.HasOne(e => e.Property)
                      .WithMany(e => e.Images)
                      .HasForeignKey(e => e.PropertyId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Contact configuration
            modelBuilder.Entity<Contact>(entity =>
            {
                entity.HasKey(e => e.ContactId);
                entity.Property(e => e.BudgetMin).HasColumnType("decimal(18,2)");
                entity.Property(e => e.BudgetMax).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Property)
                      .WithMany(e => e.Contacts)
                      .HasForeignKey(e => e.PropertyId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.Email);
                entity.HasIndex(e => e.CreatedDate);
            });

            // Favorite configuration
            modelBuilder.Entity<Favorite>(entity =>
            {
                entity.HasKey(e => e.FavoriteId);
                entity.HasOne(e => e.Property)
                      .WithMany(e => e.Favorites)
                      .HasForeignKey(e => e.PropertyId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.SessionId);
                entity.HasIndex(e => new { e.PropertyId, e.SessionId }).IsUnique();
            });

            // User configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                entity.Property(e => e.PreferredBudgetMin).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PreferredBudgetMax).HasColumnType("decimal(18,2)");

                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Seed data
            // SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category
                {
                    CategoryId = 1,
                    Name = "Nhà Phố",
                    Description = "Nhà phố, nhà liền kề trong khu dân cư",
                    Icon = "fas fa-home",
                    Slug = "nha-pho",
                    IsActive = true,
                    DisplayOrder = 1,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Category
                {
                    CategoryId = 2,
                    Name = "Chung Cư",
                    Description = "Căn hộ chung cư, cao ốc",
                    Icon = "fas fa-building",
                    Slug = "chung-cu",
                    IsActive = true,
                    DisplayOrder = 2,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Category
                {
                    CategoryId = 3,
                    Name = "Biệt Thự",
                    Description = "Biệt thự, villa cao cấp",
                    Icon = "fas fa-crown",
                    Slug = "biet-thu",
                    IsActive = true,
                    DisplayOrder = 3,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Category
                {
                    CategoryId = 4,
                    Name = "Đất Nền",
                    Description = "Đất nền, lô đất xây dựng",
                    Icon = "fas fa-map",
                    Slug = "dat-nen",
                    IsActive = true,
                    DisplayOrder = 4,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new Category
                {
                    CategoryId = 5,
                    Name = "Văn Phòng",
                    Description = "Văn phòng, mặt bằng kinh doanh",
                    Icon = "fas fa-briefcase",
                    Slug = "van-phong",
                    IsActive = true,
                    DisplayOrder = 5,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );

            // Seed Users
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    FullName = "Quản Trị Viên",
                    Phone = "0123456789",
                    Role = UserRole.Admin,
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                },
                new User
                {
                    UserId = 2,
                    Username = "customer",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456"),
                    FullName = "Nguyễn Văn Khách",
                    Phone = "0987654321",
                    Role = UserRole.Customer,
                    IsActive = true,
                    EmailConfirmed = true,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );
        }
    }
}