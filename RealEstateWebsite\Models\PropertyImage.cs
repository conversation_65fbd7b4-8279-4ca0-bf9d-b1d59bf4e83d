using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models
{
    public class PropertyImage
    {
        public int PropertyImageId { get; set; }

        [Required]
        public int PropertyId { get; set; }

        [Required(ErrorMessage = "Đường dẫn hình ảnh là bắt buộc")]
        [StringLength(500, ErrorMessage = "Đường dẫn hình ảnh không được vượt quá 500 ký tự")]
        public string ImageUrl { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "Tiêu đề hình ảnh không được vượt quá 200 ký tự")]
        public string? Title { get; set; }

        [StringLength(500, ErrorMessage = "Mô tả hình ảnh không được vượt quá 500 ký tự")]
        public string? Description { get; set; }

        public bool IsMain { get; set; } = false;

        public int DisplayOrder { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation property
        public Property Property { get; set; } = null!;
    }
}
