using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PetCareWebsite.Models;

namespace PetCareWebsite.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Pet> Pets { get; set; }
    public DbSet<Service> Services { get; set; }
    public DbSet<Appointment> Appointments { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure relationships
        builder.Entity<Pet>()
            .HasOne(p => p.User)
            .WithMany(u => u.Pets)
            .HasForeignKey(p => p.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<Appointment>()
            .HasOne(a => a.User)
            .WithMany(u => u.Appointments)
            .HasForeignKey(a => a.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<Appointment>()
            .HasOne(a => a.Pet)
            .WithMany(p => p.Appointments)
            .HasForeignKey(a => a.PetId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<Appointment>()
            .HasOne(a => a.Service)
            .WithMany(s => s.Appointments)
            .HasForeignKey(a => a.ServiceId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure decimal precision
        builder.Entity<Pet>()
            .Property(p => p.Weight)
            .HasPrecision(5, 2);

        builder.Entity<Service>()
            .Property(s => s.Price)
            .HasPrecision(10, 2);

        builder.Entity<Appointment>()
            .Property(a => a.TotalAmount)
            .HasPrecision(10, 2);

        // Seed data for Services
        SeedServices(builder);
    }

    private void SeedServices(ModelBuilder builder)
    {
        builder.Entity<Service>().HasData(
            new Service
            {
                Id = 1,
                Name = "Tắm rửa cơ bản",
                Description = "Dịch vụ tắm rửa cơ bản cho thú cưng với dầu gội chuyên dụng, làm sạch tai, cắt móng và sấy khô.",
                Price = 150000,
                DurationMinutes = 60,
                ImageUrl = "/images/services/basic-bath.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 2,
                Name = "Cắt tỉa lông chuyên nghiệp",
                Description = "Dịch vụ cắt tỉa lông theo yêu cầu, tạo kiểu dáng đẹp cho thú cưng với các công cụ chuyên nghiệp.",
                Price = 200000,
                DurationMinutes = 90,
                ImageUrl = "/images/services/grooming.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 3,
                Name = "Khám sức khỏe tổng quát",
                Description = "Kiểm tra sức khỏe tổng quát, đo nhiệt độ, kiểm tra tim mạch, hô hấp và tư vấn dinh dưỡng.",
                Price = 300000,
                DurationMinutes = 45,
                ImageUrl = "/images/services/health-check.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 4,
                Name = "Tiêm phòng định kỳ",
                Description = "Tiêm các loại vaccine cần thiết để bảo vệ thú cưng khỏi các bệnh truyền nhiễm nguy hiểm.",
                Price = 250000,
                DurationMinutes = 30,
                ImageUrl = "/images/services/vaccination.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 5,
                Name = "Spa thư giãn cao cấp",
                Description = "Dịch vụ spa cao cấp bao gồm massage, aromatherapy và các liệu pháp thư giãn cho thú cưng.",
                Price = 400000,
                DurationMinutes = 120,
                ImageUrl = "/images/services/spa.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 6,
                Name = "Vệ sinh răng miệng",
                Description = "Làm sạch răng, loại bỏ cao răng và kiểm tra sức khỏe răng miệng cho thú cưng.",
                Price = 180000,
                DurationMinutes = 45,
                ImageUrl = "/images/services/dental-care.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 7,
                Name = "Cắt móng chân chuyên nghiệp",
                Description = "Cắt móng chân an toàn và chính xác, tránh làm tổn thương thú cưng.",
                Price = 80000,
                DurationMinutes = 20,
                ImageUrl = "/images/services/nail-trimming.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 8,
                Name = "Điều trị ve rận",
                Description = "Kiểm tra và điều trị ve rận, bọ chét và các ký sinh trùng ngoài da.",
                Price = 220000,
                DurationMinutes = 60,
                ImageUrl = "/images/services/flea-treatment.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 9,
                Name = "Tư vấn dinh dưỡng",
                Description = "Tư vấn chế độ dinh dưỡng phù hợp với từng loại thú cưng và độ tuổi.",
                Price = 150000,
                DurationMinutes = 30,
                ImageUrl = "/images/services/nutrition.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 10,
                Name = "Khám chuyên khoa",
                Description = "Khám chuyên sâu các bệnh lý phức tạp với bác sĩ thú y chuyên khoa.",
                Price = 500000,
                DurationMinutes = 60,
                ImageUrl = "/images/services/specialist.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 11,
                Name = "Chăm sóc sau phẫu thuật",
                Description = "Dịch vụ chăm sóc và theo dõi thú cưng sau các ca phẫu thuật.",
                Price = 350000,
                DurationMinutes = 90,
                ImageUrl = "/images/services/post-surgery.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            },
            new Service
            {
                Id = 12,
                Name = "Tắm thuốc trị liệu",
                Description = "Tắm với các loại thuốc chuyên dụng để điều trị các bệnh về da.",
                Price = 280000,
                DurationMinutes = 75,
                ImageUrl = "/images/services/medicated-bath.jpg",
                IsActive = true,
                CreatedAt = DateTime.Now
            }
        );
    }
}
