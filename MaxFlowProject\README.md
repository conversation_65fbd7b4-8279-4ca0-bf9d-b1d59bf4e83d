# ĐỒ ÁN NGHIÊN CỨU: BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ

## 📋 THÔNG TIN ĐỒ ÁN

- **Đề tài**: <PERSON><PERSON><PERSON><PERSON> cứu bài toán luồng cực đại trên đồ thị có trọng số và cài đặt minh họa
- **Thuật toán**: Ford-Fulkerson với BFS (Edmonds-Karp)
- **<PERSON>ôn ngữ lập trình**: C++
- **Đ<PERSON> phức tạp**: O(VE²)
- **Tác giả**: [Tên sinh viên]
- **Ngày hoàn thành**: [Ngày]

## 🎯 MỤC TIÊU ĐỒ ÁN

1. Nghiên cứu lý thuyết về bài toán luồng cực đại
2. Hiểu rõ định lý Ford-Fulkerson và ý nghĩa
3. <PERSON><PERSON><PERSON> đặt thuật toán Edmonds-Karp
4. Xây dựng chương trình minh họa với giao diện thân thiện
5. <PERSON><PERSON>m tra tính đúng đắn qua test cases
6. Ứng dụng vào các bài toán thực tế

## 🚀 TÍNH NĂNG CHÍNH

- ✅ **Tìm luồng cực đại**: Sử dụng thuật toán Ford-Fulkerson
- ✅ **Hiển thị từng bước**: Theo dõi quá trình thực hiện thuật toán
- ✅ **Tìm lát cắt tối thiểu**: Xác định điểm nghẽn trong mạng
- ✅ **Đồ thị mẫu**: 3 đồ thị mẫu với độ phức tạp khác nhau
- ✅ **Giao diện menu**: Menu tương tác thân thiện
- ✅ **Test cases**: Kiểm tra tính đúng đắn của thuật toán
- ✅ **Báo cáo chi tiết**: Tài liệu lý thuyết đầy đủ

## 📁 CẤU TRÚC DỰ ÁN

```
MaxFlowProject/
├── main.cpp              # Chương trình chính
├── Graph.h               # Header file cho lớp Graph
├── Graph.cpp             # Cài đặt lớp Graph và thuật toán
├── GraphUtils.cpp        # Các hàm tiện ích và đồ thị mẫu
├── TestCases.cpp         # Test cases và ví dụ minh họa
├── Makefile              # File biên dịch
├── README.md             # Hướng dẫn sử dụng (file này)
├── BaoCaoLyThuyet.md     # Báo cáo lý thuyết chi tiết
├── bin/                  # Thư mục chứa file thực thi
└── obj/                  # Thư mục chứa file object
```

## 🛠️ YÊU CẦU HỆ THỐNG

- **Compiler**: g++ hỗ trợ C++11 trở lên
- **Hệ điều hành**: Windows, Linux, macOS
- **RAM**: Tối thiểu 512MB
- **Dung lượng**: ~50MB

### Cài đặt trên Windows
```bash
# Sử dụng MinGW hoặc Visual Studio
# Tải MinGW từ: https://www.mingw-w64.org/
```

### Cài đặt trên Linux/Ubuntu
```bash
sudo apt update
sudo apt install g++ make
```

### Cài đặt trên macOS
```bash
# Cài đặt Xcode Command Line Tools
xcode-select --install
```

## 🔧 HƯỚNG DẪN BIÊN DỊCH

### Biên dịch nhanh
```bash
# Biên dịch chương trình chính
make

# Biên dịch và chạy
make && make run
```

### Các lệnh Makefile chi tiết
```bash
make              # Biên dịch chương trình chính
make debug        # Biên dịch với thông tin debug
make test         # Biên dịch chương trình test
make run          # Chạy chương trình chính
make run-debug    # Chạy chương trình debug
make run-test     # Chạy test cases
make clean        # Xóa file object và executable
make distclean    # Xóa hoàn toàn thư mục build
make package      # Tạo file zip để nộp bài
make help         # Hiển thị trợ giúp
```

### Biên dịch thủ công (nếu không có make)
```bash
# Windows
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram.exe

# Linux/macOS
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram
```

## 📖 HƯỚNG DẪN SỬ DỤNG

### 1. Chạy chương trình
```bash
# Sau khi biên dịch
./bin/MaxFlowProgram        # Linux/macOS
bin\MaxFlowProgram.exe      # Windows
```

### 2. Menu chính
- **Tùy chọn 1**: Tạo đồ thị mới
- **Tùy chọn 2**: Sử dụng đồ thị mẫu
- **Tùy chọn 3**: Hướng dẫn sử dụng
- **Tùy chọn 4**: Thông tin thuật toán

### 3. Nhập đồ thị
```
Nhập số đỉnh: 4
Nhập số cạnh: 5
Cạnh 1: 0 1 16    # Từ đỉnh 0 đến đỉnh 1 với khả năng 16
Cạnh 2: 0 2 13    # Từ đỉnh 0 đến đỉnh 2 với khả năng 13
...
```

### 4. Tìm luồng cực đại
```
Nhập đỉnh nguồn: 0
Nhập đỉnh đích: 3
```

## 🧪 TEST CASES

Chương trình bao gồm 5 test cases chính:

1. **Test Case 1**: Đồ thị đơn giản (4 đỉnh)
2. **Test Case 2**: Đồ thị phức tạp (6 đỉnh)
3. **Test Case 3**: Đồ thị tuyến tính
4. **Test Case 4**: Không có đường đi
5. **Test Case 5**: Đồ thị có chu trình

### Chạy test cases
```bash
make test && make run-test
```

## 📊 ĐỒ THỊ MẪU

### Đồ thị mẫu 1 (Đơn giản - 4 đỉnh)
```
0 -> 1: 16    0 -> 2: 13
1 -> 2: 10    1 -> 3: 12
2 -> 1: 4     2 -> 3: 14
```
**Luồng cực đại từ 0 đến 3**: 23

### Đồ thị mẫu 2 (Phức tạp - 6 đỉnh)
```
0 -> 1: 10    0 -> 2: 8
1 -> 2: 5     1 -> 3: 5
2 -> 4: 10    3 -> 2: 7
3 -> 4: 8     3 -> 5: 10
4 -> 5: 10
```
**Luồng cực đại từ 0 đến 5**: 15

### Đồ thị mẫu 3 (Mạng giao thông - 8 đỉnh)
Mô phỏng mạng giao thông thành phố với 8 nút giao thông.

## 🔍 THUẬT TOÁN

### Ford-Fulkerson (Edmonds-Karp)
1. **Khởi tạo**: Luồng = 0
2. **Lặp**: Tìm đường tăng luồng bằng BFS
3. **Cập nhật**: Tăng luồng theo đường tìm được
4. **Dừng**: Khi không còn đường tăng luồng

### Độ phức tạp
- **Thời gian**: O(VE²)
- **Không gian**: O(V²)

## 🎓 ỨNG DỤNG THỰC TẾ

1. **Mạng giao thông**: Tối ưu lưu lượng xe
2. **Mạng viễn thông**: Phân bổ băng thông
3. **Chuỗi cung ứng**: Tối ưu vận chuyển
4. **Bài toán ghép cặp**: Matching trong đồ thị
5. **Phân tích mạng**: Tìm điểm nghẽn

## 🐛 XỬ LÝ LỖI

### Lỗi biên dịch
```bash
# Kiểm tra compiler
g++ --version

# Kiểm tra dependencies
make check-deps
```

### Lỗi runtime
- Kiểm tra định dạng input
- Đảm bảo đỉnh nguồn ≠ đỉnh đích
- Kiểm tra chỉ số đỉnh hợp lệ (0 ≤ đỉnh < số_đỉnh)

## 📚 TÀI LIỆU THAM KHẢO

1. **Introduction to Algorithms** - Cormen, Leiserson, Rivest, Stein
2. **Algorithm Design** - Jon Kleinberg, Éva Tardos
3. **Network Flows** - Ahuja, Magnanti, Orlin
4. **Báo cáo lý thuyết**: `BaoCaoLyThuyet.md`

## 📞 LIÊN HỆ

- **Email**: [email sinh viên]
- **GitHub**: [link repository nếu có]
- **Lớp**: [tên lớp]
- **Trường**: [tên trường]

## 📄 GIẤY PHÉP

Đồ án này được phát triển cho mục đích học tập và nghiên cứu.

---

**Cảm ơn bạn đã sử dụng chương trình! 🙏**
