#include "Graph.h"
#include <algorithm>
#include <string>
#include <clocale>

// Constructor
Graph::Graph(int V) : vertices(V) {
    capacity.resize(V, std::vector<int>(V, 0));
    adjMatrix.resize(V, std::vector<int>(V, 0));
}

// Destructor
Graph::~Graph() {
    // Vector tự động giải phóng bộ nhớ
}

// Thêm cạnh vào đồ thị
void Graph::addEdge(int u, int v, int cap) {
    if (u >= 0 && u < vertices && v >= 0 && v < vertices && cap > 0) {
        capacity[u][v] = cap;
        adjMatrix[u][v] = cap; // Lưu ma trận gốc để hiển thị
    }
}

// Thuật toán BFS để tìm đường tăng luồng
bool Graph::bfs(int source, int sink, std::vector<int>& parent) {
    std::vector<bool> visited(vertices, false);
    std::queue<int> q;
    
    q.push(source);
    visited[source] = true;
    parent[source] = -1;
    
    while (!q.empty()) {
        int u = q.front();
        q.pop();
        
        for (int v = 0; v < vertices; v++) {
            if (!visited[v] && capacity[u][v] > 0) {
                if (v == sink) {
                    parent[v] = u;
                    return true;
                }
                q.push(v);
                parent[v] = u;
                visited[v] = true;
            }
        }
    }
    
    return false;
}

// Thuật toán Ford-Fulkerson (Edmonds-Karp)
int Graph::fordFulkerson(int source, int sink) {
    if (source == sink) {
        std::cout << "Nguồn và đích không thể giống nhau!\n";
        return 0;
    }
    
    if (source < 0 || source >= vertices || sink < 0 || sink >= vertices) {
        std::cout << "Đỉnh nguồn hoặc đích không hợp lệ!\n";
        return 0;
    }
    
    // Tạo bản sao ma trận khả năng để không làm thay đổi đồ thị gốc
    std::vector<std::vector<int>> rGraph = capacity;
    std::vector<int> parent(vertices);
    int maxFlowValue = 0;
    int iteration = 0;
    
    std::cout << "\n=== BẮT ĐẦU THUẬT TOÁN FORD-FULKERSON ===\n";
    std::cout << "Nguồn: " << source << ", Đích: " << sink << "\n\n";
    
    // Tạm thời thay đổi capacity để sử dụng trong BFS
    capacity = rGraph;
    
    while (bfs(source, sink, parent)) {
        iteration++;
        std::cout << "--- Lần lặp " << iteration << " ---\n";
        
        // Tìm luồng tối thiểu trên đường tăng luồng
        int pathFlow = INT_MAX;
        for (int v = sink; v != source; v = parent[v]) {
            int u = parent[v];
            pathFlow = std::min(pathFlow, capacity[u][v]);
        }
        
        std::cout << "Đường tăng luồng tìm được: ";
        displayPath(parent, source, sink);
        std::cout << "Luồng tăng thêm: " << pathFlow << "\n";
        
        // Cập nhật khả năng dư
        for (int v = sink; v != source; v = parent[v]) {
            int u = parent[v];
            capacity[u][v] -= pathFlow;
            capacity[v][u] += pathFlow;
        }
        
        maxFlowValue += pathFlow;
        std::cout << "Tổng luồng hiện tại: " << maxFlowValue << "\n\n";
    }
    
    // Khôi phục ma trận capacity gốc
    capacity = adjMatrix;
    
    std::cout << "=== KẾT THÚC THUẬT TOÁN ===\n";
    std::cout << "Luồng cực đại từ " << source << " đến " << sink << ": " << maxFlowValue << "\n\n";
    
    return maxFlowValue;
}

// Hiển thị ma trận khả năng thông qua
void Graph::displayCapacityMatrix() {
    std::cout << "\n=== MA TRẬN KHẢ NĂNG THÔNG QUA ===\n";
    std::cout << "    ";
    for (int i = 0; i < vertices; i++) {
        std::cout << std::setw(4) << i;
    }
    std::cout << "\n";
    
    for (int i = 0; i < vertices; i++) {
        std::cout << std::setw(2) << i << ": ";
        for (int j = 0; j < vertices; j++) {
            std::cout << std::setw(4) << capacity[i][j];
        }
        std::cout << "\n";
    }
    std::cout << "\n";
}

// Hiển thị ma trận kề gốc
void Graph::displayAdjacencyMatrix() {
    std::cout << "\n=== MA TRẬN KỀ GỐC ===\n";
    std::cout << "    ";
    for (int i = 0; i < vertices; i++) {
        std::cout << std::setw(4) << i;
    }
    std::cout << "\n";
    
    for (int i = 0; i < vertices; i++) {
        std::cout << std::setw(2) << i << ": ";
        for (int j = 0; j < vertices; j++) {
            std::cout << std::setw(4) << adjMatrix[i][j];
        }
        std::cout << "\n";
    }
    std::cout << "\n";
}

// Hiển thị đường đi từ source đến sink
void Graph::displayPath(const std::vector<int>& parent, int source, int sink) {
    std::vector<int> path;
    int current = sink;
    
    while (current != -1) {
        path.push_back(current);
        current = parent[current];
    }
    
    std::reverse(path.begin(), path.end());
    
    for (size_t i = 0; i < path.size(); i++) {
        std::cout << path[i];
        if (i < path.size() - 1) {
            std::cout << " -> ";
        }
    }
    std::cout << "\n";
}

// Tìm và hiển thị lát cắt tối thiểu
void Graph::findMinCut(int source, int sink) {
    // Chạy Ford-Fulkerson để tìm luồng cực đại
    std::vector<std::vector<int>> rGraph = capacity;
    std::vector<int> parent(vertices);
    
    // Tạm thời thay đổi capacity
    capacity = rGraph;
    
    while (bfs(source, sink, parent)) {
        int pathFlow = INT_MAX;
        for (int v = sink; v != source; v = parent[v]) {
            int u = parent[v];
            pathFlow = std::min(pathFlow, capacity[u][v]);
        }
        
        for (int v = sink; v != source; v = parent[v]) {
            int u = parent[v];
            capacity[u][v] -= pathFlow;
            capacity[v][u] += pathFlow;
        }
    }
    
    // Tìm tất cả đỉnh có thể đến được từ source trong mạng dư
    std::vector<bool> visited(vertices, false);
    std::queue<int> q;
    q.push(source);
    visited[source] = true;
    
    while (!q.empty()) {
        int u = q.front();
        q.pop();
        
        for (int v = 0; v < vertices; v++) {
            if (!visited[v] && capacity[u][v] > 0) {
                visited[v] = true;
                q.push(v);
            }
        }
    }
    
    // Khôi phục ma trận capacity gốc
    capacity = adjMatrix;
    
    std::cout << "\n=== LÁT CẮT TỐI THIỂU ===\n";
    std::cout << "Tập S (chứa nguồn): ";
    for (int i = 0; i < vertices; i++) {
        if (visited[i]) {
            std::cout << i << " ";
        }
    }
    std::cout << "\nTập T (chứa đích): ";
    for (int i = 0; i < vertices; i++) {
        if (!visited[i]) {
            std::cout << i << " ";
        }
    }
    
    std::cout << "\nCác cạnh trong lát cắt:\n";
    int cutCapacity = 0;
    for (int i = 0; i < vertices; i++) {
        for (int j = 0; j < vertices; j++) {
            if (visited[i] && !visited[j] && adjMatrix[i][j] > 0) {
                std::cout << "(" << i << "," << j << ") với khả năng " << adjMatrix[i][j] << "\n";
                cutCapacity += adjMatrix[i][j];
            }
        }
    }
    std::cout << "Tổng khả năng lát cắt: " << cutCapacity << "\n\n";
}

// Kiểm tra tính hợp lệ của đồ thị
bool Graph::isValidGraph() {
    for (int i = 0; i < vertices; i++) {
        for (int j = 0; j < vertices; j++) {
            if (capacity[i][j] < 0) {
                return false;
            }
        }
    }
    return true;
}

// Lấy số đỉnh
int Graph::getVertices() const {
    return vertices;
}

// Lấy khả năng thông qua của cạnh
int Graph::getCapacity(int u, int v) const {
    if (u >= 0 && u < vertices && v >= 0 && v < vertices) {
        return capacity[u][v];
    }
    return 0;
}

// Đặt lại đồ thị
void Graph::reset() {
    for (int i = 0; i < vertices; i++) {
        for (int j = 0; j < vertices; j++) {
            capacity[i][j] = 0;
            adjMatrix[i][j] = 0;
        }
    }
}

// Nhập đồ thị từ người dùng
void Graph::inputGraph() {
    std::cout << "\n=== NHẬP ĐỒ THỊ ===\n";
    std::cout << "Đồ thị có " << vertices << " đỉnh (đánh số từ 0 đến " << (vertices-1) << ")\n";

    int edges;
    std::cout << "Nhập số cạnh: ";
    std::cin >> edges;

    std::cout << "Nhập thông tin các cạnh (u v capacity):\n";
    for (int i = 0; i < edges; i++) {
        int u, v, cap;
        std::cout << "Cạnh " << (i+1) << ": ";
        std::cin >> u >> v >> cap;

        if (u >= 0 && u < vertices && v >= 0 && v < vertices && cap > 0) {
            addEdge(u, v, cap);
            std::cout << "Đã thêm cạnh " << u << " -> " << v << " với khả năng " << cap << "\n";
        } else {
            std::cout << "Thông tin cạnh không hợp lệ! Vui lòng nhập lại.\n";
            i--; // Nhập lại cạnh này
        }
    }

    std::cout << "Hoàn thành nhập đồ thị!\n";
}

// Hiển thị menu và xử lý lựa chọn
void Graph::showMenu() {
    int choice;
    do {
        std::cout << "\n=== MENU CHÍNH ===\n";
        std::cout << "1. Hiển thị ma trận kề\n";
        std::cout << "2. Tìm luồng cực đại\n";
        std::cout << "3. Tìm lát cắt tối thiểu\n";
        std::cout << "4. Nhập đồ thị mới\n";
        std::cout << "5. Chạy demo với đồ thị mẫu\n";
        std::cout << "6. Hiển thị thông tin thuật toán\n";
        std::cout << "0. Thoát\n";
        std::cout << "Lựa chọn: ";
        std::cin >> choice;

        switch (choice) {
            case 1:
                displayAdjacencyMatrix();
                break;
            case 2: {
                int source, sink;
                std::cout << "Nhập đỉnh nguồn: ";
                std::cin >> source;
                std::cout << "Nhập đỉnh đích: ";
                std::cin >> sink;
                fordFulkerson(source, sink);
                break;
            }
            case 3: {
                int source, sink;
                std::cout << "Nhập đỉnh nguồn: ";
                std::cin >> source;
                std::cout << "Nhập đỉnh đích: ";
                std::cin >> sink;
                findMinCut(source, sink);
                break;
            }
            case 4:
                reset();
                inputGraph();
                break;
            case 5:
                runDemo();
                break;
            case 6:
                GraphUtils::displayAlgorithmInfo();
                break;
            case 0:
                std::cout << "Cảm ơn bạn đã sử dụng chương trình!\n";
                break;
            default:
                std::cout << "Lựa chọn không hợp lệ!\n";
        }
    } while (choice != 0);
}

// Chạy demo với đồ thị mẫu
void Graph::runDemo() {
    std::cout << "\n=== DEMO VỚI ĐỒ THỊ MẪU ===\n";
    std::cout << "1. Đồ thị mẫu 1 (đơn giản - 4 đỉnh)\n";
    std::cout << "2. Đồ thị mẫu 2 (phức tạp - 6 đỉnh)\n";
    std::cout << "3. Đồ thị mẫu 3 (mạng giao thông - 8 đỉnh)\n";
    std::cout << "Chọn đồ thị mẫu: ";

    int choice;
    std::cin >> choice;

    Graph sampleGraph(1);
    switch (choice) {
        case 1:
            sampleGraph = GraphUtils::createSampleGraph1();
            break;
        case 2:
            sampleGraph = GraphUtils::createSampleGraph2();
            break;
        case 3:
            sampleGraph = GraphUtils::createSampleGraph3();
            break;
        default:
            std::cout << "Lựa chọn không hợp lệ!\n";
            return;
    }

    sampleGraph.displayAdjacencyMatrix();

    std::cout << "Nhập đỉnh nguồn: ";
    int source, sink;
    std::cin >> source;
    std::cout << "Nhập đỉnh đích: ";
    std::cin >> sink;

    int maxFlow = sampleGraph.fordFulkerson(source, sink);
    sampleGraph.findMinCut(source, sink);
}
