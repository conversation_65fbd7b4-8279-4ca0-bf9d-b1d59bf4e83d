@model RealEstateWebsite.Models.ViewModels.UserRegisterViewModel

@{
    ViewData["Title"] = "Đăng Ký";
}

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card shadow border-0">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus"></i> Đăng Ký Tài Khoản
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Register" method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Username" class="form-label">
                                    <i class="fas fa-user"></i> Tên đăng nhập *
                                </label>
                                <input asp-for="Username" class="form-control" 
                                       placeholder="Nhập tên đăng nhập" autofocus>
                                <span asp-validation-for="Username" class="text-danger"></span>
                                <small class="form-text text-muted">Chỉ chứa chữ cái, số và dấu gạch dưới</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">
                                    <i class="fas fa-envelope"></i> Email *
                                </label>
                                <input asp-for="Email" type="email" class="form-control" 
                                       placeholder="Nhập email">
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FullName" class="form-label">
                                <i class="fas fa-id-card"></i> Họ và tên *
                            </label>
                            <input asp-for="FullName" class="form-control" 
                                   placeholder="Nhập họ và tên đầy đủ">
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Phone" class="form-label">
                                <i class="fas fa-phone"></i> Số điện thoại *
                            </label>
                            <input asp-for="Phone" class="form-control" 
                                   placeholder="Nhập số điện thoại">
                            <span asp-validation-for="Phone" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label">
                                    <i class="fas fa-lock"></i> Mật khẩu *
                                </label>
                                <div class="input-group">
                                    <input asp-for="Password" type="password" class="form-control" 
                                           placeholder="Nhập mật khẩu" id="passwordInput">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('passwordInput', 'passwordToggleIcon')">
                                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Password" class="text-danger"></span>
                                <small class="form-text text-muted">Tối thiểu 6 ký tự</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label">
                                    <i class="fas fa-lock"></i> Xác nhận mật khẩu *
                                </label>
                                <div class="input-group">
                                    <input asp-for="ConfirmPassword" type="password" class="form-control" 
                                           placeholder="Nhập lại mật khẩu" id="confirmPasswordInput">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirmPasswordInput', 'confirmPasswordToggleIcon')">
                                        <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="AgreeToTerms" class="form-check-input" type="checkbox">
                                <label asp-for="AgreeToTerms" class="form-check-label">
                                    Tôi đồng ý với 
                                    <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#termsModal">
                                        Điều khoản sử dụng
                                    </a> 
                                    và 
                                    <a href="#" class="text-primary">Chính sách bảo mật</a>
                                </label>
                                <span asp-validation-for="AgreeToTerms" class="text-danger d-block"></span>
                            </div>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-user-plus"></i> Đăng Ký
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" class="text-success text-decoration-none fw-bold">
                            Đăng nhập ngay
                        </a>
                    </p>
                </div>
            </div>

            <!-- Benefits Card -->
            <div class="card mt-3 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-gift"></i> Lợi Ích Khi Đăng Ký</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>Lưu danh sách yêu thích</li>
                                <li><i class="fas fa-check text-success me-2"></i>Nhận thông báo BDS mới</li>
                                <li><i class="fas fa-check text-success me-2"></i>Tư vấn miễn phí</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>Quản lý thông tin cá nhân</li>
                                <li><i class="fas fa-check text-success me-2"></i>Lịch sử tìm kiếm</li>
                                <li><i class="fas fa-check text-success me-2"></i>Ưu đãi đặc biệt</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Điều Khoản Sử Dụng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Chấp nhận điều khoản</h6>
                <p>Bằng việc sử dụng website này, bạn đồng ý tuân thủ các điều khoản và điều kiện sử dụng.</p>
                
                <h6>2. Sử dụng dịch vụ</h6>
                <p>Bạn cam kết sử dụng dịch vụ một cách hợp pháp và không vi phạm quyền lợi của bên thứ ba.</p>
                
                <h6>3. Thông tin cá nhân</h6>
                <p>Chúng tôi cam kết bảo mật thông tin cá nhân của bạn theo chính sách bảo mật.</p>
                
                <h6>4. Trách nhiệm người dùng</h6>
                <p>Người dùng chịu trách nhiệm về tính chính xác của thông tin cung cấp.</p>
                
                <h6>5. Thay đổi điều khoản</h6>
                <p>Chúng tôi có quyền thay đổi điều khoản này bất kỳ lúc nào mà không cần thông báo trước.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="acceptTerms()">Đồng Ý</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        function togglePassword(inputId, iconId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(iconId);
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        function acceptTerms() {
            document.getElementById('AgreeToTerms').checked = true;
            const modal = bootstrap.Modal.getInstance(document.getElementById('termsModal'));
            modal.hide();
        }

        // Real-time password confirmation validation
        document.addEventListener('DOMContentLoaded', function() {
            const password = document.getElementById('passwordInput');
            const confirmPassword = document.getElementById('confirmPasswordInput');
            
            function validatePasswordMatch() {
                if (confirmPassword.value && password.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Mật khẩu xác nhận không khớp');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
            
            password.addEventListener('input', validatePasswordMatch);
            confirmPassword.addEventListener('input', validatePasswordMatch);
        });
    </script>
}
