#ifndef GRAPH_H
#define GRAPH_H

#include <vector>
#include <queue>
#include <climits>
#include <iostream>
#include <iomanip>

class Graph {
private:
    int vertices;                           // Số đỉnh
    std::vector<std::vector<int>> capacity; // Ma trận khả năng thông qua
    std::vector<std::vector<int>> adjMatrix; // Ma trận kề gốc (để hiển thị)
    
public:
    // Constructor
    Graph(int V);
    
    // Destructor
    ~Graph();
    
    // Thêm cạnh vào đồ thị
    void addEdge(int u, int v, int cap);
    
    // Thuật toán BFS để tìm đường tăng luồng
    bool bfs(int source, int sink, std::vector<int>& parent);
    
    // Thuật to<PERSON>-<PERSON> (Edmond<PERSON>-Karp)
    int fordFulkerson(int source, int sink);
    
    // Hiển thị ma trận khả năng thông qua
    void displayCapacityMatrix();
    
    // Hiển thị ma trận kề gốc
    void displayAdjacencyMatrix();
    
    // Hiển thị đường đi từ source đến sink
    void displayPath(const std::vector<int>& parent, int source, int sink);
    
    // Tìm và hiển thị lát cắt tối thiểu
    void findMinCut(int source, int sink);
    
    // Kiểm tra tính hợp lệ của đồ thị
    bool isValidGraph();
    
    // Lấy số đỉnh
    int getVertices() const;
    
    // Lấy khả năng thông qua của cạnh
    int getCapacity(int u, int v) const;
    
    // Đặt lại đồ thị
    void reset();
    
    // Nhập đồ thị từ người dùng
    void inputGraph();
    
    // Hiển thị menu và xử lý lựa chọn
    void showMenu();
    
    // Chạy demo với đồ thị mẫu
    void runDemo();
};

// Hàm tiện ích
namespace GraphUtils {
    // Tạo đồ thị mẫu 1 (đơn giản)
    Graph createSampleGraph1();
    
    // Tạo đồ thị mẫu 2 (phức tạp hơn)
    Graph createSampleGraph2();
    
    // Tạo đồ thị mẫu 3 (mạng giao thông)
    Graph createSampleGraph3();
    
    // Hiển thị thông tin về thuật toán
    void displayAlgorithmInfo();
    
    // Hiển thị hướng dẫn sử dụng
    void displayUsageGuide();
}

#endif // GRAPH_H
