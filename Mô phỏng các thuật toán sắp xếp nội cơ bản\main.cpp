#include <iostream>
#include <vector>
#include <string>
#include <iomanip>
#include <limits>
#include "Student.h"
#include "SortingAlgorithms.h"

class StudentManager {
private:
    std::vector<Student> students;
    
public:
    void displayMenu() {
        std::cout << "\n=== HE THONG QUAN LY SINH VIEN ===" << std::endl;
        std::cout << "1. Them sinh vien" << std::endl;
        std::cout << "2. Hien thi danh sach sinh vien" << std::endl;
        std::cout << "3. Sap xep danh sach" << std::endl;
        std::cout << "4. Tim kiem sinh vien" << std::endl;
        std::cout << "5. So sanh hieu suat thuat toan" << std::endl;
        std::cout << "6. Tao du lieu mau" << std::endl;
        std::cout << "7. Xoa tat ca du lieu" << std::endl;
        std::cout << "0. Thoat" << std::endl;
        std::cout << "=================================" << std::endl;
        std::cout << "Lua chon cua ban: ";
    }
    
    void addStudent() {
        std::cout << "\n=== THEM SINH VIEN MOI ===" << std::endl;
        
        std::string id, name;
        double score;
        int year;
        
        std::cout << "Nhap ma sinh vien: ";
        std::cin.ignore();
        std::getline(std::cin, id);
        
        std::cout << "Nhap ho va ten: ";
        std::getline(std::cin, name);
        
        std::cout << "Nhap diem trung binh (0-10): ";
        std::cin >> score;
        
        std::cout << "Nhap nam sinh: ";
        std::cin >> year;
        
        Student newStudent(id, name, score, year);
        students.push_back(newStudent);
        
        std::cout << "Da them sinh vien thanh cong!" << std::endl;
    }
    
    void displayStudents() {
        if (students.empty()) {
            std::cout << "\nDanh sach sinh vien trong!" << std::endl;
            return;
        }
        
        std::cout << "\n=== DANH SACH SINH VIEN ===" << std::endl;
        Student().displayHeader();
        
        for (size_t i = 0; i < students.size(); ++i) {
            std::cout << std::setw(3) << (i + 1) << ". ";
            students[i].display();
        }
        std::cout << "Tong so sinh vien: " << students.size() << std::endl;
    }
    
    void sortMenu() {
        if (students.empty()) {
            std::cout << "\nDanh sach sinh vien trong!" << std::endl;
            return;
        }
        
        std::cout << "\n=== SAP XEP DANH SACH ===" << std::endl;
        std::cout << "1. Sap xep theo ma sinh vien" << std::endl;
        std::cout << "2. Sap xep theo ten" << std::endl;
        std::cout << "3. Sap xep theo diem trung binh" << std::endl;
        std::cout << "4. Sap xep theo nam sinh" << std::endl;
        std::cout << "0. Quay lai" << std::endl;
        std::cout << "Chon tieu chi sap xep: ";
        
        int criteria;
        std::cin >> criteria;
        
        if (criteria == 0) return;
        if (criteria < 1 || criteria > 4) {
            std::cout << "Lua chon khong hop le!" << std::endl;
            return;
        }
        
        algorithmMenu(criteria);
    }
    
    void algorithmMenu(int criteria) {
        std::cout << "\n=== CHON THUAT TOAN SAP XEP ===" << std::endl;
        std::cout << "1. Bubble Sort" << std::endl;
        std::cout << "2. Selection Sort" << std::endl;
        std::cout << "3. Insertion Sort" << std::endl;
        std::cout << "4. Quick Sort" << std::endl;
        std::cout << "5. Merge Sort" << std::endl;
        std::cout << "0. Quay lai" << std::endl;
        std::cout << "Chon thuat toan: ";
        
        int algorithm;
        std::cin >> algorithm;
        
        if (algorithm == 0) return;
        if (algorithm < 1 || algorithm > 5) {
            std::cout << "Lua chon khong hop le!" << std::endl;
            return;
        }
        
        performSort(criteria, algorithm);
    }
    
    void performSort(int criteria, int algorithm) {
        auto students_copy = students;
        SortingStats stats;
        
        std::function<bool(const Student&, const Student&)> compareFn;
        
        switch (criteria) {
            case 1: compareFn = compareById; break;
            case 2: compareFn = compareByName; break;
            case 3: compareFn = compareByScore; break;
            case 4: compareFn = compareByYear; break;
        }
        
        std::cout << "\nDang sap xep..." << std::endl;
        
        switch (algorithm) {
            case 1:
                stats = SortingAlgorithms::bubbleSort(students_copy, compareFn);
                break;
            case 2:
                stats = SortingAlgorithms::selectionSort(students_copy, compareFn);
                break;
            case 3:
                stats = SortingAlgorithms::insertionSort(students_copy, compareFn);
                break;
            case 4:
                stats = SortingAlgorithms::quickSort(students_copy, compareFn);
                break;
            case 5:
                stats = SortingAlgorithms::mergeSort(students_copy, compareFn);
                break;
        }
        
        students = students_copy;
        
        std::cout << "Sap xep hoan thanh!" << std::endl;
        SortingAlgorithms::displayStats(stats);
        
        std::cout << "\nBan co muon xem danh sach da sap xep? (y/n): ";
        char choice;
        std::cin >> choice;
        if (choice == 'y' || choice == 'Y') {
            displayStudents();
        }
    }
    
    void searchStudent() {
        if (students.empty()) {
            std::cout << "\nDanh sach sinh vien trong!" << std::endl;
            return;
        }
        
        std::cout << "\n=== TIM KIEM SINH VIEN ===" << std::endl;
        std::cout << "Nhap tu khoa tim kiem (ma SV hoac ten): ";
        
        std::string keyword;
        std::cin.ignore();
        std::getline(std::cin, keyword);
        
        std::vector<Student> results;
        
        for (const auto& student : students) {
            if (student.getStudentId().find(keyword) != std::string::npos ||
                student.getFullName().find(keyword) != std::string::npos) {
                results.push_back(student);
            }
        }
        
        if (results.empty()) {
            std::cout << "Khong tim thay sinh vien nao!" << std::endl;
        } else {
            std::cout << "\nKet qua tim kiem (" << results.size() << " sinh vien):" << std::endl;
            Student().displayHeader();
            for (const auto& student : results) {
                student.display();
            }
        }
    }
    
    void compareAlgorithms() {
        if (students.empty()) {
            std::cout << "\nDanh sach sinh vien trong!" << std::endl;
            return;
        }
        
        std::cout << "\n=== SO SANH HIEU SUAT THUAT TOAN ===" << std::endl;
        std::cout << "Dang thuc hien so sanh tren " << students.size() << " sinh vien..." << std::endl;
        
        std::vector<SortingStats> statsVector;
        
        // Test với sắp xếp theo điểm
        auto compareFn = compareByScore;
        
        // Bubble Sort
        auto copy1 = students;
        statsVector.push_back(SortingAlgorithms::bubbleSort(copy1, compareFn));
        
        // Selection Sort
        auto copy2 = students;
        statsVector.push_back(SortingAlgorithms::selectionSort(copy2, compareFn));
        
        // Insertion Sort
        auto copy3 = students;
        statsVector.push_back(SortingAlgorithms::insertionSort(copy3, compareFn));
        
        // Quick Sort
        auto copy4 = students;
        statsVector.push_back(SortingAlgorithms::quickSort(copy4, compareFn));
        
        // Merge Sort
        auto copy5 = students;
        statsVector.push_back(SortingAlgorithms::mergeSort(copy5, compareFn));
        
        SortingAlgorithms::compareAlgorithms(statsVector);
    }
    
    void generateSampleData() {
        std::cout << "\n=== TAO DU LIEU MAU ===" << std::endl;
        std::cout << "Nhap so luong sinh vien muon tao: ";
        
        int count;
        std::cin >> count;
        
        if (count <= 0 || count > 10000) {
            std::cout << "So luong khong hop le (1-10000)!" << std::endl;
            return;
        }
        
        students = SortingAlgorithms::generateRandomStudents(count);
        std::cout << "Da tao " << count << " sinh vien mau!" << std::endl;
    }
    
    void clearAllData() {
        std::cout << "\nBan co chac chan muon xoa tat ca du lieu? (y/n): ";
        char choice;
        std::cin >> choice;
        
        if (choice == 'y' || choice == 'Y') {
            students.clear();
            std::cout << "Da xoa tat ca du lieu!" << std::endl;
        }
    }
    
    void run() {
        int choice;
        
        std::cout << "CHUONG TRINH MO PHONG CAC THUAT TOAN SAP XEP NOI CO BAN" << std::endl;
        std::cout << "Ung dung: Quan ly sinh vien" << std::endl;
        
        do {
            displayMenu();
            std::cin >> choice;
            
            // Clear input buffer
            if (std::cin.fail()) {
                std::cin.clear();
                std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
                choice = -1;
            }
            
            switch (choice) {
                case 1: addStudent(); break;
                case 2: displayStudents(); break;
                case 3: sortMenu(); break;
                case 4: searchStudent(); break;
                case 5: compareAlgorithms(); break;
                case 6: generateSampleData(); break;
                case 7: clearAllData(); break;
                case 0: 
                    std::cout << "\nCam on ban da su dung chuong trinh!" << std::endl;
                    break;
                default:
                    std::cout << "\nLua chon khong hop le! Vui long thu lai." << std::endl;
                    break;
            }
            
            if (choice != 0) {
                std::cout << "\nNhan Enter de tiep tuc...";
                std::cin.ignore();
                std::cin.get();
            }
            
        } while (choice != 0);
    }
};

int main() {
    // Set console to support Vietnamese characters
    system("chcp 65001 > nul");
    
    StudentManager manager;
    manager.run();
    
    return 0;
}
