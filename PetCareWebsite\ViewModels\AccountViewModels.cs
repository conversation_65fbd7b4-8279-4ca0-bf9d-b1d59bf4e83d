using System.ComponentModel.DataAnnotations;

namespace PetCareWebsite.ViewModels;

public class LoginViewModel
{
    [Required(ErrorMessage = "Email là bắt buộc")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    [Display(Name = "Email")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
    [DataType(DataType.Password)]
    [Display(Name = "Mật khẩu")]
    public string Password { get; set; } = string.Empty;

    [Display(Name = "Ghi nhớ đăng nhập")]
    public bool RememberMe { get; set; }
}

public class RegisterViewModel
{
    [Required(ErrorMessage = "Họ tên là bắt buộc")]
    [Display(Name = "Họ tên")]
    public string FullName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email là bắt buộc")]
    [EmailAddress(ErrorMessage = "Email không hợp lệ")]
    [Display(Name = "Email")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Số điện thoại là bắt buộc")]
    [Phone(ErrorMessage = "Số điện thoại không hợp lệ")]
    [Display(Name = "Số điện thoại")]
    public string PhoneNumber { get; set; } = string.Empty;

    [Required(ErrorMessage = "Địa chỉ là bắt buộc")]
    [Display(Name = "Địa chỉ")]
    public string Address { get; set; } = string.Empty;

    [Required(ErrorMessage = "Mật khẩu là bắt buộc")]
    [StringLength(100, ErrorMessage = "Mật khẩu phải có ít nhất {2} ký tự.", MinimumLength = 6)]
    [DataType(DataType.Password)]
    [Display(Name = "Mật khẩu")]
    public string Password { get; set; } = string.Empty;

    [DataType(DataType.Password)]
    [Display(Name = "Xác nhận mật khẩu")]
    [Compare("Password", ErrorMessage = "Mật khẩu xác nhận không khớp.")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

public class PetViewModel
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Tên thú cưng là bắt buộc")]
    [Display(Name = "Tên thú cưng")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Loài là bắt buộc")]
    [Display(Name = "Loài")]
    public string Species { get; set; } = string.Empty;

    [Required(ErrorMessage = "Giống là bắt buộc")]
    [Display(Name = "Giống")]
    public string Breed { get; set; } = string.Empty;

    [Required(ErrorMessage = "Tuổi là bắt buộc")]
    [Range(0, 50, ErrorMessage = "Tuổi phải từ 0 đến 50")]
    [Display(Name = "Tuổi")]
    public int Age { get; set; }

    [Required(ErrorMessage = "Giới tính là bắt buộc")]
    [Display(Name = "Giới tính")]
    public string Gender { get; set; } = string.Empty;

    [Required(ErrorMessage = "Cân nặng là bắt buộc")]
    [Range(0.1, 200, ErrorMessage = "Cân nặng phải từ 0.1kg đến 200kg")]
    [Display(Name = "Cân nặng (kg)")]
    public decimal Weight { get; set; }

    [Display(Name = "Mô tả thêm")]
    public string? Description { get; set; }
}

public class AppointmentViewModel
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Vui lòng chọn thú cưng")]
    [Display(Name = "Thú cưng")]
    public int PetId { get; set; }

    [Required(ErrorMessage = "Vui lòng chọn dịch vụ")]
    [Display(Name = "Dịch vụ")]
    public int ServiceId { get; set; }

    [Required(ErrorMessage = "Vui lòng chọn ngày hẹn")]
    [Display(Name = "Ngày hẹn")]
    [DataType(DataType.Date)]
    public DateTime AppointmentDate { get; set; } = DateTime.Today.AddDays(1);

    [Required(ErrorMessage = "Vui lòng chọn giờ hẹn")]
    [Display(Name = "Giờ hẹn")]
    [DataType(DataType.Time)]
    public TimeSpan AppointmentTime { get; set; } = new TimeSpan(9, 0, 0);

    [Display(Name = "Ghi chú")]
    public string? Notes { get; set; }
}

public class AdminDashboardViewModel
{
    public int TotalUsers { get; set; }
    public int TotalPets { get; set; }
    public int TotalServices { get; set; }
    public int TotalAppointments { get; set; }
    public int PendingAppointments { get; set; }
    public int CompletedAppointments { get; set; }
    public decimal TotalRevenue { get; set; }
    public List<PetCareWebsite.Models.Appointment> RecentAppointments { get; set; } = new();
}

public class ServiceViewModel
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Tên dịch vụ là bắt buộc")]
    [Display(Name = "Tên dịch vụ")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Mô tả là bắt buộc")]
    [Display(Name = "Mô tả")]
    public string Description { get; set; } = string.Empty;

    [Required(ErrorMessage = "Giá là bắt buộc")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
    [Display(Name = "Giá (VNĐ)")]
    public decimal Price { get; set; }

    [Required(ErrorMessage = "Thời gian là bắt buộc")]
    [Range(1, 480, ErrorMessage = "Thời gian phải từ 1 đến 480 phút")]
    [Display(Name = "Thời gian (phút)")]
    public int DurationMinutes { get; set; }

    [Display(Name = "Kích hoạt")]
    public bool IsActive { get; set; } = true;
}
