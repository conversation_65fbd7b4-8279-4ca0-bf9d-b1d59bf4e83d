﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Trung Tâm Chăm Só<PERSON></title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/dark-theme.css?v=2.0&t=<?= time() ?>" />
    <link rel="stylesheet" href="~/PetCareWebsite.styles.css" asp-append-version="true" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* FORCE DARK THEME - IMMEDIATE OVERRIDE */
        * {
            box-sizing: border-box !important;
        }

        html, body {
            background: #0f0f23 !important;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%) !important;
            background-attachment: fixed !important;
            min-height: 100vh !important;
            color: #e0e0e0 !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }

        /* NAVBAR FORCE OVERRIDE */
        .navbar {
            background: rgba(26, 32, 44, 0.95) !important;
            background: #1a202c !important;
            backdrop-filter: blur(20px) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
        }

        .navbar-brand {
            color: #63b3ed !important;
            background: linear-gradient(135deg, #63b3ed 0%, #4299e1 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            font-weight: 700 !important;
            font-size: 1.5rem !important;
        }

        .nav-link {
            color: #e2e8f0 !important;
            color: #cbd5e0 !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
        }

        .nav-link:hover {
            color: #63b3ed !important;
            transform: translateY(-1px) !important;
        }

        .text-light {
            color: #f7fafc !important;
        }

        .text-info {
            color: #63b3ed !important;
        }

        .text-success {
            color: #68d391 !important;
        }

        .text-warning {
            color: #f6e05e !important;
        }

        .text-danger {
            color: #fc8181 !important;
        }

        .dropdown-menu {
            background: rgba(26, 32, 44, 0.95) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-radius: 12px !important;
        }

        .dropdown-item {
            color: #e2e8f0 !important;
            transition: all 0.3s ease !important;
        }

        .dropdown-item:hover {
            background: rgba(99, 179, 237, 0.2) !important;
            color: #63b3ed !important;
        }

        /* FORCE CARD STYLES */
        .card {
            background: rgba(26, 32, 44, 0.8) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-radius: 16px !important;
            color: #e2e8f0 !important;
        }

        .card-title {
            color: #63b3ed !important;
        }

        .card-text {
            color: #cbd5e0 !important;
        }

        /* FORCE BUTTON STYLES */
        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
            border: none !important;
            color: white !important;
        }

        .btn-outline-primary {
            border: 2px solid #4299e1 !important;
            color: #63b3ed !important;
            background: transparent !important;
        }

        /* FORCE HERO SECTION */
        .hero-section {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2d3748 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        /* FORCE TEXT COLORS */
        h1, h2, h3, h4, h5, h6 {
            color: #f8fafc !important;
        }

        .text-muted {
            color: #a0aec0 !important;
        }

        /* FORCE ANIMATIONS */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .floating {
            animation: float 6s ease-in-out infinite !important;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 3s ease-in-out infinite !important;
        }
    </style>
</head>
<body class="page-enter">
    <div class="page-bg"></div>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark shadow-sm">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-paw me-2 floating text-info"></i>Trung Tâm Chăm Sóc Thú Cưng
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1 me-auto">
                        <li class="nav-item">
                            <a class="nav-link text-light fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1 text-info"></i>Trang Chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-light fw-bold" asp-area="" asp-controller="Services" asp-action="Index">
                                <i class="fas fa-heart me-1 pulse text-danger"></i>Dịch Vụ
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            @if (User.IsInRole("Admin"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link text-warning fw-bold pulse" asp-area="" asp-controller="Admin" asp-action="Index">
                                        ⚙️ Quản Trị
                                    </a>
                                </li>
                            }
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-light fw-bold" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    👤 @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu glass-effect" aria-labelledby="navbarDropdown">
                                    <li><a class="dropdown-item text-light" asp-controller="Pets" asp-action="Index">🐾 Thú Cưng Của Tôi</a></li>
                                    <li><a class="dropdown-item text-light" asp-controller="Appointments" asp-action="Index">📅 Lịch Hẹn</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item text-light">🚪 Đăng Xuất</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link text-info fw-bold glow" asp-controller="Account" asp-action="Login">
                                    🔐 Đăng Nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-success fw-bold bounce-heart" asp-controller="Account" asp-action="Register">
                                    🎉 Đăng Ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>


    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        // FORCE APPLY DARK THEME
        document.addEventListener('DOMContentLoaded', function() {
            // Force body background
            document.body.style.background = 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)';
            document.body.style.backgroundAttachment = 'fixed';
            document.body.style.color = '#e0e0e0';
            document.body.style.minHeight = '100vh';

            // Force navbar
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.style.background = '#1a202c';
                navbar.style.backdropFilter = 'blur(20px)';
                navbar.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
            }

            // Force nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.style.color = '#cbd5e0';
                link.style.fontWeight = '500';
            });

            // Force cards
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.style.background = 'rgba(26, 32, 44, 0.8)';
                card.style.backdropFilter = 'blur(20px)';
                card.style.border = '1px solid rgba(255, 255, 255, 0.1)';
                card.style.borderRadius = '16px';
                card.style.color = '#e2e8f0';
            });

            // Force headings
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            headings.forEach(heading => {
                heading.style.color = '#f8fafc';
            });

            console.log('Dark theme applied successfully!');
        });
    </script>

    <script>
        // Add dynamic effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add sparkle effects
            function createSparkle() {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                sparkle.style.position = 'fixed';
                sparkle.style.left = Math.random() * window.innerWidth + 'px';
                sparkle.style.top = Math.random() * window.innerHeight + 'px';
                sparkle.style.width = '4px';
                sparkle.style.height = '4px';
                sparkle.style.background = 'white';
                sparkle.style.borderRadius = '50%';
                sparkle.style.pointerEvents = 'none';
                sparkle.style.zIndex = '1000';
                sparkle.style.animation = 'sparkle 2s ease-in-out';

                document.body.appendChild(sparkle);

                setTimeout(() => {
                    sparkle.remove();
                }, 2000);
            }

            // Create sparkles periodically
            setInterval(createSparkle, 1000);

            // Add hover effects to cards
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                    this.style.transition = 'all 0.3s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click effects to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255,255,255,0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.pointerEvents = 'none';

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkle {
                0%, 100% { opacity: 0; transform: scale(0); }
                50% { opacity: 1; transform: scale(1); }
            }

            @keyframes ripple {
                to { transform: scale(4); opacity: 0; }
            }

            .hero-section {
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%) !important;
                position: relative !important;
                overflow: hidden !important;
            }

            .service-card {
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
                border-radius: 20px !important;
                overflow: hidden !important;
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(10px) !important;
                border: 1px solid rgba(255, 255, 255, 0.2) !important;
            }

            .navbar {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(10px) !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
            }
        `;
        document.head.appendChild(style);
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
