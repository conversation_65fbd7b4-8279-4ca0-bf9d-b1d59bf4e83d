#!/bin/bash

echo "==================================="
echo "BIÊN DỊCH ĐỒ ÁN LUỒNG CỰC ĐẠI"
echo "==================================="

echo
echo "Biên dịch chương trình chính..."
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram
if [ $? -eq 0 ]; then
    echo "✓ Biên dịch chương trình chính thành công!"
else
    echo "✗ Lỗi biên dịch chương trình chính!"
    exit 1
fi

echo
echo "Biên dịch chương trình test..."
g++ -std=c++11 -Wall -O2 test_main.cpp Graph.cpp GraphUtils.cpp -o TestProgram
if [ $? -eq 0 ]; then
    echo "✓ Biên dịch chương trình test thành công!"
else
    echo "✗ Lỗi biên dịch chương trình test!"
fi

echo
echo "Biên dịch demo đơn giản..."
g++ -std=c++11 -Wall -O2 demo.cpp Graph.cpp GraphUtils.cpp -o Demo
if [ $? -eq 0 ]; then
    echo "✓ Biên dịch demo thành công!"
else
    echo "✗ Lỗi biên dịch demo!"
fi

echo
echo "==================================="
echo "HOÀN THÀNH BIÊN DỊCH"
echo "==================================="
echo
echo "Các file thực thi đã tạo:"
echo "- MaxFlowProgram (chương trình chính)"
echo "- TestProgram (chương trình test)"
echo "- Demo (demo đơn giản)"
echo
echo "Để chạy:"
echo "  ./MaxFlowProgram"
echo "  ./TestProgram"
echo "  ./Demo"
echo

# Cấp quyền thực thi
chmod +x MaxFlowProgram TestProgram Demo 2>/dev/null

echo "Nhấn Enter để tiếp tục..."
read
