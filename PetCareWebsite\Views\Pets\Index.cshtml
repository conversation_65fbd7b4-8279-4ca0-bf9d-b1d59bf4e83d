@model IEnumerable<PetCareWebsite.Models.Pet>
@{
    ViewData["Title"] = "Thú <PERSON>ng Của Tôi";
}

<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-5 fw-bold text-primary">
                    <i class="fas fa-paw me-3"></i>Thú <PERSON>ng Của Tôi
                </h1>
                <a asp-action="Create" class="btn btn-success btn-lg">
                    <i class="fas fa-plus me-2"></i>Thêm Th<PERSON>
                </a>
            </div>
        </div>
    </div>

    @if (Model.Any())
    {
        <div class="row g-4">
            @foreach (var pet in Model)
            {
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm pet-card">
                        <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center text-white" style="height: 200px;">
                            <div class="text-center">
                                <i class="fas fa-heart fa-4x mb-2"></i>
                                <h5 class="mb-0">@pet.Name</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title text-primary">@pet.Name</h5>
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Loài</small>
                                    <div class="fw-bold">@pet.Species</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Tuổi</small>
                                    <div class="fw-bold">@pet.Age tuổi</div>
                                </div>
                            </div>
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Giống</small>
                                    <div class="fw-bold">@pet.Breed</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Cân nặng</small>
                                    <div class="fw-bold">@pet.Weight kg</div>
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(pet.Description))
                            {
                                <p class="card-text text-muted">@pet.Description</p>
                            }
                        </div>
                        <div class="card-footer bg-transparent border-0">
                            <div class="d-grid gap-2">
                                <a asp-action="Details" asp-route-id="@pet.Id" class="btn btn-outline-primary">
                                    <i class="fas fa-info-circle me-2"></i>Xem Chi Tiết
                                </a>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@pet.Id" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>Sửa
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@pet.Id" class="btn btn-danger">
                                        <i class="fas fa-trash me-1"></i>Xóa
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12 text-center py-5">
                <i class="fas fa-paw fa-5x text-muted mb-4"></i>
                <h3 class="text-muted mb-3">Bạn chưa có thú cưng nào</h3>
                <p class="text-muted mb-4">Hãy thêm thông tin thú cưng để có thể đặt lịch hẹn dịch vụ</p>
                <a asp-action="Create" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Thêm Thú Cưng Đầu Tiên
                </a>
            </div>
        </div>
    }
</div>

<style>
.pet-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.pet-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
</style>
