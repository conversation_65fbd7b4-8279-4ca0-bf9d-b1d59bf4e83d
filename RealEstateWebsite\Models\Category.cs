using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models
{
    public class Category
    {
        public int CategoryId { get; set; }

        [Required(ErrorMessage = "Tên danh mục là bắt buộc")]
        [StringLength(100, ErrorMessage = "Tên danh mục không được vượt quá 100 ký tự")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Mô tả không được vượt quá 500 ký tự")]
        public string? Description { get; set; }

        [StringLength(100, ErrorMessage = "Icon không được vượt quá 100 ký tự")]
        public string? Icon { get; set; }

        [StringLength(200, ErrorMessage = "Hình ảnh không được vượt quá 200 ký tự")]
        public string? Image { get; set; }

        public bool IsActive { get; set; } = true;

        public int DisplayOrder { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation property
        public ICollection<Property> Properties { get; set; } = new List<Property>();

        // SEO properties
        [StringLength(200)]
        public string? MetaTitle { get; set; }

        [StringLength(500)]
        public string? MetaDescription { get; set; }

        [StringLength(200)]
        public string? MetaKeywords { get; set; }

        // URL slug for SEO-friendly URLs
        [StringLength(150)]
        public string? Slug { get; set; }
    }
}
