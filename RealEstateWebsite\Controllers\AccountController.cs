using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstateWebsite.Data;
using RealEstateWebsite.Models;
using RealEstateWebsite.Models.ViewModels;

namespace RealEstateWebsite.Controllers;

public class AccountController : Controller
{
    private readonly RealEstateContext _context;

    public AccountController(RealEstateContext context)
    {
        _context = context;
    }

    // GET: Account/Login
    public IActionResult Login()
    {
        // Check if already logged in
        if (HttpContext.Session.GetString("UserLoggedIn") == "true")
        {
            return RedirectToAction("Index", "Home");
        }
        
        return View();
    }

    // POST: Account/Login
    [HttpPost]
    public async Task<IActionResult> Login(UserLoginViewModel model)
    {
        if (ModelState.IsValid)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => (u.Username == model.UsernameOrEmail || u.Email == model.UsernameOrEmail) && u.IsActive);

            if (user != null && BCrypt.Net.BCrypt.Verify(model.Password, user.PasswordHash))
            {
                // Set session
                HttpContext.Session.SetString("UserLoggedIn", "true");
                HttpContext.Session.SetString("UserId", user.UserId.ToString());
                HttpContext.Session.SetString("Username", user.Username);
                HttpContext.Session.SetString("UserFullName", user.FullName);
                HttpContext.Session.SetString("UserRole", user.Role.ToString());
                
                // Update last login
                user.LastLoginDate = DateTime.Now;
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = $"Chào mừng {user.FullName}!";
                return RedirectToAction("Index", "Home");
            }
            else
            {
                ViewData["ErrorMessage"] = "Tên đăng nhập/Email hoặc mật khẩu không đúng!";
            }
        }
        
        return View(model);
    }

    // GET: Account/Register
    public IActionResult Register()
    {
        // Check if already logged in
        if (HttpContext.Session.GetString("UserLoggedIn") == "true")
        {
            return RedirectToAction("Index", "Home");
        }
        
        return View();
    }

    // POST: Account/Register
    [HttpPost]
    public async Task<IActionResult> Register(UserRegisterViewModel model)
    {
        if (ModelState.IsValid)
        {
            // Check if username or email already exists
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == model.Username || u.Email == model.Email);

            if (existingUser != null)
            {
                if (existingUser.Username == model.Username)
                {
                    ModelState.AddModelError("Username", "Tên đăng nhập đã tồn tại!");
                }
                if (existingUser.Email == model.Email)
                {
                    ModelState.AddModelError("Email", "Email đã được sử dụng!");
                }
                return View(model);
            }

            // Create new user
            var user = new User
            {
                Username = model.Username,
                Email = model.Email,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(model.Password),
                FullName = model.FullName,
                Phone = model.Phone,
                Role = UserRole.Customer,
                IsActive = true,
                EmailConfirmed = false,
                CreatedDate = DateTime.Now
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Đăng ký thành công! Vui lòng đăng nhập.";
            return RedirectToAction("Login");
        }
        
        return View(model);
    }

    // GET: Account/Logout
    public IActionResult Logout()
    {
        HttpContext.Session.Clear();
        TempData["SuccessMessage"] = "Đã đăng xuất thành công!";
        return RedirectToAction("Index", "Home");
    }

    // GET: Account/Profile
    public async Task<IActionResult> Profile()
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        var userId = int.Parse(HttpContext.Session.GetString("UserId")!);
        var user = await _context.Users.FindAsync(userId);

        if (user == null)
        {
            return RedirectToAction("Logout");
        }

        var model = new UserProfileViewModel
        {
            UserId = user.UserId,
            Username = user.Username,
            Email = user.Email,
            FullName = user.FullName,
            Phone = user.Phone,
            Address = user.Address,
            DateOfBirth = user.DateOfBirth,
            Gender = user.Gender,
            Occupation = user.Occupation,
            Company = user.Company,
            PreferredBudgetMin = user.PreferredBudgetMin,
            PreferredBudgetMax = user.PreferredBudgetMax,
            PreferredLocation = user.PreferredLocation,
            PreferredPropertyType = user.PreferredPropertyType
        };

        return View(model);
    }

    // POST: Account/Profile
    [HttpPost]
    public async Task<IActionResult> Profile(UserProfileViewModel model)
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        if (ModelState.IsValid)
        {
            var userId = int.Parse(HttpContext.Session.GetString("UserId")!);
            var user = await _context.Users.FindAsync(userId);

            if (user == null)
            {
                return RedirectToAction("Logout");
            }

            // Update user information
            user.FullName = model.FullName;
            user.Phone = model.Phone;
            user.Address = model.Address;
            user.DateOfBirth = model.DateOfBirth;
            user.Gender = model.Gender;
            user.Occupation = model.Occupation;
            user.Company = model.Company;
            user.PreferredBudgetMin = model.PreferredBudgetMin;
            user.PreferredBudgetMax = model.PreferredBudgetMax;
            user.PreferredLocation = model.PreferredLocation;
            user.PreferredPropertyType = model.PreferredPropertyType;

            await _context.SaveChangesAsync();

            // Update session
            HttpContext.Session.SetString("UserFullName", user.FullName);

            TempData["SuccessMessage"] = "Cập nhật thông tin thành công!";
            return RedirectToAction("Profile");
        }

        return View(model);
    }

    // GET: Account/ChangePassword
    public IActionResult ChangePassword()
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");
            
        return View();
    }

    // POST: Account/ChangePassword
    [HttpPost]
    public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        if (ModelState.IsValid)
        {
            var userId = int.Parse(HttpContext.Session.GetString("UserId")!);
            var user = await _context.Users.FindAsync(userId);

            if (user == null)
            {
                return RedirectToAction("Logout");
            }

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(model.CurrentPassword, user.PasswordHash))
            {
                ModelState.AddModelError("CurrentPassword", "Mật khẩu hiện tại không đúng!");
                return View(model);
            }

            // Update password
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(model.NewPassword);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Đổi mật khẩu thành công!";
            return RedirectToAction("Profile");
        }

        return View(model);
    }

    // GET: Account/MyFavorites
    public async Task<IActionResult> MyFavorites()
    {
        if (!IsAuthenticated())
            return RedirectToAction("Login");

        var userId = int.Parse(HttpContext.Session.GetString("UserId")!);
        var sessionId = HttpContext.Session.Id;

        var favorites = await _context.Favorites
            .Include(f => f.Property)
            .ThenInclude(p => p.Category)
            .Where(f => f.SessionId == sessionId || f.UserEmail == HttpContext.Session.GetString("UserEmail"))
            .OrderByDescending(f => f.CreatedDate)
            .ToListAsync();

        return View(favorites);
    }

    // Helper method to check authentication
    private bool IsAuthenticated()
    {
        return HttpContext.Session.GetString("UserLoggedIn") == "true";
    }
}
