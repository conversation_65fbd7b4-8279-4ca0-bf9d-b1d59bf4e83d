﻿@model RealEstateWebsite.Models.ViewModels.HomeViewModel

@{
    ViewData["Title"] = "Trang Chủ";
    ViewData["MetaDescription"] = "Website bất động sản hàng đầu Việt Nam - Tìm kiếm nhà đất, chung cư, bi<PERSON><PERSON> thự";
    ViewData["MetaKeywords"] = "bất động sản, nh<PERSON> đất, chung cư, biệt thự, mua bán nhà";
}

<!-- Hero Section with Stunning Visuals -->
<section class="hero-section">
    <div class="hero-carousel">
        <!-- Slide 1 - Luxury Villa -->
        <div class="hero-slide active" style="background-image: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.6) 100%), url('https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            <div class="hero-particles"></div>
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-7">
                        <div class="hero-content text-white animate-fade-in">
                            <div class="hero-badge">
                                <i class="fas fa-crown"></i>
                                <span>Bất Động Sản Cao Cấp</span>
                            </div>
                            <h1 class="hero-title">
                                Khám Phá <span class="gradient-text">Ngôi Nhà</span><br>
                                Trong Mơ Của Bạn
                            </h1>
                            <p class="hero-subtitle">
                                Hơn 10,000+ bất động sản cao cấp đang chờ bạn khám phá.
                                Từ biệt thự sang trọng đến chung cư hiện đại, chúng tôi có tất cả.
                            </p>
                            <div class="hero-features">
                                <div class="feature-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Pháp lý rõ ràng</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-handshake"></i>
                                    <span>Tư vấn miễn phí</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-clock"></i>
                                    <span>Hỗ trợ 24/7</span>
                                </div>
                            </div>
                            <div class="hero-stats">
                                <div class="stat-item">
                                    <div class="stat-number">@Model.Statistics.TotalProperties+</div>
                                    <div class="stat-label">Bất Động Sản</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">@Model.Statistics.HappyCustomers+</div>
                                    <div class="stat-label">Khách Hàng</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">@Model.Statistics.YearsExperience+</div>
                                    <div class="stat-label">Năm Kinh Nghiệm</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2 - Modern Apartment -->
        <div class="hero-slide" style="background-image: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.6) 100%), url('https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            <div class="hero-particles"></div>
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-7">
                        <div class="hero-content text-white animate-fade-in">
                            <div class="hero-badge">
                                <i class="fas fa-building"></i>
                                <span>Chung Cư Hiện Đại</span>
                            </div>
                            <h1 class="hero-title">
                                <span class="gradient-text">Căn Hộ Cao Cấp</span><br>
                                View Thành Phố Tuyệt Đẹp
                            </h1>
                            <p class="hero-subtitle">
                                Sống trong không gian hiện đại với đầy đủ tiện ích 5 sao.
                                Từ gym, hồ bơi đến khu vui chơi trẻ em, tất cả đều có sẵn.
                            </p>
                            <div class="hero-amenities">
                                <div class="amenity-item">
                                    <i class="fas fa-swimming-pool"></i>
                                    <span>Hồ bơi</span>
                                </div>
                                <div class="amenity-item">
                                    <i class="fas fa-dumbbell"></i>
                                    <span>Phòng gym</span>
                                </div>
                                <div class="amenity-item">
                                    <i class="fas fa-car"></i>
                                    <span>Bãi đỗ xe</span>
                                </div>
                                <div class="amenity-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>An ninh 24/7</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3 - Townhouse -->
        <div class="hero-slide" style="background-image: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.6) 100%), url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            <div class="hero-particles"></div>
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-7">
                        <div class="hero-content text-white animate-fade-in">
                            <div class="hero-badge">
                                <i class="fas fa-home"></i>
                                <span>Nhà Phố Thông Minh</span>
                            </div>
                            <h1 class="hero-title">
                                <span class="gradient-text">Smart Home</span><br>
                                Công Nghệ Tương Lai
                            </h1>
                            <p class="hero-subtitle">
                                Nhà phố thông minh với hệ thống IoT hiện đại.
                                Điều khiển mọi thứ từ xa, tiết kiệm năng lượng và an toàn tuyệt đối.
                            </p>
                            <div class="hero-tech">
                                <div class="tech-item">
                                    <i class="fas fa-wifi"></i>
                                    <span>Smart WiFi</span>
                                </div>
                                <div class="tech-item">
                                    <i class="fas fa-thermometer-half"></i>
                                    <span>Điều hòa thông minh</span>
                                </div>
                                <div class="tech-item">
                                    <i class="fas fa-video"></i>
                                    <span>Camera AI</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Arrows -->
        <button class="carousel-nav prev" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="carousel-nav next" onclick="changeSlide(1)">
            <i class="fas fa-chevron-right"></i>
        </button>

        <!-- Dots Indicator -->
        <div class="carousel-dots">
            <span class="dot active" onclick="currentSlideFunc(1)"></span>
            <span class="dot" onclick="currentSlideFunc(2)"></span>
            <span class="dot" onclick="currentSlideFunc(3)"></span>
        </div>
    </div>

    <!-- Floating Search Form -->
    <div class="floating-search">
        <div class="container">
            <div class="search-card">
                <div class="search-header">
                    <h3><i class="fas fa-search"></i> Tìm Kiếm Bất Động Sản</h3>
                    <p>Nhập thông tin để tìm kiếm bất động sản phù hợp</p>
                </div>

                <form asp-controller="Properties" asp-action="Index" method="get" class="advanced-search-form">
                    <div class="search-tabs">
                        <input type="radio" name="Type" value="1" id="sale-tab" checked>
                        <label for="sale-tab" class="tab-label">
                            <i class="fas fa-tag"></i>
                            <span>Mua Bán</span>
                        </label>

                        <input type="radio" name="Type" value="2" id="rent-tab">
                        <label for="rent-tab" class="tab-label">
                            <i class="fas fa-key"></i>
                            <span>Cho Thuê</span>
                        </label>
                    </div>

                    <div class="search-fields">
                        <div class="field-group">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                <input type="text" name="Keyword" class="form-control" placeholder="Nhập địa chỉ, tên dự án...">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="field-group">
                                    <select name="CategoryId" class="form-select">
                                        <option value="">Loại bất động sản</option>
                                        @foreach (var category in Model.Categories)
                                        {
                                            <option value="@category.CategoryId">@category.Name</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field-group">
                                    <select name="City" class="form-select">
                                        <option value="">Chọn thành phố</option>
                                        <option value="Hồ Chí Minh">TP. Hồ Chí Minh</option>
                                        <option value="Hà Nội">Hà Nội</option>
                                        <option value="Đà Nẵng">Đà Nẵng</option>
                                        <option value="Cần Thơ">Cần Thơ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="field-group">
                                    <select name="MinPrice" class="form-select">
                                        <option value="">Giá từ</option>
                                        <option value="500000000">500 triệu</option>
                                        <option value="1000000000">1 tỷ</option>
                                        <option value="2000000000">2 tỷ</option>
                                        <option value="5000000000">5 tỷ</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="field-group">
                                    <select name="MaxPrice" class="form-select">
                                        <option value="">Giá đến</option>
                                        <option value="1000000000">1 tỷ</option>
                                        <option value="2000000000">2 tỷ</option>
                                        <option value="5000000000">5 tỷ</option>
                                        <option value="10000000000">10 tỷ</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn-search">
                            <i class="fas fa-search"></i>
                            <span>Tìm Kiếm Ngay</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="why-choose-us">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="why-content">
                    <div class="section-badge">
                        <i class="fas fa-star"></i>
                        <span>Tại Sao Chọn Chúng Tôi</span>
                    </div>
                    <h2 class="section-title">
                        Đối Tác Tin Cậy Cho <br>
                        <span class="gradient-text">Mọi Giao Dịch BDS</span>
                    </h2>
                    <p class="section-description">
                        Với hơn 10 năm kinh nghiệm trong lĩnh vực bất động sản, chúng tôi cam kết mang đến
                        dịch vụ tốt nhất và những cơ hội đầu tư sinh lời cao nhất cho khách hàng.
                    </p>

                    <div class="why-features">
                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Pháp Lý Minh Bạch</h4>
                                <p>100% bất động sản có giấy tờ pháp lý đầy đủ, rõ ràng</p>
                            </div>
                        </div>

                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Đội Ngũ Chuyên Nghiệp</h4>
                                <p>Tư vấn viên giàu kinh nghiệm, hỗ trợ 24/7</p>
                            </div>
                        </div>

                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Giá Trị Gia Tăng</h4>
                                <p>Cam kết mang lại lợi nhuận tối ưu cho nhà đầu tư</p>
                            </div>
                        </div>
                    </div>

                    <div class="why-stats">
                        <div class="stat-box">
                            <div class="stat-number">98%</div>
                            <div class="stat-text">Khách hàng hài lòng</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">5000+</div>
                            <div class="stat-text">Giao dịch thành công</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number">24/7</div>
                            <div class="stat-text">Hỗ trợ khách hàng</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="why-images">
                    <div class="image-grid">
                        <div class="grid-item large">
                            <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Luxury House" class="img-fluid rounded-3">
                            <div class="image-overlay">
                                <div class="overlay-content">
                                    <h5>Biệt Thự Cao Cấp</h5>
                                    <p>Thiết kế sang trọng</p>
                                </div>
                            </div>
                        </div>
                        <div class="grid-item small top">
                            <img src="https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                 alt="Modern Apartment" class="img-fluid rounded-3">
                            <div class="image-overlay">
                                <div class="overlay-content">
                                    <h6>Chung Cư Hiện Đại</h6>
                                </div>
                            </div>
                        </div>
                        <div class="grid-item small bottom">
                            <img src="https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                 alt="Smart Home" class="img-fluid rounded-3">
                            <div class="image-overlay">
                                <div class="overlay-content">
                                    <h6>Smart Home</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating Elements -->
                    <div class="floating-element" style="top: 20%; right: -10%;">
                        <div class="floating-card">
                            <i class="fas fa-home"></i>
                            <span>1000+ BDS</span>
                        </div>
                    </div>

                    <div class="floating-element" style="bottom: 30%; left: -10%;">
                        <div class="floating-card">
                            <i class="fas fa-handshake"></i>
                            <span>Uy tín #1</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties Section -->
<section class="featured-properties">
    <div class="container">
        <div class="section-header text-center">
            <div class="section-badge">
                <i class="fas fa-gem"></i>
                <span>Bất Động Sản Nổi Bật</span>
            </div>
            <h2 class="section-title">
                Khám Phá <span class="gradient-text">Những Căn Hộ</span><br>
                Đẳng Cấp Nhất
            </h2>
            <p class="section-description">
                Tuyển chọn những bất động sản cao cấp nhất với vị trí đắc địa,
                thiết kế hiện đại và tiện ích 5 sao
            </p>
        </div>

        <div class="row">
            @if (Model.FeaturedProperties != null && Model.FeaturedProperties.Any())
            {
                @foreach (var property in Model.FeaturedProperties.Take(6))
                {
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="property-card">
                            <div class="property-image">
                                <img src="@(!string.IsNullOrEmpty(property.MainImage) ? property.MainImage : "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80")"
                                     alt="@property.Title" class="img-fluid">
                                <div class="property-badges">
                                    <span class="badge bg-danger">VIP</span>
                                    @if (property.Type == PropertyType.Sale)
                                    {
                                        <span class="badge bg-success">Bán</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-info">Thuê</span>
                                    }
                                </div>
                                <div class="property-overlay">
                                    <a asp-controller="Properties" asp-action="Details" asp-route-id="@property.PropertyId"
                                       class="btn btn-light btn-sm">
                                        <i class="fas fa-eye"></i> Xem Chi Tiết
                                    </a>
                                </div>
                            </div>
                            <div class="property-content">
                                <div class="property-price">
                                    @property.Price.ToString("N0") VNĐ
                                    @if (property.Type == PropertyType.Rent)
                                    {
                                        <span class="price-unit">/tháng</span>
                                    }
                                </div>
                                <h5 class="property-title">
                                    <a asp-controller="Properties" asp-action="Details" asp-route-id="@property.PropertyId">
                                        @property.Title
                                    </a>
                                </h5>
                                <p class="property-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    @property.Address, @property.City
                                </p>
                                <div class="property-features">
                                    <span><i class="fas fa-bed"></i> @property.Bedrooms phòng ngủ</span>
                                    <span><i class="fas fa-bath"></i> @property.Bathrooms phòng tắm</span>
                                    <span><i class="fas fa-ruler-combined"></i> @property.Area m²</span>
                                </div>
                                <div class="property-footer">
                                    <div class="property-agent">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                             alt="Agent" class="agent-avatar">
                                        <span>Môi giới</span>
                                    </div>
                                    <div class="property-date">
                                        @property.CreatedDate.ToString("dd/MM/yyyy")
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <!-- Demo Properties -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="property-card">
                        <div class="property-image">
                            <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Nhà phố hiện đại" class="img-fluid">
                            <div class="property-badges">
                                <span class="badge bg-danger">VIP</span>
                                <span class="badge bg-success">Bán</span>
                            </div>
                            <div class="property-overlay">
                                <a href="#" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                        <div class="property-content">
                            <div class="property-price">
                                10.500.000.000 VNĐ
                            </div>
                            <h5 class="property-title">
                                <a href="#">Nhà Phố Hiện Đại Quận 7</a>
                            </h5>
                            <p class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                Đường Nguyễn Thị Thập, Quận 7, TP.HCM
                            </p>
                            <div class="property-features">
                                <span><i class="fas fa-bed"></i> 4 phòng ngủ</span>
                                <span><i class="fas fa-bath"></i> 3 phòng tắm</span>
                                <span><i class="fas fa-ruler-combined"></i> 120 m²</span>
                            </div>
                            <div class="property-footer">
                                <div class="property-agent">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                         alt="Agent" class="agent-avatar">
                                    <span>Nguyễn Văn A</span>
                                </div>
                                <div class="property-date">
                                    15/12/2024
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="property-card">
                        <div class="property-image">
                            <img src="https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Chung cư cao cấp" class="img-fluid">
                            <div class="property-badges">
                                <span class="badge bg-warning">Hot</span>
                                <span class="badge bg-info">Thuê</span>
                            </div>
                            <div class="property-overlay">
                                <a href="#" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                        <div class="property-content">
                            <div class="property-price">
                                25.000.000 VNĐ <span class="price-unit">/tháng</span>
                            </div>
                            <h5 class="property-title">
                                <a href="#">Chung Cư Vinhomes Central Park</a>
                            </h5>
                            <p class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                Đường Nguyễn Hữu Cảnh, Bình Thạnh, TP.HCM
                            </p>
                            <div class="property-features">
                                <span><i class="fas fa-bed"></i> 2 phòng ngủ</span>
                                <span><i class="fas fa-bath"></i> 2 phòng tắm</span>
                                <span><i class="fas fa-ruler-combined"></i> 85 m²</span>
                            </div>
                            <div class="property-footer">
                                <div class="property-agent">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                         alt="Agent" class="agent-avatar">
                                    <span>Trần Thị B</span>
                                </div>
                                <div class="property-date">
                                    14/12/2024
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="property-card">
                        <div class="property-image">
                            <img src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                 alt="Biệt thự sang trọng" class="img-fluid">
                            <div class="property-badges">
                                <span class="badge bg-danger">VIP</span>
                                <span class="badge bg-success">Bán</span>
                            </div>
                            <div class="property-overlay">
                                <a href="#" class="btn btn-light btn-sm">
                                    <i class="fas fa-eye"></i> Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                        <div class="property-content">
                            <div class="property-price">
                                ************** VNĐ
                            </div>
                            <h5 class="property-title">
                                <a href="#">Biệt Thự Phú Mỹ Hưng</a>
                            </h5>
                            <p class="property-location">
                                <i class="fas fa-map-marker-alt"></i>
                                Khu đô thị Phú Mỹ Hưng, Quận 7, TP.HCM
                            </p>
                            <div class="property-features">
                                <span><i class="fas fa-bed"></i> 5 phòng ngủ</span>
                                <span><i class="fas fa-bath"></i> 4 phòng tắm</span>
                                <span><i class="fas fa-ruler-combined"></i> 300 m²</span>
                            </div>
                            <div class="property-footer">
                                <div class="property-agent">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&q=80"
                                         alt="Agent" class="agent-avatar">
                                    <span>Lê Văn C</span>
                                </div>
                                <div class="property-date">
                                    13/12/2024
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="text-center mt-4">
            <a asp-controller="Properties" asp-action="Index" class="btn btn-primary btn-lg">
                Xem Tất Cả Bất Động Sản <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">Danh Mục Bất Động Sản</h2>
            <p class="section-subtitle">Khám phá các loại bất động sản đa dạng phù hợp với nhu cầu của bạn</p>
        </div>

        <div class="row">
            @foreach (var category in Model.Categories)
            {
                <div class="col-lg-2 col-md-4 col-6 mb-4">
                    <a asp-controller="Properties" asp-action="Index" asp-route-categoryId="@category.CategoryId" class="category-card">
                        <div class="category-icon">
                            <i class="@category.Icon"></i>
                        </div>
                        <h5 class="category-name">@category.Name</h5>
                        <p class="category-count">@category.Properties.Count bất động sản</p>
                    </a>
                </div>
            }
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Enhanced Hero Carousel with smooth transitions
        let currentSlide = 0;
        const slides = document.querySelectorAll('.hero-slide');
        const dots = document.querySelectorAll('.dot');
        const totalSlides = slides.length;
        let autoPlayInterval;

        function showSlide(index) {
            // Remove active class from all slides and dots
            slides.forEach((slide, i) => {
                slide.classList.remove('active');
                if (i === index) {
                    setTimeout(() => slide.classList.add('active'), 50);
                }
            });

            dots.forEach((dot, i) => {
                dot.classList.remove('active');
                if (i === index) {
                    dot.classList.add('active');
                }
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }

        function changeSlide(direction) {
            clearInterval(autoPlayInterval);
            if (direction === 1) {
                nextSlide();
            } else {
                prevSlide();
            }
            startAutoPlay();
        }

        function currentSlideFunc(index) {
            clearInterval(autoPlayInterval);
            currentSlide = index - 1;
            showSlide(currentSlide);
            startAutoPlay();
        }

        function startAutoPlay() {
            autoPlayInterval = setInterval(nextSlide, 6000);
        }

        // Scroll animations
        function animateOnScroll() {
            const elements = document.querySelectorAll('.animate-on-scroll');
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('animated');
                }
            });
        }

        // Parallax effect for hero section
        function parallaxEffect() {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                const rate = scrolled * -0.5;
                heroSection.style.transform = `translateY(${rate}px)`;
            }
        }

        // Smooth number counting animation
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const target = parseInt(number.textContent.replace(/\D/g, ''));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current) + (number.textContent.includes('+') ? '+' : '');
                }, 20);
            });
        }

        // Enhanced search form interactions
        function enhanceSearchForm() {
            const searchForm = document.querySelector('.advanced-search-form');
            const inputs = searchForm.querySelectorAll('input, select');

            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.boxShadow = '0 5px 15px rgba(37, 99, 235, 0.1)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                    this.parentElement.style.boxShadow = 'none';
                });
            });
        }

        // Property card hover effects
        function enhancePropertyCards() {
            const propertyCards = document.querySelectorAll('.property-card');
            propertyCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px)';
                    this.style.boxShadow = '0 25px 60px rgba(0,0,0,0.15)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 8px 30px rgba(0,0,0,0.08)';
                });
            });
        }

        // Floating elements animation
        function animateFloatingElements() {
            const floatingElements = document.querySelectorAll('.floating-element');
            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });
        }

        // Initialize all animations and effects
        document.addEventListener('DOMContentLoaded', function() {
            // Start carousel
            startAutoPlay();

            // Add scroll event listeners
            window.addEventListener('scroll', () => {
                animateOnScroll();
                parallaxEffect();
            });

            // Initialize enhancements
            enhanceSearchForm();
            enhancePropertyCards();
            animateFloatingElements();

            // Trigger initial scroll animation
            setTimeout(animateOnScroll, 100);

            // Animate numbers when they come into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateNumbers();
                        observer.unobserve(entry.target);
                    }
                });
            });

            const statsSection = document.querySelector('.hero-stats');
            if (statsSection) {
                observer.observe(statsSection);
            }

            // Add loading animation to images
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('load', function() {
                    this.style.opacity = '1';
                    this.style.transform = 'scale(1)';
                });

                // Set initial state
                img.style.opacity = '0';
                img.style.transform = 'scale(0.95)';
                img.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Add CSS classes for scroll animations
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .animate-on-scroll.animated {
                opacity: 1;
                transform: translateY(0);
            }

            .property-card {
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            }
        `;
        document.head.appendChild(style);
    </script>
}
