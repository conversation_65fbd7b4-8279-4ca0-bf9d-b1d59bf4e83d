using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models.ViewModels
{
    public class PropertySearchViewModel
    {
        [StringLength(200)]
        public string? Keyword { get; set; }

        public int? CategoryId { get; set; }

        public PropertyType? Type { get; set; }

        [StringLength(100)]
        public string? City { get; set; }

        [StringLength(100)]
        public string? District { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Giá tối thiểu phải lớn hơn hoặc bằng 0")]
        public decimal? MinPrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Giá tối đa phải lớn hơn hoặc bằng 0")]
        public decimal? MaxPrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Diện tích tối thiểu phải lớn hơn hoặc bằng 0")]
        public double? MinArea { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Diện tích tối đa phải lớn hơn hoặc bằng 0")]
        public double? MaxArea { get; set; }

        public int? MinBedrooms { get; set; }
        public int? MaxBedrooms { get; set; }

        public int? MinBathrooms { get; set; }
        public int? MaxBathrooms { get; set; }

        // Advanced search options
        public bool? HasParking { get; set; }
        public bool? HasGarden { get; set; }
        public bool? HasSwimmingPool { get; set; }
        public bool? HasElevator { get; set; }
        public bool? HasBalcony { get; set; }
        public bool? HasAirConditioning { get; set; }
        public bool? HasSecurity { get; set; }

        public string? Orientation { get; set; }
        public int? YearBuiltFrom { get; set; }
        public int? YearBuiltTo { get; set; }

        // Sorting options
        public PropertySortBy SortBy { get; set; } = PropertySortBy.CreatedDate;
        public SortDirection SortDirection { get; set; } = SortDirection.Descending;

        // Pagination
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 12;

        // View mode
        public ViewMode ViewMode { get; set; } = ViewMode.Grid;
    }

    public class PropertySearchResultViewModel
    {
        public List<Property> Properties { get; set; } = new List<Property>();
        public PropertySearchViewModel SearchCriteria { get; set; } = new PropertySearchViewModel();
        public PaginationViewModel Pagination { get; set; } = new PaginationViewModel();
        public List<Category> Categories { get; set; } = new List<Category>();
        public List<string> Cities { get; set; } = new List<string>();
        public List<string> Districts { get; set; } = new List<string>();
        public SearchStatistics Statistics { get; set; } = new SearchStatistics();
    }

    public class PaginationViewModel
    {
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalItems { get; set; }
        public int PageSize { get; set; }
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    public class SearchStatistics
    {
        public int TotalFound { get; set; }
        public int ForSale { get; set; }
        public int ForRent { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public decimal? AveragePrice { get; set; }
    }

    public enum PropertySortBy
    {
        CreatedDate = 1,
        Price = 2,
        Area = 3,
        Title = 4,
        City = 5,
        Bedrooms = 6
    }

    public enum SortDirection
    {
        Ascending = 1,
        Descending = 2
    }

    public enum ViewMode
    {
        Grid = 1,
        List = 2,
        Map = 3
    }
}
