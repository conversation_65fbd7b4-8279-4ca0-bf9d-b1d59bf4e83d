<?php
session_start();

// Dữ liệu sản phẩm mẫu
$products = [
    ['id' => 1, 'name' => 'Paracetamol 500mg', 'price' => 3000, 'description' => 'Thuốc giảm đau, hạ sốt', 'prescription' => false, 'stock' => 500],
    ['id' => 2, 'name' => 'Amoxicillin 250mg', 'price' => 12000, 'description' => 'Kháng sinh điều trị nhiễm khuẩn', 'prescription' => true, 'stock' => 200],
    ['id' => 3, 'name' => 'Vitamin C 1000mg', 'price' => 25000, 'description' => 'Bổ sung vitamin C', 'prescription' => false, 'stock' => 1000],
    ['id' => 4, 'name' => 'Nhiệt kế điện tử', 'price' => 150000, 'description' => 'Đo nhiệt độ chính xác', 'prescription' => false, 'stock' => 50]
];

// Xử lý thêm vào giỏ hàng
if (isset($_POST['add_to_cart'])) {
    if (!isset($_SESSION['cart'])) $_SESSION['cart'] = [];
    $product_id = (int)$_POST['product_id'];
    $quantity = (int)$_POST['quantity'];
    
    if (isset($_SESSION['cart'][$product_id])) {
        $_SESSION['cart'][$product_id] += $quantity;
    } else {
        $_SESSION['cart'][$product_id] = $quantity;
    }
    $message = "Đã thêm sản phẩm vào giỏ hàng!";
}

$cart_count = isset($_SESSION['cart']) ? array_sum($_SESSION['cart']) : 0;
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhà Thuốc An Khang - Website Dược Phẩm</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2E8B57;
            --secondary-color: #F0F8FF;
            --accent-color: #FF6347;
        }
        
        .navbar-brand { font-weight: bold; color: var(--primary-color) !important; }
        .hero-section { background: linear-gradient(135deg, var(--primary-color), #20B2AA); color: white; padding: 4rem 0; }
        .product-card { border: 1px solid #e0e0e0; border-radius: 8px; transition: transform 0.3s ease; height: 100%; }
        .product-card:hover { transform: translateY(-5px); box-shadow: 0 5px 20px rgba(0,0,0,0.1); }
        .prescription-badge { background: #ffc107; color: #000; font-size: 0.8rem; padding: 0.2rem 0.5rem; border-radius: 4px; }
        .btn-primary { background: var(--primary-color); border-color: var(--primary-color); }
        .btn-primary:hover { background: #228B22; border-color: #228B22; }
        .cart-badge { background: var(--accent-color); color: white; border-radius: 50%; padding: 0.2rem 0.5rem; font-size: 0.8rem; }
        .category-card { background: var(--secondary-color); border-radius: 8px; padding: 2rem; text-align: center; transition: transform 0.3s ease; }
        .category-card:hover { transform: translateY(-3px); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-plus-circle me-2"></i>Nhà Thuốc An Khang
            </a>
            
            <div class="collapse navbar-collapse">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link active" href="#">Trang chủ</a></li>
                    <li class="nav-item"><a class="nav-link" href="#products">Sản phẩm</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">Giới thiệu</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Liên hệ</a></li>
                </ul>
                
                <div class="d-flex">
                    <form class="d-flex me-3">
                        <input class="form-control me-2" type="search" placeholder="Tìm thuốc...">
                        <button class="btn btn-outline-primary" type="submit"><i class="fas fa-search"></i></button>
                    </form>
                    
                    <a href="cart.php" class="btn btn-outline-primary position-relative">
                        <i class="fas fa-shopping-cart"></i>
                        <?php if ($cart_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle cart-badge"><?php echo $cart_count; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">Sức khỏe là vàng</h1>
                    <p class="lead mb-4">Chúng tôi cung cấp dược phẩm chất lượng cao, dịch vụ tư vấn chuyên nghiệp và giao hàng nhanh chóng.</p>
                    <a href="#products" class="btn btn-light btn-lg">
                        <i class="fas fa-pills me-2"></i>Mua thuốc ngay
                    </a>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-hospital fa-10x text-white opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Danh mục sản phẩm</h2>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="category-card">
                        <i class="fas fa-prescription-bottle-alt fa-3x text-primary mb-3"></i>
                        <h5>Thuốc kê đơn</h5>
                        <p>Thuốc theo đơn bác sĩ</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="category-card">
                        <i class="fas fa-pills fa-3x text-success mb-3"></i>
                        <h5>Thuốc không kê đơn</h5>
                        <p>Thuốc bán tự do</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="category-card">
                        <i class="fas fa-leaf fa-3x text-info mb-3"></i>
                        <h5>Thực phẩm chức năng</h5>
                        <p>Vitamin, khoáng chất</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="category-card">
                        <i class="fas fa-stethoscope fa-3x text-warning mb-3"></i>
                        <h5>Dụng cụ y tế</h5>
                        <p>Thiết bị y tế</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products -->
    <section id="products" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">Sản phẩm nổi bật</h2>
            
            <?php if (isset($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <div class="row g-4">
                <?php foreach ($products as $product): ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="card product-card">
                            <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 200px; background: <?php echo $product['prescription'] ? '#FFC107' : '#28A745'; ?>;">
                                <i class="fas fa-pills fa-4x text-white"></i>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $product['name']; ?></h5>
                                <p class="card-text text-muted"><?php echo $product['description']; ?></p>
                                
                                <?php if ($product['prescription']): ?>
                                    <span class="prescription-badge mb-2 d-inline-block">
                                        <i class="fas fa-prescription"></i> Kê đơn
                                    </span>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-primary mb-0"><?php echo number_format($product['price']); ?>₫</span>
                                    <small class="text-muted">Còn: <?php echo $product['stock']; ?></small>
                                </div>
                                
                                <form method="POST" class="mt-3">
                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                    <div class="input-group mb-2">
                                        <input type="number" name="quantity" value="1" min="1" max="<?php echo $product['stock']; ?>" class="form-control form-control-sm">
                                        <button type="submit" name="add_to_cart" class="btn btn-primary btn-sm">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Tại sao chọn chúng tôi?</h2>
            <div class="row g-4">
                <div class="col-md-4 text-center">
                    <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                    <h5>Giao hàng nhanh</h5>
                    <p>Giao hàng trong 2-4 giờ tại TP.HCM</p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-user-md fa-3x text-success mb-3"></i>
                    <h5>Tư vấn chuyên nghiệp</h5>
                    <p>Đội ngũ dược sĩ giàu kinh nghiệm</p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-shield-alt fa-3x text-info mb-3"></i>
                    <h5>Chất lượng đảm bảo</h5>
                    <p>Sản phẩm chính hãng, có nguồn gốc</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>Nhà Thuốc An Khang</h5>
                    <p>Nhà thuốc trực tuyến uy tín, phục vụ sức khỏe cộng đồng.</p>
                </div>
                <div class="col-md-4">
                    <h5>Liên hệ</h5>
                    <p><i class="fas fa-phone me-2"></i>1900-1234</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-map-marker-alt me-2"></i>123 Đường ABC, Quận 1, TP.HCM</p>
                </div>
                <div class="col-md-4">
                    <h5>Theo dõi chúng tôi</h5>
                    <div class="d-flex">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-youtube fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2025 Nhà Thuốc An Khang. Tất cả quyền được bảo lưu.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
