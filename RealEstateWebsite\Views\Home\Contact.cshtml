@model RealEstateWebsite.Models.Contact

@{
    ViewData["Title"] = "Liên <PERSON>";
}

<div class="container my-5">
    <!-- Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary"><PERSON><PERSON><PERSON>ới <PERSON>ún<PERSON>i</h1>
        <p class="lead text-muted">Chúng tôi luôn sẵn sàng hỗ trợ và tư vấn cho bạn</p>
    </div>

    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8 mb-5">
            <div class="card shadow border-0">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-envelope"></i> <PERSON><PERSON><PERSON></h4>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Contact" method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FullName" class="form-label">Họ và tên *</label>
                                <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên">
                                <span asp-validation-for="FullName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email *</label>
                                <input asp-for="Email" class="form-control" placeholder="Nhập email">
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Phone" class="form-label">Số điện thoại *</label>
                                <input asp-for="Phone" class="form-control" placeholder="Nhập số điện thoại">
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Type" class="form-label">Loại liên hệ</label>
                                <select asp-for="Type" class="form-select">
                                    <option value="1">Liên hệ chung</option>
                                    <option value="2">Hỏi về bất động sản</option>
                                    <option value="3">Tư vấn</option>
                                    <option value="4">Khiếu nại</option>
                                    <option value="5">Góp ý</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Subject" class="form-label">Tiêu đề</label>
                            <input asp-for="Subject" class="form-control" placeholder="Nhập tiêu đề">
                            <span asp-validation-for="Subject" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Message" class="form-label">Nội dung tin nhắn *</label>
                            <textarea asp-for="Message" class="form-control" rows="5" placeholder="Nhập nội dung tin nhắn..."></textarea>
                            <span asp-validation-for="Message" class="text-danger"></span>
                        </div>
                        
                        <!-- Additional Fields for Property Inquiry -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="BudgetMin" class="form-label">Ngân sách từ (VNĐ)</label>
                                <select asp-for="BudgetMin" class="form-select">
                                    <option value="">Không giới hạn</option>
                                    <option value="500000000">500 triệu</option>
                                    <option value="1000000000">1 tỷ</option>
                                    <option value="2000000000">2 tỷ</option>
                                    <option value="5000000000">5 tỷ</option>
                                    <option value="10000000000">10 tỷ</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="BudgetMax" class="form-label">Ngân sách đến (VNĐ)</label>
                                <select asp-for="BudgetMax" class="form-select">
                                    <option value="">Không giới hạn</option>
                                    <option value="1000000000">1 tỷ</option>
                                    <option value="2000000000">2 tỷ</option>
                                    <option value="5000000000">5 tỷ</option>
                                    <option value="10000000000">10 tỷ</option>
                                    <option value="20000000000">20 tỷ</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="PreferredLocation" class="form-label">Khu vực mong muốn</label>
                                <input asp-for="PreferredLocation" class="form-control" placeholder="VD: Quận 1, TP.HCM">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="PreferredContactTime" class="form-label">Thời gian liên hệ</label>
                                <select asp-for="PreferredContactTime" class="form-select">
                                    <option value="">Bất kỳ lúc nào</option>
                                    <option value="Sáng (8h-12h)">Sáng (8h-12h)</option>
                                    <option value="Chiều (13h-17h)">Chiều (13h-17h)</option>
                                    <option value="Tối (18h-21h)">Tối (18h-21h)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> Gửi Tin Nhắn
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-4">
            <!-- Contact Details -->
            <div class="card shadow border-0 mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Thông Tin Liên Hệ</h5>
                </div>
                <div class="card-body">
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-primary text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Địa chỉ</h6>
                                <p class="text-muted mb-0">123 Đường ABC, Quận 1, TP.HCM</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-success text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Hotline</h6>
                                <p class="text-muted mb-0">1900-1234</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-warning text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Email</h6>
                                <p class="text-muted mb-0"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-info text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Giờ làm việc</h6>
                                <p class="text-muted mb-0">T2-T7: 8:00 - 18:00<br>CN: 8:00 - 12:00</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media -->
            <div class="card shadow border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-share-alt"></i> Kết Nối Với Chúng Tôi</h5>
                </div>
                <div class="card-body text-center">
                    <div class="d-flex justify-content-center gap-3">
                        <a href="#" class="btn btn-primary btn-lg rounded-circle" style="width: 50px; height: 50px;">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-danger btn-lg rounded-circle" style="width: 50px; height: 50px;">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="btn btn-info btn-lg rounded-circle" style="width: 50px; height: 50px;">
                            <i class="fab fa-zalo"></i>
                        </a>
                        <a href="#" class="btn btn-success btn-lg rounded-circle" style="width: 50px; height: 50px;">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card shadow border-0">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Mẹo Hữu Ích</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Chuẩn bị đầy đủ thông tin về nhu cầu
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Xác định rõ ngân sách và khu vực
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Liên hệ trong giờ hành chính để được hỗ trợ nhanh nhất
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            Cung cấp số điện thoại để được tư vấn trực tiếp
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Section -->
    <div class="mt-5">
        <div class="card shadow border-0">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-map"></i> Vị Trí Văn Phòng</h5>
            </div>
            <div class="card-body p-0">
                <div class="ratio ratio-21x9">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.*************!2d106.69975131533414!3d10.776530192318146!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f4b3332a4b9%3A0x5a981a5efee9fd7d!2zUXXhuq1uIDEsIFRow6BuaCBwaOG7kSBI4buTIENow60gTWluaCwgVmnhu4d0IE5hbQ!5e0!3m2!1svi!2s!4v1642678901234!5m2!1svi!2s" 
                            style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
