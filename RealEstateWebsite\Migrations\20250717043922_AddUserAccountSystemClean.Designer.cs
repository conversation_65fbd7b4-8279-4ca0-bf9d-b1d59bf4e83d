﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using RealEstateWebsite.Data;

#nullable disable

namespace RealEstateWebsite.Migrations
{
    [DbContext(typeof(RealEstateContext))]
    [Migration("20250717043922_AddUserAccountSystemClean")]
    partial class AddUserAccountSystemClean
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("RealEstateWebsite.Models.Category", b =>
                {
                    b.Property<int>("CategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Icon")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Image")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Slug")
                        .HasMaxLength(150)
                        .HasColumnType("TEXT");

                    b.HasKey("CategoryId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Slug")
                        .IsUnique();

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Contact", b =>
                {
                    b.Property<int>("ContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AdminNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("BudgetMax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BudgetMin")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("InterestedPropertyType")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("PreferredContactTime")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PreferredLocation")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Response")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("ResponseBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ResponseDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Subject")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("ContactId");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("Email");

                    b.HasIndex("PropertyId");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Favorite", b =>
                {
                    b.Property<int>("FavoriteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("FavoriteId");

                    b.HasIndex("SessionId");

                    b.HasIndex("PropertyId", "SessionId")
                        .IsUnique();

                    b.ToTable("Favorites");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Property", b =>
                {
                    b.Property<int>("PropertyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<double>("Area")
                        .HasColumnType("decimal(10,2)");

                    b.Property<int>("Bathrooms")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Bedrooms")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("District")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("Floors")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Gallery")
                        .HasColumnType("TEXT");

                    b.Property<bool>("HasAirConditioning")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasBalcony")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasElevator")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasGarden")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasParking")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasSecurity")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("HasSwimmingPool")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsAvailable")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("INTEGER");

                    b.Property<double?>("Latitude")
                        .HasColumnType("decimal(10,8)");

                    b.Property<string>("LegalStatus")
                        .HasColumnType("TEXT");

                    b.Property<double?>("Longitude")
                        .HasColumnType("decimal(11,8)");

                    b.Property<string>("MainImage")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Orientation")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("VirtualTourUrl")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Ward")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("YearBuilt")
                        .HasColumnType("INTEGER");

                    b.HasKey("PropertyId");

                    b.HasIndex("Area");

                    b.HasIndex("CategoryId");

                    b.HasIndex("City");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("District");

                    b.HasIndex("Price");

                    b.HasIndex("Title");

                    b.ToTable("Properties");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.PropertyImage", b =>
                {
                    b.Property<int>("PropertyImageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsMain")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("PropertyImageId");

                    b.HasIndex("PropertyId");

                    b.ToTable("PropertyImages");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Avatar")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Company")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("EmailVerificationExpiry")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmailVerificationToken")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FacebookUrl")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("Gender")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("LinkedInUrl")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Occupation")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PasswordResetExpiry")
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordResetToken")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PreferredBudgetMax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PreferredBudgetMin")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PreferredLocation")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("PreferredPropertyType")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Role")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityAnswerHash")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("SecurityQuestion")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("UserId");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Contact", b =>
                {
                    b.HasOne("RealEstateWebsite.Models.Property", "Property")
                        .WithMany("Contacts")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Property");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Favorite", b =>
                {
                    b.HasOne("RealEstateWebsite.Models.Property", "Property")
                        .WithMany("Favorites")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Property");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Property", b =>
                {
                    b.HasOne("RealEstateWebsite.Models.Category", "Category")
                        .WithMany("Properties")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.PropertyImage", b =>
                {
                    b.HasOne("RealEstateWebsite.Models.Property", "Property")
                        .WithMany("Images")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Property");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Category", b =>
                {
                    b.Navigation("Properties");
                });

            modelBuilder.Entity("RealEstateWebsite.Models.Property", b =>
                {
                    b.Navigation("Contacts");

                    b.Navigation("Favorites");

                    b.Navigation("Images");
                });
#pragma warning restore 612, 618
        }
    }
}
