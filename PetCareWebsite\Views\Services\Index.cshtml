@model IEnumerable<PetCareWebsite.Models.Service>
@{
    ViewData["Title"] = "Dịch Vụ <PERSON>m <PERSON>óc <PERSON>";
}

<div class="container">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold gradient-text mb-3 floating">
                💖 Dịch Vụ Chăm Sóc Thú Cưng
            </h1>
            <p class="lead text-muted">
                Chúng tôi cung cấp đầy đủ các dịch vụ chăm sóc sức khỏe và làm đẹp cho thú cưng của bạn
            </p>
        </div>
    </div>

    <!-- Services Grid -->
    <div class="row g-4">
        @{
            var serviceEmojis = new[] { "🛁", "✂️", "🩺", "💉", "🧖‍♀️", "🦷", "💅", "🐛", "🥗", "👨‍⚕️", "🏥", "💊" };
            var gradients = new[] { "bg-gradient-primary", "bg-gradient-success", "bg-gradient-warning", "bg-gradient-info", "bg-gradient-cute", "bg-gradient-pet" };
        }
        @foreach (var (service, index) in Model.Select((s, i) => (s, i)))
        {
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm service-card glass-effect">
                    <div class="card-img-top @gradients[index % gradients.Length] d-flex align-items-center justify-content-center text-white" style="height: 200px;">
                        <div class="text-center">
                            <span style="font-size: 4rem;" class="floating">@serviceEmojis[index % serviceEmojis.Length]</span>
                            <h6 class="mb-0 mt-2 fw-bold">@service.Name</h6>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title gradient-text mb-3">@service.Name</h5>
                        <p class="card-text text-muted flex-grow-1">@service.Description</p>

                        <div class="service-info mb-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="glass-effect p-3 rounded-4">
                                        <span style="font-size: 1.5rem;">💰</span>
                                        <div class="fw-bold text-success">@service.Price.ToString("N0") VNĐ</div>
                                        <small class="text-muted">Giá dịch vụ</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="glass-effect p-3 rounded-4">
                                        <span style="font-size: 1.5rem;">⏱️</span>
                                        <div class="fw-bold text-info">@service.DurationMinutes phút</div>
                                        <small class="text-muted">Thời gian</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <div class="d-grid gap-2">
                            <a href="@Url.Action("Details", "Services", new { id = service.Id })" class="btn btn-outline-primary">
                                <i class="fas fa-info-circle me-2"></i>Xem Chi Tiết
                            </a>
                            @if (User.Identity?.IsAuthenticated == true)
                            {
                                <a href="@Url.Action("Create", "Appointments", new { serviceId = service.Id })" class="btn btn-primary">
                                    <i class="fas fa-calendar-plus me-2"></i>Đặt Lịch Hẹn
                                </a>
                            }
                            else
                            {
                                <a href="@Url.Action("Login", "Account")" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Đăng Nhập Để Đặt Lịch
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    @if (!Model.Any())
    {
        <div class="row">
            <div class="col-12 text-center py-5">
                <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Hiện tại chưa có dịch vụ nào</h4>
                <p class="text-muted">Vui lòng quay lại sau để xem các dịch vụ mới</p>
            </div>
        </div>
    }
</div>

<style>
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
</style>
