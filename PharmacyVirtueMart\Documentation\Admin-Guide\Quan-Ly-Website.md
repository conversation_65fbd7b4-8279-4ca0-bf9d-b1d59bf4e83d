# Hướng Dẫn Quản Lý Website Dược Phẩm VirtueMart

## 1. Đ<PERSON><PERSON> nhập hệ thống quản trị

### Truy cập Admin Panel:
- **URL**: http://yoursite.com/administrator
- **Username**: admin (hoặc tài khoản đã tạo)
- **Password**: [mật khẩu admin]

### Dashboard chính:
- Tổng quan thống kê bán hàng
- Đơn hàng mới cần xử lý
- Sản phẩm sắp hết hàng
- Cảnh báo hạn sử dụng

## 2. Quản lý danh mục sản phẩm

### Tạo danh mục mới:
1. **Components** → **VirtueMart** → **Categories**
2. Click **New** để tạo danh mục mới
3. Điền thông tin:
   - **Category Name**: <PERSON><PERSON><PERSON> danh mục (VD: <PERSON>huốc kê đơn)
   - **Category Description**: <PERSON><PERSON> tả chi tiết
   - **SEF <PERSON>**: URL thân thiện
   - **Published**: Bật để hiển thị

### Danh mục dược phẩm chính:
```
📁 Thuốc kê đơn
  ├── Kháng sinh
  ├── Thuốc tim mạch
  ├── Thuốc tiêu hóa
  └── Thuốc thần kinh

📁 Thuốc không kê đơn
  ├── Thuốc giảm đau
  ├── Thuốc cảm cúm
  ├── Thuốc dạ dày
  └── Thuốc ngoài da

📁 Thực phẩm chức năng
  ├── Vitamin & Khoáng chất
  ├── Thảo dược
  └── Dinh dưỡng trẻ em

📁 Dụng cụ y tế
  ├── Thiết bị đo
  ├── Băng gạc
  └── Dụng cụ sơ cứu
```

### Cấu hình danh mục:
- **Category Layout**: Chọn layout hiển thị
- **Products per Row**: Số sản phẩm trên 1 hàng
- **Category Image**: Hình ảnh đại diện
- **Meta Description**: Mô tả SEO

## 3. Quản lý sản phẩm

### Thêm sản phẩm mới:
1. **Components** → **VirtueMart** → **Products**
2. Click **New** để tạo sản phẩm
3. **Product Information** tab:
   - **Product Name**: Tên sản phẩm
   - **Product SKU**: Mã sản phẩm duy nhất
   - **Product Short Description**: Mô tả ngắn
   - **Product Description**: Mô tả chi tiết

### Thông tin dược phẩm bổ sung:
```
📋 Thông tin cơ bản:
- Số đăng ký: VN-12345-20
- Hoạt chất: Paracetamol 500mg
- Dạng bào chế: Viên nén
- Quy cách đóng gói: Hộp 10 vỉ x 10 viên

📋 Thông tin y khoa:
- Chỉ định: Giảm đau, hạ sốt
- Chống chỉ định: Dị ứng paracetamol
- Tác dụng phụ: Buồn nôn, phát ban
- Liều dùng: 1-2 viên/lần, 3-4 lần/ngày

📋 Bảo quản:
- Điều kiện: Nơi khô ráo, tránh ánh sáng
- Hạn sử dụng: 24 tháng
- Nhiệt độ: Dưới 30°C
```

### Cấu hình giá bán:
1. **Price** tab:
   - **Product Price**: Giá bán lẻ
   - **Cost Price**: Giá vốn
   - **Tax**: Thuế VAT (10%)
   - **Currency**: VND

### Quản lý hình ảnh:
1. **Images** tab:
   - **Product Image**: Hình chính
   - **Additional Images**: Hình bổ sung
   - **Image Alt Text**: Mô tả hình ảnh

### Quản lý tồn kho:
1. **Inventory** tab:
   - **Product in Stock**: Số lượng tồn
   - **Low Stock Notification**: Cảnh báo hết hàng
   - **Track Inventory**: Theo dõi tồn kho

## 4. Quản lý đơn hàng

### Xem danh sách đơn hàng:
1. **Components** → **VirtueMart** → **Orders**
2. Các trạng thái đơn hàng:
   - **Pending**: Chờ xử lý
   - **Confirmed**: Đã xác nhận
   - **Shipped**: Đã giao hàng
   - **Delivered**: Đã nhận hàng
   - **Cancelled**: Đã hủy

### Xử lý đơn hàng:
1. Click vào đơn hàng cần xử lý
2. Kiểm tra thông tin:
   - Thông tin khách hàng
   - Danh sách sản phẩm
   - Tổng tiền thanh toán
   - Địa chỉ giao hàng

3. Cập nhật trạng thái:
   - Chọn trạng thái mới
   - Thêm ghi chú nếu cần
   - Gửi email thông báo

### Xử lý đơn thuốc kê đơn:
```
✅ Kiểm tra đơn thuốc:
- Xác thực đơn thuốc hợp lệ
- Kiểm tra chữ ký bác sĩ
- Đối chiếu thông tin bệnh nhân

✅ Kiểm tra tồn kho:
- Đảm bảo đủ số lượng
- Kiểm tra hạn sử dụng
- Xác nhận batch phù hợp

✅ Chuẩn bị thuốc:
- Đóng gói theo đơn
- Dán nhãn hướng dẫn sử dụng
- Kiểm tra chất lượng
```

## 5. Quản lý khách hàng

### Xem danh sách khách hàng:
1. **Components** → **VirtueMart** → **Shoppers**
2. Thông tin khách hàng:
   - Thông tin cá nhân
   - Lịch sử mua hàng
   - Địa chỉ giao hàng
   - Phương thức thanh toán

### Tạo tài khoản khách hàng:
1. Click **New** để tạo mới
2. Điền thông tin:
   - **First Name**: Tên
   - **Last Name**: Họ
   - **Email**: Email đăng nhập
   - **Phone**: Số điện thoại
   - **Address**: Địa chỉ

### Quản lý nhóm khách hàng:
- **Retail**: Khách lẻ
- **Wholesale**: Khách sỉ
- **VIP**: Khách VIP
- **Healthcare**: Cơ sở y tế

## 6. Báo cáo và thống kê

### Báo cáo bán hàng:
1. **Components** → **VirtueMart** → **Statistics**
2. Các loại báo cáo:
   - Doanh thu theo ngày/tháng
   - Sản phẩm bán chạy
   - Khách hàng mua nhiều
   - Lợi nhuận theo danh mục

### Báo cáo tồn kho:
```sql
-- Sản phẩm sắp hết hàng
CALL CheckLowStock();

-- Sản phẩm sắp hết hạn (30 ngày)
CALL CheckExpiringProducts(30);
```

### Báo cáo dược phẩm:
- Thuốc kê đơn bán nhiều
- Cảnh báo tương tác thuốc
- Thống kê theo nhà sản xuất
- Báo cáo hạn sử dụng

## 7. Cấu hình hệ thống

### Cài đặt shop:
1. **Components** → **VirtueMart** → **Configuration**
2. **Shop** tab:
   - **Shop Name**: Tên cửa hàng
   - **Company**: Thông tin công ty
   - **Shop Email**: Email liên hệ
   - **Currency**: Đơn vị tiền tệ

### Cấu hình thanh toán:
1. **Payment Methods** tab:
   - **Cash on Delivery**: Thanh toán khi nhận hàng
   - **Bank Transfer**: Chuyển khoản
   - **Credit Card**: Thẻ tín dụng
   - **E-wallet**: Ví điện tử

### Cấu hình vận chuyển:
1. **Shipment Methods** tab:
   - **Standard Shipping**: Giao hàng tiêu chuẩn
   - **Express Shipping**: Giao hàng nhanh
   - **Free Shipping**: Miễn phí vận chuyển
   - **Pickup**: Nhận tại cửa hàng

## 8. Bảo trì và sao lưu

### Sao lưu dữ liệu:
```bash
# Sao lưu database
mysqldump -u username -p pharmacy_virtuemart > backup_$(date +%Y%m%d).sql

# Sao lưu files
tar -czf website_backup_$(date +%Y%m%d).tar.gz /path/to/joomla/
```

### Cập nhật hệ thống:
1. Sao lưu trước khi cập nhật
2. Cập nhật Joomla core
3. Cập nhật VirtueMart
4. Kiểm tra tính năng sau cập nhật

### Bảo mật:
- Thay đổi mật khẩu admin định kỳ
- Cập nhật phần mềm thường xuyên
- Sử dụng SSL certificate
- Giới hạn quyền truy cập

## 9. Xử lý sự cố thường gặp

### Lỗi hiển thị sản phẩm:
- Kiểm tra cache
- Xóa cache Joomla
- Kiểm tra quyền file

### Lỗi thanh toán:
- Kiểm tra cấu hình payment gateway
- Xem log lỗi
- Liên hệ nhà cung cấp dịch vụ

### Lỗi email:
- Kiểm tra cấu hình SMTP
- Test gửi email
- Kiểm tra spam folder

## Kết luận
Việc quản lý website dược phẩm đòi hỏi sự chính xác và tuân thủ quy định. 
Luôn kiểm tra kỹ thông tin sản phẩm và đơn hàng trước khi xử lý.
