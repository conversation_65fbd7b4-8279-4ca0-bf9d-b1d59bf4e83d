namespace RealEstateWebsite.Models.ViewModels
{
    public class HomeViewModel
    {
        public List<Property> FeaturedProperties { get; set; } = new List<Property>();
        public List<Property> LatestProperties { get; set; } = new List<Property>();
        public List<Category> Categories { get; set; } = new List<Category>();
        public PropertySearchViewModel SearchForm { get; set; } = new PropertySearchViewModel();
        public HomeStatistics Statistics { get; set; } = new HomeStatistics();
    }

    public class HomeStatistics
    {
        public int TotalProperties { get; set; }
        public int PropertiesForSale { get; set; }
        public int PropertiesForRent { get; set; }
        public int SoldProperties { get; set; }
        public int HappyCustomers { get; set; }
        public int YearsExperience { get; set; }
    }
}
