// Global variables
let array = [];
let arraySize = 50;
let sortSpeed = 5;
let isSorting = false;
let isPaused = false;
let sortingTimeoutId = null;
let comparisons = 0;
let swaps = 0;
let startTime = 0;
let students = [];

// Algorithm information
const algorithmInfo = {
    bubble: {
        name: "Bubble Sort",
        timeComplexity: "O(n²)",
        spaceComplexity: "O(1)",
        stability: "Có",
        description: "Bubble Sort so sánh các cặp phần tử liền kề và hoán đổi chúng nếu chúng không đúng thứ tự. Quá trình này được lặp lại cho đến khi mảng được sắp xếp hoàn toàn.",
        code: `// Bubble Sort Implementation
function bubbleSort(arr) {
    let n = arr.length;
    for (let i = 0; i < n - 1; i++) {
        for (let j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                // Swap elements
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
            }
        }
    }
    return arr;
}`
    },
    selection: {
        name: "Selection Sort",
        timeComplexity: "O(n²)",
        spaceComplexity: "O(1)",
        stability: "Không",
        description: "Selection Sort tìm phần tử nhỏ nhất trong mảng chưa sắp xếp và đặt nó vào vị trí đầu tiên. Quá trình này được lặp lại cho phần còn lại của mảng.",
        code: `// Selection Sort Implementation
function selectionSort(arr) {
    let n = arr.length;
    for (let i = 0; i < n - 1; i++) {
        let minIdx = i;
        for (let j = i + 1; j < n; j++) {
            if (arr[j] < arr[minIdx]) {
                minIdx = j;
            }
        }
        if (minIdx !== i) {
            [arr[i], arr[minIdx]] = [arr[minIdx], arr[i]];
        }
    }
    return arr;
}`
    },
    insertion: {
        name: "Insertion Sort",
        timeComplexity: "O(n²)",
        spaceComplexity: "O(1)",
        stability: "Có",
        description: "Insertion Sort xây dựng mảng đã sắp xếp từng phần tử một. Nó lấy một phần tử từ danh sách chưa sắp xếp và chèn nó vào vị trí đúng trong phần đã sắp xếp.",
        code: `// Insertion Sort Implementation
function insertionSort(arr) {
    for (let i = 1; i < arr.length; i++) {
        let key = arr[i];
        let j = i - 1;
        while (j >= 0 && arr[j] > key) {
            arr[j + 1] = arr[j];
            j--;
        }
        arr[j + 1] = key;
    }
    return arr;
}`
    },
    quick: {
        name: "Quick Sort",
        timeComplexity: "O(n log n)",
        spaceComplexity: "O(log n)",
        stability: "Không",
        description: "Quick Sort sử dụng chiến lược 'chia để trị'. Nó chọn một phần tử làm pivot, phân chia mảng thành hai phần và đệ quy sắp xếp từng phần.",
        code: `// Quick Sort Implementation
function quickSort(arr, low = 0, high = arr.length - 1) {
    if (low < high) {
        let pi = partition(arr, low, high);
        quickSort(arr, low, pi - 1);
        quickSort(arr, pi + 1, high);
    }
    return arr;
}

function partition(arr, low, high) {
    let pivot = arr[high];
    let i = low - 1;
    for (let j = low; j < high; j++) {
        if (arr[j] < pivot) {
            i++;
            [arr[i], arr[j]] = [arr[j], arr[i]];
        }
    }
    [arr[i + 1], arr[high]] = [arr[high], arr[i + 1]];
    return i + 1;
}`
    },
    merge: {
        name: "Merge Sort",
        timeComplexity: "O(n log n)",
        spaceComplexity: "O(n)",
        stability: "Có",
        description: "Merge Sort chia mảng thành hai nửa, đệ quy sắp xếp từng nửa, sau đó trộn hai nửa đã sắp xếp lại với nhau.",
        code: `// Merge Sort Implementation
function mergeSort(arr) {
    if (arr.length <= 1) return arr;
    
    const mid = Math.floor(arr.length / 2);
    const left = mergeSort(arr.slice(0, mid));
    const right = mergeSort(arr.slice(mid));
    
    return merge(left, right);
}

function merge(left, right) {
    let result = [];
    let i = 0, j = 0;
    
    while (i < left.length && j < right.length) {
        if (left[i] <= right[j]) {
            result.push(left[i]);
            i++;
        } else {
            result.push(right[j]);
            j++;
        }
    }
    
    return result.concat(left.slice(i)).concat(right.slice(j));
}`
    }
};

// DOM elements
const arraySizeSlider = document.getElementById('arraySize');
const arraySizeValue = document.getElementById('arraySizeValue');
const sortSpeedSlider = document.getElementById('sortSpeed');
const sortSpeedValue = document.getElementById('sortSpeedValue');
const algorithmSelect = document.getElementById('algorithmSelect');
const generateBtn = document.getElementById('generateBtn');
const sortBtn = document.getElementById('sortBtn');
const pauseBtn = document.getElementById('pauseBtn');
const resetBtn = document.getElementById('resetBtn');
const arrayContainer = document.getElementById('arrayContainer');
const comparisonsSpan = document.getElementById('comparisons');
const swapsSpan = document.getElementById('swaps');
const timeElapsedSpan = document.getElementById('timeElapsed');
const statusSpan = document.getElementById('status');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    generateArray();
    updateAlgorithmInfo();
    
    // Event listeners
    arraySizeSlider.addEventListener('input', function() {
        arraySize = parseInt(this.value);
        arraySizeValue.textContent = arraySize;
        if (!isSorting) generateArray();
    });
    
    sortSpeedSlider.addEventListener('input', function() {
        sortSpeed = parseInt(this.value);
        sortSpeedValue.textContent = sortSpeed;
    });
    
    algorithmSelect.addEventListener('change', updateAlgorithmInfo);
    generateBtn.addEventListener('click', generateArray);
    sortBtn.addEventListener('click', startSorting);
    pauseBtn.addEventListener('click', pauseSorting);
    resetBtn.addEventListener('click', resetSorting);
    
    // Student demo
    document.getElementById('generateStudentsBtn').addEventListener('click', generateStudents);
    document.getElementById('sortStudentsBtn').addEventListener('click', sortStudents);
    
    // Comparison
    document.getElementById('compareAllBtn').addEventListener('click', compareAllAlgorithms);
});

// Generate random array
function generateArray() {
    array = [];
    for (let i = 0; i < arraySize; i++) {
        array.push(Math.floor(Math.random() * 300) + 10);
    }
    renderArray();
    resetStats();
    updateStatus('Sẵn sàng');
}

// Render array visualization
function renderArray() {
    arrayContainer.innerHTML = '';
    const containerWidth = arrayContainer.clientWidth;
    const barWidth = Math.max(8, (containerWidth - arraySize * 2) / arraySize);
    
    array.forEach((value, index) => {
        const bar = document.createElement('div');
        bar.className = 'array-bar';
        bar.style.height = `${value}px`;
        bar.style.width = `${barWidth}px`;
        bar.textContent = arraySize <= 20 ? value : '';
        bar.id = `bar-${index}`;
        arrayContainer.appendChild(bar);
    });
}

// Update algorithm information
function updateAlgorithmInfo() {
    const selectedAlgorithm = algorithmSelect.value;
    const info = algorithmInfo[selectedAlgorithm];
    
    document.getElementById('timeComplexity').textContent = info.timeComplexity;
    document.getElementById('spaceComplexity').textContent = info.spaceComplexity;
    document.getElementById('stability').textContent = info.stability;
    document.getElementById('algorithmDescription').textContent = info.description;
    document.getElementById('algorithmCode').textContent = info.code;
}

// Reset statistics
function resetStats() {
    comparisons = 0;
    swaps = 0;
    startTime = 0;
    updateStats();
}

// Update statistics display
function updateStats() {
    comparisonsSpan.textContent = comparisons;
    swapsSpan.textContent = swaps;
    if (startTime > 0) {
        const elapsed = Date.now() - startTime;
        timeElapsedSpan.textContent = `${elapsed}ms`;
    }
}

// Update status
function updateStatus(status) {
    statusSpan.textContent = status;
}

// Start sorting
async function startSorting() {
    if (isSorting) return;

    isSorting = true;
    isPaused = false;
    startTime = Date.now();
    
    sortBtn.disabled = true;
    pauseBtn.disabled = false;
    generateBtn.disabled = true;
    
    updateStatus('Đang sắp xếp...');
    
    const selectedAlgorithm = algorithmSelect.value;
    
    try {
        switch (selectedAlgorithm) {
            case 'bubble':
                await bubbleSortVisualization();
                break;
            case 'selection':
                await selectionSortVisualization();
                break;
            case 'insertion':
                await insertionSortVisualization();
                break;
            case 'quick':
                await quickSortVisualization(0, array.length - 1);
                break;
            case 'merge':
                await mergeSortVisualization(0, array.length - 1);
                break;
        }
        
        if (!isPaused) {
            updateStatus('Hoàn thành');
            markAllSorted();
        }
    } catch (error) {
        console.error('Sorting error:', error);
        updateStatus('Lỗi');
    }
    
    isSorting = false;
    sortBtn.disabled = false;
    pauseBtn.disabled = true;
    generateBtn.disabled = false;
    updateStats();
}

// Pause sorting
function pauseSorting() {
    isPaused = true;
    updateStatus('Tạm dừng');
    sortBtn.disabled = false;
    pauseBtn.disabled = true;
}

// Reset sorting
function resetSorting() {
    isPaused = true;
    isSorting = false;
    
    if (sortingTimeoutId) {
        clearTimeout(sortingTimeoutId);
        sortingTimeoutId = null;
    }
    
    generateArray();
    
    sortBtn.disabled = false;
    pauseBtn.disabled = true;
    generateBtn.disabled = false;
}

// Delay function for animation
function delay(ms) {
    return new Promise(resolve => {
        sortingTimeoutId = setTimeout(resolve, ms);
    });
}

// Get delay based on speed
function getDelay() {
    return Math.max(10, 500 - (sortSpeed * 45));
}

// Highlight bars
function highlightBars(indices, className) {
    // Remove previous highlights
    document.querySelectorAll('.array-bar').forEach(bar => {
        bar.classList.remove('comparing', 'swapping', 'sorted', 'pivot');
    });
    
    // Add new highlights
    indices.forEach(index => {
        const bar = document.getElementById(`bar-${index}`);
        if (bar) {
            bar.classList.add(className);
        }
    });
}

// Mark bars as sorted
function markSorted(indices) {
    indices.forEach(index => {
        const bar = document.getElementById(`bar-${index}`);
        if (bar) {
            bar.classList.add('sorted');
        }
    });
}

// Mark all bars as sorted
function markAllSorted() {
    document.querySelectorAll('.array-bar').forEach(bar => {
        bar.classList.add('sorted');
    });
}

// Swap array elements and update visualization
async function swapElements(i, j) {
    if (isPaused) throw new Error('Paused');
    
    [array[i], array[j]] = [array[j], array[i]];
    swaps++;
    
    highlightBars([i, j], 'swapping');
    
    // Update bar heights
    const bar1 = document.getElementById(`bar-${i}`);
    const bar2 = document.getElementById(`bar-${j}`);
    
    if (bar1 && bar2) {
        bar1.style.height = `${array[i]}px`;
        bar2.style.height = `${array[j]}px`;
        
        if (arraySize <= 20) {
            bar1.textContent = array[i];
            bar2.textContent = array[j];
        }
    }
    
    await delay(getDelay());
    updateStats();
}

// Compare elements
async function compareElements(i, j) {
    if (isPaused) throw new Error('Paused');

    comparisons++;
    highlightBars([i, j], 'comparing');
    await delay(getDelay() / 2);
    updateStats();

    return array[i] > array[j];
}

// Bubble Sort Visualization
async function bubbleSortVisualization() {
    const n = array.length;

    for (let i = 0; i < n - 1; i++) {
        let swapped = false;

        for (let j = 0; j < n - i - 1; j++) {
            if (isPaused) throw new Error('Paused');

            const shouldSwap = await compareElements(j, j + 1);

            if (shouldSwap) {
                await swapElements(j, j + 1);
                swapped = true;
            }
        }

        // Mark the last element as sorted
        markSorted([n - i - 1]);

        if (!swapped) break; // Optimization: if no swaps, array is sorted
    }

    // Mark remaining elements as sorted
    for (let i = 0; i < n; i++) {
        markSorted([i]);
    }
}

// Selection Sort Visualization
async function selectionSortVisualization() {
    const n = array.length;

    for (let i = 0; i < n - 1; i++) {
        let minIdx = i;

        // Highlight current position
        highlightBars([i], 'pivot');
        await delay(getDelay());

        for (let j = i + 1; j < n; j++) {
            if (isPaused) throw new Error('Paused');

            await compareElements(j, minIdx);

            if (array[j] < array[minIdx]) {
                minIdx = j;
            }
        }

        if (minIdx !== i) {
            await swapElements(i, minIdx);
        }

        // Mark current position as sorted
        markSorted([i]);
    }

    // Mark last element as sorted
    markSorted([n - 1]);
}

// Insertion Sort Visualization
async function insertionSortVisualization() {
    const n = array.length;

    // Mark first element as sorted
    markSorted([0]);

    for (let i = 1; i < n; i++) {
        if (isPaused) throw new Error('Paused');

        let key = array[i];
        let j = i - 1;

        // Highlight current element being inserted
        highlightBars([i], 'pivot');
        await delay(getDelay());

        while (j >= 0) {
            if (isPaused) throw new Error('Paused');

            await compareElements(j, i);

            if (array[j] <= key) break;

            // Shift element to the right
            array[j + 1] = array[j];

            // Update visualization
            const bar = document.getElementById(`bar-${j + 1}`);
            if (bar) {
                bar.style.height = `${array[j + 1]}px`;
                if (arraySize <= 20) {
                    bar.textContent = array[j + 1];
                }
            }

            j--;
            await delay(getDelay());
        }

        // Insert the key at correct position
        array[j + 1] = key;

        // Update visualization
        const bar = document.getElementById(`bar-${j + 1}`);
        if (bar) {
            bar.style.height = `${key}px`;
            if (arraySize <= 20) {
                bar.textContent = key;
            }
        }

        // Mark elements up to current position as sorted
        for (let k = 0; k <= i; k++) {
            markSorted([k]);
        }

        await delay(getDelay());
    }
}

// Quick Sort Visualization
async function quickSortVisualization(low, high) {
    if (low < high) {
        if (isPaused) throw new Error('Paused');

        const pi = await partitionVisualization(low, high);

        await quickSortVisualization(low, pi - 1);
        await quickSortVisualization(pi + 1, high);
    }
}

// Partition function for Quick Sort
async function partitionVisualization(low, high) {
    const pivot = array[high];
    let i = low - 1;

    // Highlight pivot
    highlightBars([high], 'pivot');
    await delay(getDelay());

    for (let j = low; j < high; j++) {
        if (isPaused) throw new Error('Paused');

        await compareElements(j, high);

        if (array[j] < pivot) {
            i++;
            if (i !== j) {
                await swapElements(i, j);
            }
        }
    }

    if (i + 1 !== high) {
        await swapElements(i + 1, high);
    }

    // Mark pivot as sorted
    markSorted([i + 1]);

    return i + 1;
}

// Merge Sort Visualization
async function mergeSortVisualization(left, right) {
    if (left < right) {
        if (isPaused) throw new Error('Paused');

        const mid = Math.floor((left + right) / 2);

        await mergeSortVisualization(left, mid);
        await mergeSortVisualization(mid + 1, right);
        await mergeVisualization(left, mid, right);
    }
}

// Merge function for Merge Sort
async function mergeVisualization(left, mid, right) {
    const leftArr = array.slice(left, mid + 1);
    const rightArr = array.slice(mid + 1, right + 1);

    let i = 0, j = 0, k = left;

    while (i < leftArr.length && j < rightArr.length) {
        if (isPaused) throw new Error('Paused');

        comparisons++;

        // Highlight elements being compared
        highlightBars([k], 'comparing');
        await delay(getDelay());

        if (leftArr[i] <= rightArr[j]) {
            array[k] = leftArr[i];
            i++;
        } else {
            array[k] = rightArr[j];
            j++;
        }

        // Update visualization
        const bar = document.getElementById(`bar-${k}`);
        if (bar) {
            bar.style.height = `${array[k]}px`;
            if (arraySize <= 20) {
                bar.textContent = array[k];
            }
        }

        swaps++;
        k++;
        updateStats();
    }

    // Copy remaining elements
    while (i < leftArr.length) {
        if (isPaused) throw new Error('Paused');

        array[k] = leftArr[i];

        const bar = document.getElementById(`bar-${k}`);
        if (bar) {
            bar.style.height = `${array[k]}px`;
            if (arraySize <= 20) {
                bar.textContent = array[k];
            }
        }

        i++;
        k++;
        swaps++;
        await delay(getDelay() / 2);
        updateStats();
    }

    while (j < rightArr.length) {
        if (isPaused) throw new Error('Paused');

        array[k] = rightArr[j];

        const bar = document.getElementById(`bar-${k}`);
        if (bar) {
            bar.style.height = `${array[k]}px`;
            if (arraySize <= 20) {
                bar.textContent = array[k];
            }
        }

        j++;
        k++;
        swaps++;
        await delay(getDelay() / 2);
        updateStats();
    }

    // Mark merged section as sorted if it's the final merge
    if (left === 0 && right === array.length - 1) {
        for (let idx = left; idx <= right; idx++) {
            markSorted([idx]);
        }
    }
}

// Student Management Functions
function generateStudents() {
    const firstNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Võ', 'Vũ', 'Đặng', 'Bùi'];
    const lastNames = ['Văn An', 'Thị Bình', 'Văn Cường', 'Thị Dung', 'Văn Em', 'Thị Phương', 'Văn Giang', 'Thị Hoa', 'Văn Inh', 'Thị Kiều'];

    students = [];

    for (let i = 0; i < 20; i++) {
        const student = {
            id: `SV${String(1001 + i).padStart(4, '0')}`,
            name: `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`,
            score: (Math.random() * 4 + 6).toFixed(2), // Score between 6.00 and 10.00
            year: 1995 + Math.floor(Math.random() * 10) // Year between 1995 and 2004
        };
        students.push(student);
    }

    displayStudents();
}

function displayStudents() {
    const tableBody = document.getElementById('studentTableBody');
    tableBody.innerHTML = '';

    students.forEach((student, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${student.id}</td>
            <td>${student.name}</td>
            <td>${student.score}</td>
            <td>${student.year}</td>
        `;
        tableBody.appendChild(row);
    });
}

function sortStudents() {
    const criteria = document.getElementById('sortCriteria').value;
    const startTime = performance.now();

    // Highlight table during sorting
    const table = document.getElementById('studentTable');
    table.style.opacity = '0.7';

    setTimeout(() => {
        students.sort((a, b) => {
            switch (criteria) {
                case 'id':
                    return a.id.localeCompare(b.id);
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'score':
                    return parseFloat(b.score) - parseFloat(a.score); // Descending order
                case 'year':
                    return a.year - b.year;
                default:
                    return 0;
            }
        });

        const endTime = performance.now();
        const sortTime = (endTime - startTime).toFixed(2);

        displayStudents();

        // Restore table opacity
        table.style.opacity = '1';

        // Show sorting result
        alert(`Đã sắp xếp ${students.length} sinh viên theo ${getCriteriaName(criteria)} trong ${sortTime}ms`);

        // Highlight sorted rows
        const rows = document.querySelectorAll('#studentTableBody tr');
        rows.forEach((row, index) => {
            setTimeout(() => {
                row.classList.add('highlight');
                setTimeout(() => row.classList.remove('highlight'), 1000);
            }, index * 100);
        });

    }, 500); // Simulate processing time
}

function getCriteriaName(criteria) {
    const names = {
        'id': 'Mã sinh viên',
        'name': 'Tên',
        'score': 'Điểm trung bình',
        'year': 'Năm sinh'
    };
    return names[criteria] || criteria;
}

// Algorithm Comparison Functions
async function compareAllAlgorithms() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const comparisonTable = document.getElementById('comparisonTable');
    const tableBody = document.getElementById('comparisonTableBody');

    loadingOverlay.style.display = 'flex';

    // Create test array
    const testArray = [...array];
    const algorithms = ['bubble', 'selection', 'insertion', 'quick', 'merge'];
    const results = [];

    for (const algorithm of algorithms) {
        // Reset array for each test
        array = [...testArray];
        renderArray();

        // Reset stats
        comparisons = 0;
        swaps = 0;

        const startTime = performance.now();

        try {
            // Run algorithm without visualization for speed
            switch (algorithm) {
                case 'bubble':
                    await bubbleSortFast();
                    break;
                case 'selection':
                    await selectionSortFast();
                    break;
                case 'insertion':
                    await insertionSortFast();
                    break;
                case 'quick':
                    await quickSortFast(0, array.length - 1);
                    break;
                case 'merge':
                    await mergeSortFast(0, array.length - 1);
                    break;
            }

            const endTime = performance.now();
            const executionTime = (endTime - startTime).toFixed(2);

            results.push({
                name: algorithmInfo[algorithm].name,
                time: executionTime,
                comparisons: comparisons,
                swaps: swaps,
                complexity: algorithmInfo[algorithm].timeComplexity
            });

        } catch (error) {
            console.error(`Error in ${algorithm}:`, error);
        }

        // Small delay to show progress
        await delay(100);
    }

    // Display results
    tableBody.innerHTML = '';
    results.forEach(result => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${result.name}</strong></td>
            <td>${result.time}ms</td>
            <td>${result.comparisons.toLocaleString()}</td>
            <td>${result.swaps.toLocaleString()}</td>
            <td>${result.complexity}</td>
        `;
        tableBody.appendChild(row);
    });

    // Restore original array
    array = [...testArray];
    renderArray();

    loadingOverlay.style.display = 'none';
    comparisonTable.style.display = 'block';

    // Scroll to comparison table
    comparisonTable.scrollIntoView({ behavior: 'smooth' });
}

// Fast sorting algorithms (without visualization)
async function bubbleSortFast() {
    const n = array.length;
    for (let i = 0; i < n - 1; i++) {
        let swapped = false;
        for (let j = 0; j < n - i - 1; j++) {
            comparisons++;
            if (array[j] > array[j + 1]) {
                [array[j], array[j + 1]] = [array[j + 1], array[j]];
                swaps++;
                swapped = true;
            }
        }
        if (!swapped) break;
    }
}

async function selectionSortFast() {
    const n = array.length;
    for (let i = 0; i < n - 1; i++) {
        let minIdx = i;
        for (let j = i + 1; j < n; j++) {
            comparisons++;
            if (array[j] < array[minIdx]) {
                minIdx = j;
            }
        }
        if (minIdx !== i) {
            [array[i], array[minIdx]] = [array[minIdx], array[i]];
            swaps++;
        }
    }
}

async function insertionSortFast() {
    for (let i = 1; i < array.length; i++) {
        let key = array[i];
        let j = i - 1;
        while (j >= 0) {
            comparisons++;
            if (array[j] <= key) break;
            array[j + 1] = array[j];
            swaps++;
            j--;
        }
        array[j + 1] = key;
    }
}

async function quickSortFast(low, high) {
    if (low < high) {
        const pi = await partitionFast(low, high);
        await quickSortFast(low, pi - 1);
        await quickSortFast(pi + 1, high);
    }
}

async function partitionFast(low, high) {
    const pivot = array[high];
    let i = low - 1;

    for (let j = low; j < high; j++) {
        comparisons++;
        if (array[j] < pivot) {
            i++;
            if (i !== j) {
                [array[i], array[j]] = [array[j], array[i]];
                swaps++;
            }
        }
    }

    if (i + 1 !== high) {
        [array[i + 1], array[high]] = [array[high], array[i + 1]];
        swaps++;
    }

    return i + 1;
}

async function mergeSortFast(left, right) {
    if (left < right) {
        const mid = Math.floor((left + right) / 2);
        await mergeSortFast(left, mid);
        await mergeSortFast(mid + 1, right);
        await mergeFast(left, mid, right);
    }
}

async function mergeFast(left, mid, right) {
    const leftArr = array.slice(left, mid + 1);
    const rightArr = array.slice(mid + 1, right + 1);

    let i = 0, j = 0, k = left;

    while (i < leftArr.length && j < rightArr.length) {
        comparisons++;
        if (leftArr[i] <= rightArr[j]) {
            array[k] = leftArr[i];
            i++;
        } else {
            array[k] = rightArr[j];
            j++;
        }
        swaps++;
        k++;
    }

    while (i < leftArr.length) {
        array[k] = leftArr[i];
        i++;
        k++;
        swaps++;
    }

    while (j < rightArr.length) {
        array[k] = rightArr[j];
        j++;
        k++;
        swaps++;
    }
}
