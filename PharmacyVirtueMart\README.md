# Website Dược Phẩm và Sản Phẩm Y Tế - VirtueMart

## Mô tả dự án
Website giới thiệu và bán hàng dượ<PERSON> phẩm, sản phẩm y tế sử dụng Joomla CMS và VirtueMart component.

## Tính năng chính
- Quản lý danh mục sản phẩm dược phẩm
- Hiển thị thông tin chi tiết sản phẩm (mô tả, giá bán, hình ảnh)
- Giỏ hàng và quản lý đơn hàng
- Quản lý khách hàng
- B<PERSON>o cáo bán hàng
- Giao diện responsive

## Cấu trúc thư mục

```
PharmacyVirtueMart/
├── Documentation/           # Tài liệu dự án
│   ├── Installation-Guide/  # Hướng dẫn cài đặt
│   ├── User-Manual/        # Hướng dẫn sử dụng
│   └── Admin-Guide/        # Hướng dẫn quản trị
├── Database/               # Cơ sở dữ liệu
│   ├── MySQL-Scripts/      # Scripts tạo database
│   └── Sample-Data/        # Dữ liệu mẫu
├── Joomla-VirtueMart/      # Source code
│   ├── Components/         # VirtueMart components
│   ├── Templates/          # Giao diện templates
│   └── Modules/           # Modules bổ sung
├── Screenshots/            # Ảnh chụp màn hình
│   ├── Frontend/          # Giao diện người dùng
│   └── Backend/           # Giao diện quản trị
└── Reports/               # Báo cáo dự án
    └── Project-Report/    # Báo cáo chi tiết
```

## Yêu cầu hệ thống
- PHP 7.4 hoặc cao hơn
- MySQL 5.7 hoặc cao hơn
- Apache/Nginx web server
- Joomla 4.x
- VirtueMart 4.x

## Các bước thực hiện
1. Cài đặt và cấu hình Joomla
2. Cài đặt VirtueMart component
3. Thiết lập cơ sở dữ liệu MySQL
4. Cấu hình danh mục sản phẩm
5. Nhập dữ liệu sản phẩm mẫu
6. Tùy chỉnh giao diện
7. Kiểm thử và triển khai

## Tác giả
Dự án website dược phẩm VirtueMart

## Ghi chú
Dự án này được phát triển cho mục đích học tập và thương mại điện tử trong lĩnh vực dược phẩm.
