{"GlobalPropertiesHash": "wktoueEcMwtKSQsDnHlW1qjxV3KmKQ3y5ko4MDBA0iE=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["acjuLEyQnGZN7ltTnFnoklwUIJFfLztptY73SppbpAw=", "oY4oRsz/4C9dRUB0cGiYB5jba4N3pGOcxRJI6ygC2AQ=", "nvL+Ndk7lvBpxtQYPEns/B940iGW8aroU3ZbzUpnKX8=", "AwfKuxrVVRtHdzW3B2gV0Pm5RJaRObeVVK/ksp9pYdU=", "UH39s2cdSC0skNYJ0mRvqpMyOp3W4KwlgczsHt+k4gs=", "MNVgm/6CYfivCWbXRsr8g+49LpJcQ/NQNYJqVFOuHW8=", "HF5t47YivqFrUrUxqm7MC3QsgU1ThDQey/SiotjI4Bs=", "G/CNpOCouho30yJneAX/NmtssJmdZa/KJF+g4WGBv+0=", "yig15bXG7xdCjI4dYSNBuNIhmjKX/rjfamVU5nQEOew=", "XlhOk+FW8B1pfn7dS6Gc4kFM/eMtSkKXIAPypXltX2M=", "Ud+VfDw9qmQ0nR5HU/4twu8tz0nw8JpHAErrg17zsrY=", "r0wdZhjgckNVxsTGWEo+NDL4Ukdj+bOuJq6sG3Dr3ss=", "+EEKHdS5vaLIo5xzIMzOQhiHI2yeRClg32ni/xg+YKw=", "IinS5QCkKWFzsxpp0Hz6pq6XmpjjHlquFSwNfYBADp8=", "lrndh4jT5NktMwz3p2EV4WWZK6NXfBOBh9IRa0WQSec=", "T++Vfv7MkoCT9ZqcSUXwQT/Q5f1VApozcj03d58Bygc=", "iczu/gTOKpEtTdy4WDuQfG28wcGSIljngK6KlDVmwaE=", "pN4k3BvU0kI39oMGUs7OoexOIJGpk0r63n2X/MNZfYg=", "D30MELAToUxOJcNQrwAIeNQHW0FdeGpgZdIJ1/NDUJA=", "RmG6mDCUONvtCnaIxHZBblA81t88GoRbzJZnmHK2krA=", "WKrzWdGeiGMJaHLSkc0SsInMp0x+Hm9mDYhg4bOjI6c=", "eFzjP1ymWpbeXeAtkY83nW9ohLCH+fxiwTt2HX9gtzE=", "Dgxm0C5VJjHbEoxxC0TN86XYLZkPpQSv7CANK3iqcIM=", "TZ3MIqRtUBtYkODRfBGBIpOhQRHTSYUv89Id9Pb72wY=", "eGHZONE9sAZvUAjIXpu4jYXXkAvgY/oJInUKRhlcN2U=", "MokAlxwYtj5I4XiBJ8cdagLTDs+qi/a7TqnBzB9ecz0=", "9+wORk1bdGyOkfkrQ3DWxWRUKor2qZpPPvW5xAxzd2c=", "iLCcJXrrXPHPy0FwAFP8BKZv4OwgDe3aYaMHSCKokaY=", "2SwlXu3lW42duKrGw6+foXnRetSUW/H2kW6S0mDfheU=", "P63PRMC/7bFs+my7H//i+JcuhWKFbqV8LSTmImegzK0=", "zvjQPC6h+lXcNxQrVU/29UnPupG9zDNp3wMNCUZfIpI=", "583VCZczuygzNJNXO4rkPNPdoAKZbtn0IDxjdQaptjI=", "8ddM9HcXZJOGwbDA2JVbtDT1lfsLj+Iy99zdZ9JwNMg=", "/qsfwnNXhqMp7HKk+qb26/WQzQb8vs96E7l7L3iZSXs=", "crh9oNgtue6cOF/As7YCwo2PB6GIfqg+QZ+XCrLa0no=", "yLmXb+oZnf7pvYl+gUkJCIp7F9EDFbc/7CwBYX7lKQo=", "UUAUWJghF/HCC4yFc0lohSA50ZnMuMOh/8j/LrWHX1E=", "2VxyuswWPwD0Xn4HMqVkNivtv9WwLEKz2XC0ALvmCt4=", "1ymzvccyvNC3pp1lF1Exb8kyFxX7bR+TlEuH8SF9zBg=", "MbV4T6Zg5ugvEEsqK8GKGIC6NvuCp6XHa9iu7zdouK0=", "uqwLSnCY3urCvt50k1LRrvkepkVYwPA+GF5iIJVT4V8=", "GMO7aR6ftLi8h2lDmXmQ7yY3jlJEy7pOt+rr4pgf0Fg=", "3SoL+PcOoyZ+h/pR1yP6VJHbnhLUr78VRfN9AIKTQdQ=", "MlgkFk6EN+B5zhhFH9jqf4DK+8vo6ad1UyT809hpoDw=", "k0kazrlDWC82nboRYSHORBcpO0q7SWsJA5yJDXg2xTU=", "JWUYdhXB/ORGPkF3QB2Tu92ftRkrd7UykNN/Zxnx2Gs=", "D7c/FjcsStjDu2g8yhg8k8SCEFyr4WZUkoXMDJpYtDo=", "KV+lKwwJ2cx7zSzEML0vhdowPOiifgwh9OGb4nvhlqI=", "4sT47+b87wEDIkymiYXu0ZkaXGMGCDxQBZzqltdJQbM=", "OSVjeZazj09GUAQMh/AEYHAMaG6tY9at2DkO51WRKYM=", "JRfERBs58zlVZD0Hw3HhnvtGaPiWHliAd8VjKq+gP0A=", "ImJIquHbSkXZ2FC8wLQgaxQLPHq4bUFeJz34K5Qf6ck=", "b8QWi8Hlf+upVQeV/SaUKbBGXW/vbm8187hELqnDfsc=", "JZFTHuXwb3hd84fuZm7K+vYZM1zvjPrZfieSCSG8vPE=", "fPfrOmkeM78vLJK9jFsEC+FA5QlECj6vf5MnsWEuIt8=", "AfAO12dUFRHBuuWPj35axxutTQjKJOggFJw+xTs3eiM=", "uAj6uSL49LHax3bbOVUUh0GOg3lVipcwzrrX6RMQv8k=", "k4TBp5DwBlwGDsUMb3dWzxCwYpzysi1E4Qggv+uy6xU=", "3vsjL/l2Ybi3akwiCiiacB3Ix4vrqHs7MXHj/VZ9B9k=", "pjjoD9GawoxhJqhzQY1ST9guKU6gfj9+i/hV2yC+/og=", "w2vG/6E7KAu51G6g0nzfUpONDhOSdN9X8nNPUAGmkB0=", "R4q4/MGwZZbZEpu5z7BLqY4lz9AJb22rH+vn3Qnf+FY=", "2DYwhoW/8kLDs1DNBShHp8YFoJhDAeHonn/JIa/mkWo=", "HVj7IaRocfH/mCuVlho81swHlshrj1cRkCahu9manq4=", "KOdBH9OlUpfhz0tltGjYlD64oHUlGyKBDoYk0Ypytfg=", "TTZEe4CWa1lZiyPg2DhyRauX3ZF01noQyLbjTYmOtxQ=", "C9n/N/rNzNd06tMczN2eSQBeHaw4wE6d3LaW+la76rI=", "F/mtsNxlqylLXK10hHaNTbqnX/a8tWIkEHxkteU9Yl0=", "4pru3odL9kXj5GJwFxz6I+O0Rz9rCb95IshoEPIUaD4=", "x4pdBb1/0+c+hI6deVabe+8W84MfARtcr1ld4GizUA8=", "YVFZ15p1TuvF1mH8vfiubsvGcD8bVo/FMgUzHbGjh9A=", "XCgkkbf0EeCgRgpqswhXl93oI6PuYenOuoZy5PHiRDs=", "bTW9ancPf/lGEjNKOJjwz2oNGsF486umFtkUFawcuOk=", "HdSJrIgqZ71f15xypGADq+QcF2lcRtw9gAGIdoA284k=", "RDAlVj1enoGKJh0TI2A8sd2210/PkSu9UdlPsPaYjaY=", "fv+bY5m+uiw619ddNzmsWGYNlRgZEV1ROKrkAZjcRIE=", "GPycDBZBkTiXNhU41Awgwj7ERkZNu49P9VTcwFnj2Bs=", "IXIFkTgFX+6Sfz7ugUPjcaYyZpT786ceZciOOu2P0Og=", "F3GCJjcaVspcv4ybwup6e9NU7kOXFaXY8u4zdQPfW3o=", "ngXag7b/W9hfysMRiyqnkO+9UVxf53l7C07vdQFwzqk=", "95HkQTZLkEXW9cMeRYPLUOKCo/dHw1y3t0KQkK4Sfr8=", "yyIOYuZFgsbXGbH28woWgTIYzL80wPKVC0SjgoZ28Us="], "CachedAssets": {"acjuLEyQnGZN7ltTnFnoklwUIJFfLztptY73SppbpAw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\css\\site.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gi61fezo2k", "Integrity": "yJGiCan5tNL1ySe6lix0HQdYfZVQRoe16T8fCk2fHYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 32246, "LastWriteTime": "2025-07-17T11:26:59.8932028+00:00"}, "oY4oRsz/4C9dRUB0cGiYB5jba4N3pGOcxRJI6ygC2AQ=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\favicon.ico", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-17T01:18:18.0498492+00:00"}, "nvL+Ndk7lvBpxtQYPEns/B940iGW8aroU3ZbzUpnKX8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\js\\site.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-17T01:18:18.1021565+00:00"}, "AwfKuxrVVRtHdzW3B2gV0Pm5RJaRObeVVK/ksp9pYdU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-17T01:18:17.9759847+00:00"}, "UH39s2cdSC0skNYJ0mRvqpMyOp3W4KwlgczsHt+k4gs=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-17T01:18:17.9759847+00:00"}, "MNVgm/6CYfivCWbXRsr8g+49LpJcQ/NQNYJqVFOuHW8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-17T01:18:17.9769908+00:00"}, "HF5t47YivqFrUrUxqm7MC3QsgU1ThDQey/SiotjI4Bs=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-17T01:18:17.9769908+00:00"}, "G/CNpOCouho30yJneAX/NmtssJmdZa/KJF+g4WGBv+0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-17T01:18:17.9769908+00:00"}, "yig15bXG7xdCjI4dYSNBuNIhmjKX/rjfamVU5nQEOew=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-17T01:18:17.9784829+00:00"}, "XlhOk+FW8B1pfn7dS6Gc4kFM/eMtSkKXIAPypXltX2M=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-17T01:18:17.9798808+00:00"}, "Ud+VfDw9qmQ0nR5HU/4twu8tz0nw8JpHAErrg17zsrY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-17T01:18:17.9808875+00:00"}, "r0wdZhjgckNVxsTGWEo+NDL4Ukdj+bOuJq6sG3Dr3ss=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-17T01:18:17.9808875+00:00"}, "+EEKHdS5vaLIo5xzIMzOQhiHI2yeRClg32ni/xg+YKw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-17T01:18:17.9808875+00:00"}, "IinS5QCkKWFzsxpp0Hz6pq6XmpjjHlquFSwNfYBADp8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-17T01:18:17.9818506+00:00"}, "lrndh4jT5NktMwz3p2EV4WWZK6NXfBOBh9IRa0WQSec=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-17T01:18:17.9818506+00:00"}, "T++Vfv7MkoCT9ZqcSUXwQT/Q5f1VApozcj03d58Bygc=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-17T01:18:17.9832033+00:00"}, "iczu/gTOKpEtTdy4WDuQfG28wcGSIljngK6KlDVmwaE=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-17T01:18:17.9832033+00:00"}, "pN4k3BvU0kI39oMGUs7OoexOIJGpk0r63n2X/MNZfYg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-17T01:18:17.9832033+00:00"}, "D30MELAToUxOJcNQrwAIeNQHW0FdeGpgZdIJ1/NDUJA=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-17T01:18:17.984459+00:00"}, "RmG6mDCUONvtCnaIxHZBblA81t88GoRbzJZnmHK2krA=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-17T01:18:17.984459+00:00"}, "WKrzWdGeiGMJaHLSkc0SsInMp0x+Hm9mDYhg4bOjI6c=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-17T01:18:17.9859459+00:00"}, "eFzjP1ymWpbeXeAtkY83nW9ohLCH+fxiwTt2HX9gtzE=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-17T01:18:17.9859459+00:00"}, "Dgxm0C5VJjHbEoxxC0TN86XYLZkPpQSv7CANK3iqcIM=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-17T01:18:17.9859459+00:00"}, "TZ3MIqRtUBtYkODRfBGBIpOhQRHTSYUv89Id9Pb72wY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-17T01:18:17.9859459+00:00"}, "eGHZONE9sAZvUAjIXpu4jYXXkAvgY/oJInUKRhlcN2U=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-17T01:18:17.9873991+00:00"}, "MokAlxwYtj5I4XiBJ8cdagLTDs+qi/a7TqnBzB9ecz0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-17T01:18:17.9884044+00:00"}, "9+wORk1bdGyOkfkrQ3DWxWRUKor2qZpPPvW5xAxzd2c=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-17T01:18:17.9884044+00:00"}, "iLCcJXrrXPHPy0FwAFP8BKZv4OwgDe3aYaMHSCKokaY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-17T01:18:17.9884044+00:00"}, "2SwlXu3lW42duKrGw6+foXnRetSUW/H2kW6S0mDfheU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-17T01:18:17.9908035+00:00"}, "P63PRMC/7bFs+my7H//i+JcuhWKFbqV8LSTmImegzK0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-17T01:18:17.9908035+00:00"}, "zvjQPC6h+lXcNxQrVU/29UnPupG9zDNp3wMNCUZfIpI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-17T01:18:17.9922374+00:00"}, "583VCZczuygzNJNXO4rkPNPdoAKZbtn0IDxjdQaptjI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-17T01:18:17.9922374+00:00"}, "8ddM9HcXZJOGwbDA2JVbtDT1lfsLj+Iy99zdZ9JwNMg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-17T01:18:17.9935744+00:00"}, "/qsfwnNXhqMp7HKk+qb26/WQzQb8vs96E7l7L3iZSXs=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-17T01:18:17.9965886+00:00"}, "crh9oNgtue6cOF/As7YCwo2PB6GIfqg+QZ+XCrLa0no=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-17T01:18:17.9980663+00:00"}, "yLmXb+oZnf7pvYl+gUkJCIp7F9EDFbc/7CwBYX7lKQo=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-17T01:18:18.0005557+00:00"}, "UUAUWJghF/HCC4yFc0lohSA50ZnMuMOh/8j/LrWHX1E=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-17T01:18:18.0016834+00:00"}, "2VxyuswWPwD0Xn4HMqVkNivtv9WwLEKz2XC0ALvmCt4=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-17T01:18:18.0021887+00:00"}, "1ymzvccyvNC3pp1lF1Exb8kyFxX7bR+TlEuH8SF9zBg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-17T01:18:18.0039781+00:00"}, "MbV4T6Zg5ugvEEsqK8GKGIC6NvuCp6XHa9iu7zdouK0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-17T01:18:18.0044825+00:00"}, "uqwLSnCY3urCvt50k1LRrvkepkVYwPA+GF5iIJVT4V8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-17T01:18:18.0055536+00:00"}, "GMO7aR6ftLi8h2lDmXmQ7yY3jlJEy7pOt+rr4pgf0Fg=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-17T01:18:18.0055536+00:00"}, "3SoL+PcOoyZ+h/pR1yP6VJHbnhLUr78VRfN9AIKTQdQ=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-17T01:18:18.0055536+00:00"}, "MlgkFk6EN+B5zhhFH9jqf4DK+8vo6ad1UyT809hpoDw=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-17T01:18:18.0069783+00:00"}, "k0kazrlDWC82nboRYSHORBcpO0q7SWsJA5yJDXg2xTU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-17T01:18:18.0079849+00:00"}, "JWUYdhXB/ORGPkF3QB2Tu92ftRkrd7UykNN/Zxnx2Gs=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-17T01:18:18.0089851+00:00"}, "D7c/FjcsStjDu2g8yhg8k8SCEFyr4WZUkoXMDJpYtDo=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-17T01:18:18.0103904+00:00"}, "KV+lKwwJ2cx7zSzEML0vhdowPOiifgwh9OGb4nvhlqI=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-17T01:18:17.9769908+00:00"}, "4sT47+b87wEDIkymiYXu0ZkaXGMGCDxQBZzqltdJQbM=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-17T01:18:18.0593424+00:00"}, "OSVjeZazj09GUAQMh/AEYHAMaG6tY9at2DkO51WRKYM=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-17T01:18:18.0615683+00:00"}, "JRfERBs58zlVZD0Hw3HhnvtGaPiWHliAd8VjKq+gP0A=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-17T01:18:17.9798808+00:00"}, "ImJIquHbSkXZ2FC8wLQgaxQLPHq4bUFeJz34K5Qf6ck=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-17T01:18:18.0418171+00:00"}, "b8QWi8Hlf+upVQeV/SaUKbBGXW/vbm8187hELqnDfsc=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-17T01:18:18.0488502+00:00"}, "JZFTHuXwb3hd84fuZm7K+vYZM1zvjPrZfieSCSG8vPE=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-17T01:18:18.0498492+00:00"}, "fPfrOmkeM78vLJK9jFsEC+FA5QlECj6vf5MnsWEuIt8=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-17T01:18:18.0498492+00:00"}, "AfAO12dUFRHBuuWPj35axxutTQjKJOggFJw+xTs3eiM=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-17T01:18:17.9784829+00:00"}, "uAj6uSL49LHax3bbOVUUh0GOg3lVipcwzrrX6RMQv8k=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-17T01:18:18.0103904+00:00"}, "k4TBp5DwBlwGDsUMb3dWzxCwYpzysi1E4Qggv+uy6xU=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-17T01:18:18.0118824+00:00"}, "3vsjL/l2Ybi3akwiCiiacB3Ix4vrqHs7MXHj/VZ9B9k=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-17T01:18:18.0196125+00:00"}, "pjjoD9GawoxhJqhzQY1ST9guKU6gfj9+i/hV2yC+/og=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-17T01:18:18.0196125+00:00"}, "w2vG/6E7KAu51G6g0nzfUpONDhOSdN9X8nNPUAGmkB0=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-17T01:18:18.023777+00:00"}, "R4q4/MGwZZbZEpu5z7BLqY4lz9AJb22rH+vn3Qnf+FY=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-17T01:18:18.0398168+00:00"}, "2DYwhoW/8kLDs1DNBShHp8YFoJhDAeHonn/JIa/mkWo=": {"Identity": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "RealEstateWebsite", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\RealEstateWebsite\\wwwroot\\", "BasePath": "_content/RealEstateWebsite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-17T01:18:17.9769908+00:00"}}, "CachedCopyCandidates": {}}