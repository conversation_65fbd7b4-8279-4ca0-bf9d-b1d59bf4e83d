using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PetCareWebsite.Data;
using PetCareWebsite.Models;
using PetCareWebsite.ViewModels;

namespace PetCareWebsite.Controllers;

[Authorize]
public class AppointmentsController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;

    public AppointmentsController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
    {
        _context = context;
        _userManager = userManager;
    }

    public async Task<IActionResult> Index()
    {
        var userId = _userManager.GetUserId(User);
        var appointments = await _context.Appointments
            .Include(a => a.Pet)
            .Include(a => a.Service)
            .Where(a => a.UserId == userId)
            .OrderByDescending(a => a.AppointmentDate)
            .ToListAsync();

        return View(appointments);
    }

    public async Task<IActionResult> Details(int id)
    {
        var userId = _userManager.GetUserId(User);
        var appointment = await _context.Appointments
            .Include(a => a.Pet)
            .Include(a => a.Service)
            .Include(a => a.User)
            .FirstOrDefaultAsync(a => a.Id == id && a.UserId == userId);

        if (appointment == null)
        {
            return NotFound();
        }

        return View(appointment);
    }

    public async Task<IActionResult> Create(int? serviceId)
    {
        var userId = _userManager.GetUserId(User);
        var pets = await _context.Pets
            .Where(p => p.UserId == userId)
            .ToListAsync();

        if (!pets.Any())
        {
            TempData["Error"] = "Bạn cần thêm thông tin thú cưng trước khi đặt lịch hẹn.";
            return RedirectToAction("Create", "Pets");
        }

        var services = await _context.Services
            .Where(s => s.IsActive)
            .ToListAsync();

        var model = new AppointmentViewModel();
        
        if (serviceId.HasValue)
        {
            model.ServiceId = serviceId.Value;
        }

        ViewBag.Pets = new SelectList(pets, "Id", "Name");
        ViewBag.Services = new SelectList(services, "Id", "Name", model.ServiceId);

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(AppointmentViewModel model)
    {
        if (ModelState.IsValid)
        {
            var userId = _userManager.GetUserId(User);
            
            // Validate that the pet belongs to the user
            var pet = await _context.Pets
                .FirstOrDefaultAsync(p => p.Id == model.PetId && p.UserId == userId);
            
            if (pet == null)
            {
                ModelState.AddModelError("PetId", "Thú cưng không hợp lệ.");
                await LoadSelectLists(model);
                return View(model);
            }

            // Get service to calculate total amount
            var service = await _context.Services.FindAsync(model.ServiceId);
            if (service == null)
            {
                ModelState.AddModelError("ServiceId", "Dịch vụ không hợp lệ.");
                await LoadSelectLists(model);
                return View(model);
            }

            // Check if appointment time is in the future
            var appointmentDateTime = model.AppointmentDate.Add(model.AppointmentTime);
            if (appointmentDateTime <= DateTime.Now)
            {
                ModelState.AddModelError("AppointmentDate", "Thời gian hẹn phải trong tương lai.");
                await LoadSelectLists(model);
                return View(model);
            }

            var appointment = new Appointment
            {
                UserId = userId!,
                PetId = model.PetId,
                ServiceId = model.ServiceId,
                AppointmentDate = model.AppointmentDate,
                AppointmentTime = model.AppointmentTime,
                Notes = model.Notes,
                TotalAmount = service.Price,
                Status = "Pending"
            };

            _context.Appointments.Add(appointment);
            await _context.SaveChangesAsync();
            
            TempData["Success"] = "Lịch hẹn đã được đặt thành công! Chúng tôi sẽ liên hệ với bạn để xác nhận.";
            return RedirectToAction(nameof(Index));
        }

        await LoadSelectLists(model);
        return View(model);
    }

    public async Task<IActionResult> Cancel(int id)
    {
        var userId = _userManager.GetUserId(User);
        var appointment = await _context.Appointments
            .Include(a => a.Pet)
            .Include(a => a.Service)
            .FirstOrDefaultAsync(a => a.Id == id && a.UserId == userId);

        if (appointment == null)
        {
            return NotFound();
        }

        if (appointment.Status == "Completed" || appointment.Status == "Cancelled")
        {
            TempData["Error"] = "Không thể hủy lịch hẹn này.";
            return RedirectToAction(nameof(Index));
        }

        return View(appointment);
    }

    [HttpPost, ActionName("Cancel")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CancelConfirmed(int id)
    {
        var userId = _userManager.GetUserId(User);
        var appointment = await _context.Appointments
            .FirstOrDefaultAsync(a => a.Id == id && a.UserId == userId);

        if (appointment != null && appointment.Status != "Completed" && appointment.Status != "Cancelled")
        {
            appointment.Status = "Cancelled";
            _context.Update(appointment);
            await _context.SaveChangesAsync();
            TempData["Success"] = "Lịch hẹn đã được hủy thành công.";
        }

        return RedirectToAction(nameof(Index));
    }

    private async Task LoadSelectLists(AppointmentViewModel model)
    {
        var userId = _userManager.GetUserId(User);
        var pets = await _context.Pets
            .Where(p => p.UserId == userId)
            .ToListAsync();

        var services = await _context.Services
            .Where(s => s.IsActive)
            .ToListAsync();

        ViewBag.Pets = new SelectList(pets, "Id", "Name", model.PetId);
        ViewBag.Services = new SelectList(services, "Id", "Name", model.ServiceId);
    }
}
