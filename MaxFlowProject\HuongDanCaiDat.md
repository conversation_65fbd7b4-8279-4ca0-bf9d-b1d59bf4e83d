# HƯỚNG DẪN CÀI ĐẶT VÀ CHẠY CHƯƠNG TRÌNH

## 🔧 CÀI ĐẶT COMPILER C++

### Cách 1: Cài đặt MinGW-w64 (Khuyến nghị cho Windows)

1. **Tải MinGW-w64:**
   - Truy cập: https://www.mingw-w64.org/downloads/
   - Chọn "MingW-W64-builds"
   - Tải file installer

2. **Cài đặt:**
   - Chạy installer
   - Chọn Architecture: x86_64
   - Threads: posix
   - Exception: seh
   - Cài đặt vào thư mục: `C:\mingw64`

3. **Thêm vào PATH:**
   - Mở System Properties → Advanced → Environment Variables
   - Thêm `C:\mingw64\bin` vào PATH
   - Restart Command Prompt

4. **Kiểm tra:**
   ```cmd
   g++ --version
   ```

### Cách 2: Cài đặt Visual Studio Build Tools

1. **Tải Visual Studio Installer:**
   - Truy cập: https://visualstudio.microsoft.com/downloads/
   - T<PERSON>i "Build Tools for Visual Studio"

2. **Cài đặt:**
   - Chọn "C++ build tools"
   - Cài đặt MSVC compiler

3. **<PERSON>ử dụng:**
   - Mở "Developer Command Prompt"
   - Sử dụng lệnh `cl` thay vì `g++`

### Cách 3: Cài đặt Code::Blocks (IDE + Compiler)

1. **Tải Code::Blocks:**
   - Truy cập: http://www.codeblocks.org/downloads
   - Chọn phiên bản có MinGW

2. **Cài đặt và sử dụng:**
   - Cài đặt Code::Blocks
   - Mở project và build

## 🚀 CÁCH CHẠY CHƯƠNG TRÌNH

### Bước 1: Biên dịch

**Với MinGW (g++):**
```cmd
cd MaxFlowProject
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram.exe
```

**Với Visual Studio (cl):**
```cmd
cd MaxFlowProject
cl /EHsc main.cpp Graph.cpp GraphUtils.cpp /Fe:MaxFlowProgram.exe
```

**Sử dụng script có sẵn:**
```cmd
# Windows
compile.bat

# Linux/macOS
chmod +x compile.sh
./compile.sh
```

### Bước 2: Chạy chương trình

```cmd
MaxFlowProgram.exe
```

## 📋 CÁC LỆNH BIÊN DỊCH CHI TIẾT

### Biên dịch chương trình chính:
```cmd
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram.exe
```

### Biên dịch chương trình test:
```cmd
g++ -std=c++11 -Wall -O2 test_main.cpp Graph.cpp GraphUtils.cpp -o TestProgram.exe
```

### Biên dịch demo đơn giản:
```cmd
g++ -std=c++11 simple_demo.cpp -o SimpleDemo.exe
```

## 🐛 XỬ LÝ LỖI THƯỜNG GẶP

### Lỗi: 'g++' is not recognized
**Nguyên nhân:** Chưa cài đặt compiler hoặc chưa thêm vào PATH
**Giải pháp:** Cài đặt MinGW và thêm vào PATH

### Lỗi: No such file or directory
**Nguyên nhân:** Đường dẫn file không đúng
**Giải pháp:** Kiểm tra đường dẫn và tên file

### Lỗi biên dịch C++11
**Nguyên nhân:** Compiler không hỗ trợ C++11
**Giải pháp:** Cập nhật compiler hoặc thêm flag `-std=c++11`

### Lỗi encoding tiếng Việt
**Nguyên nhân:** Console không hỗ trợ UTF-8
**Giải pháp:** Chạy lệnh `chcp 65001` trước khi chạy chương trình

## 🎯 KIỂM TRA CHƯƠNG TRÌNH

### Test nhanh với demo đơn giản:
```cmd
g++ simple_demo.cpp -o SimpleDemo.exe
SimpleDemo.exe
```

### Chạy tất cả test cases:
```cmd
TestProgram.exe
```

### Kiểm tra với đồ thị mẫu:
1. Chạy `MaxFlowProgram.exe`
2. Chọn menu "2. Sử dụng đồ thị mẫu"
3. Chọn đồ thị mẫu 1
4. Nhập nguồn: 0, đích: 3
5. Kết quả mong đợi: 23

## 📱 CHẠY TRÊN CÁC HỆ ĐIỀU HÀNH KHÁC

### Linux/Ubuntu:
```bash
sudo apt update
sudo apt install g++
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram
./MaxFlowProgram
```

### macOS:
```bash
# Cài đặt Xcode Command Line Tools
xcode-select --install
g++ -std=c++11 -Wall -O2 main.cpp Graph.cpp GraphUtils.cpp -o MaxFlowProgram
./MaxFlowProgram
```

## 🔍 KIỂM TRA KẾT QUẢ

Sau khi chạy thành công, bạn sẽ thấy:

1. **Menu chính** với các tùy chọn
2. **Đồ thị mẫu** hiển thị ma trận kề
3. **Từng bước thuật toán** với đường tăng luồng
4. **Kết quả cuối cùng** với luồng cực đại
5. **Lát cắt tối thiểu** và phân tích

## 📞 HỖ TRỢ

Nếu gặp vấn đề:
1. Kiểm tra file `KetQuaChayThu.txt` để xem kết quả mong đợi
2. Đọc thông báo lỗi cụ thể
3. Kiểm tra version compiler: `g++ --version`
4. Thử với demo đơn giản trước: `simple_demo.cpp`

---

**Chúc bạn thành công! 🎉**
