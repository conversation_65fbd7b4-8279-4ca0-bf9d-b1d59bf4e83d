# Hướng Dẫn Sử Dụng Web Demo - Mô Phỏng Thuật Toán Sắp Xếp

## 🌐 Giới Thiệu

Trang web demo này cung cấp giao diện trực quan để mô phỏng và so sánh các thuật toán sắp xếp nội cơ bản. Đư<PERSON><PERSON> thiết kế với HTML5, CSS3 và JavaScript thuần, không cần cài đặt thêm bất kỳ thư viện nào.

## 🚀 Cách Chạy

### Phương pháp 1: Mở trực tiếp
```bash
# Mở file index.html bằng trình duyệt web
# Double-click vào file index.html
# Hoặc kéo thả file vào trình duyệt
```

### Phương pháp 2: Sử dụng Live Server (Khuyến nghị)
```bash
# Nếu có VS Code với Live Server extension
# Right-click vào index.html → "Open with Live Server"

# Hoặc sử dụng Python
python -m http.server 8000
# Truy cập: http://localhost:8000

# Hoặc sử dụng Node.js
npx http-server
```

## 📋 Tính Năng Chính

### 1. **Điều Khiển Mô Phỏng**
- **Kích thước mảng:** 10-100 phần tử
- **Tốc độ sắp xếp:** 1-10 (chậm đến nhanh)
- **Chọn thuật toán:** Bubble, Selection, Insertion, Quick, Merge Sort
- **Các nút điều khiển:**
  - 🎲 **Tạo Mảng Mới:** Tạo dữ liệu ngẫu nhiên
  - ▶️ **Bắt Đầu:** Chạy thuật toán đã chọn
  - ⏸️ **Tạm Dừng:** Dừng tạm thời
  - ⏹️ **Dừng:** Reset về trạng thái ban đầu

### 2. **Visualization Trực Quan**
- **Thanh màu xanh:** Phần tử bình thường
- **Thanh màu đỏ:** Đang so sánh
- **Thanh màu hồng:** Đang hoán đổi
- **Thanh màu xanh lá:** Đã sắp xếp
- **Thanh màu cam:** Pivot (Quick Sort)

### 3. **Thống Kê Thời Gian Thực**
- Số lần so sánh
- Số lần hoán đổi
- Thời gian thực thi
- Trạng thái hiện tại

### 4. **Thông Tin Thuật Toán**
- Độ phức tạp thời gian và không gian
- Tính ổn định
- Mô tả chi tiết
- Mã nguồn minh họa

### 5. **So Sánh Hiệu Suất**
- Chạy tất cả thuật toán trên cùng dữ liệu
- Bảng so sánh chi tiết
- Thời gian thực thi, số lần so sánh, hoán đổi

### 6. **Demo Quản Lý Sinh Viên**
- Tạo danh sách sinh viên mẫu
- Sắp xếp theo: Mã SV, Tên, Điểm, Năm sinh
- Animation highlight kết quả

## 🎯 Hướng Dẫn Sử Dụng Chi Tiết

### Bước 1: Thiết Lập Tham Số
1. Điều chỉnh **kích thước mảng** (10-100)
2. Chọn **tốc độ sắp xếp** (1=chậm, 10=nhanh)
3. Chọn **thuật toán** từ dropdown

### Bước 2: Tạo Dữ Liệu
1. Click **"Tạo Mảng Mới"** để tạo dữ liệu ngẫu nhiên
2. Quan sát visualization với các thanh có chiều cao khác nhau

### Bước 3: Chạy Mô Phỏng
1. Click **"Bắt Đầu Sắp Xếp"**
2. Quan sát quá trình sắp xếp với màu sắc:
   - Đỏ: Đang so sánh
   - Hồng: Đang hoán đổi
   - Xanh lá: Đã hoàn thành
3. Theo dõi thống kê thời gian thực

### Bước 4: Điều Khiển
- **Tạm dừng:** Dừng tạm thời, có thể tiếp tục
- **Dừng:** Reset hoàn toàn
- **Thay đổi tốc độ:** Trong khi chạy

### Bước 5: So Sánh Thuật Toán
1. Click **"So Sánh Tất Cả"**
2. Chờ hệ thống chạy tất cả thuật toán
3. Xem bảng kết quả chi tiết

### Bước 6: Demo Sinh Viên
1. Click **"Tạo Danh Sách Sinh Viên"**
2. Chọn tiêu chí sắp xếp
3. Click **"Sắp Xếp"** và quan sát kết quả

## 🎨 Giao Diện Responsive

### Desktop (>768px)
- Layout 2-3 cột
- Visualization rộng
- Bảng đầy đủ

### Mobile (<768px)
- Layout 1 cột
- Nút điều khiển stack
- Bảng scroll ngang

## 🔧 Tùy Chỉnh

### Thay Đổi Màu Sắc
```css
/* Trong file styles.css */
.array-bar.comparing {
    background: linear-gradient(to top, #your-color1, #your-color2);
}
```

### Thêm Thuật Toán Mới
```javascript
// Trong file script.js
const algorithmInfo = {
    newAlgorithm: {
        name: "New Algorithm",
        timeComplexity: "O(?)",
        // ... thêm thông tin
    }
};
```

### Điều Chỉnh Tốc Độ
```javascript
// Thay đổi hàm getDelay()
function getDelay() {
    return Math.max(5, 1000 - (sortSpeed * 90)); // Chậm hơn
}
```

## 📊 Kết Quả Mong Đợi

### Với 50 phần tử:
- **Quick Sort:** ~5-15ms, ít so sánh nhất
- **Merge Sort:** ~10-25ms, ổn định
- **Insertion Sort:** ~50-150ms, tốt với dữ liệu nhỏ
- **Selection Sort:** ~100-200ms
- **Bubble Sort:** ~200-500ms, chậm nhất

### Với 100 phần tử:
- Thời gian tăng theo độ phức tạp
- O(n²) algorithms chậm rõ rệt
- O(n log n) algorithms vẫn nhanh

## 🐛 Troubleshooting

### Lỗi Thường Gặp:

**1. Trang không load:**
- Kiểm tra tất cả file cùng thư mục
- Mở Developer Tools (F12) xem lỗi

**2. Animation không mượt:**
- Giảm kích thước mảng
- Tăng tốc độ sắp xếp
- Đóng các tab khác

**3. Nút không hoạt động:**
- Refresh trang
- Kiểm tra JavaScript enabled
- Xem Console errors

**4. Responsive không đúng:**
- Zoom browser về 100%
- Kiểm tra viewport meta tag

### Performance Tips:
- Kích thước ≤ 50 cho animation mượt
- Tốc độ 5-7 cho quan sát tốt nhất
- Đóng DevTools khi demo

## 🎓 Mục Đích Giáo Dục

### Sinh Viên Có Thể:
1. **Quan sát trực quan** cách thuật toán hoạt động
2. **So sánh hiệu suất** thực tế
3. **Hiểu độ phức tạp** qua thực nghiệm
4. **Thực hành phân tích** thuật toán
5. **Demo ứng dụng** thực tế

### Giảng Viên Có Thể:
1. **Giảng dạy trực quan** trong lớp
2. **Giao bài tập** thực hành
3. **So sánh lý thuyết** vs thực tế
4. **Tạo quiz** tương tác
5. **Demo live** cho sinh viên

## 📱 Tương Thích

### Trình Duyệt Hỗ Trợ:
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ⚠️ IE 11 (hạn chế)

### Thiết Bị:
- 💻 Desktop/Laptop
- 📱 Smartphone
- 📟 Tablet
- 🖥️ Smart TV Browser

## 🔗 Tích Hợp

### Với LMS:
- Embed iframe
- SCORM package
- Direct link

### Với Presentation:
- Fullscreen mode
- Screenshot/recording
- Live demo

---

## 📞 Hỗ Trợ

**Phát triển bởi:** [Tên Sinh Viên]  
**Email:** [<EMAIL>]  
**GitHub:** [github.com/username]  

**Báo lỗi:** Tạo issue trên GitHub  
**Góp ý:** Pull request welcome  

---

*Cập nhật: [Ngày/Tháng/Năm]*
