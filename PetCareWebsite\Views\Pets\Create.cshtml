@model PetCareWebsite.ViewModels.PetViewModel
@{
    ViewData["Title"] = "Thêm Thú Cưng";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-paw me-2"></i>Thêm Thú Cưng Mới
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label fw-bold">
                                    <i class="fas fa-tag me-2"></i>@Html.DisplayNameFor(m => m.Name)
                                </label>
                                <input asp-for="Name" class="form-control form-control-lg" placeholder="Tên thú cưng" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Species" class="form-label fw-bold">
                                    <i class="fas fa-paw me-2"></i>@Html.DisplayNameFor(m => m.Species)
                                </label>
                                <select asp-for="Species" class="form-select form-select-lg">
                                    <option value="">Chọn loài</option>
                                    <option value="Chó">Chó</option>
                                    <option value="Mèo">Mèo</option>
                                    <option value="Chim">Chim</option>
                                    <option value="Thỏ">Thỏ</option>
                                    <option value="Hamster">Hamster</option>
                                    <option value="Khác">Khác</option>
                                </select>
                                <span asp-validation-for="Species" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Breed" class="form-label fw-bold">
                                    <i class="fas fa-dna me-2"></i>@Html.DisplayNameFor(m => m.Breed)
                                </label>
                                <input asp-for="Breed" class="form-control form-control-lg" placeholder="Giống loài" />
                                <span asp-validation-for="Breed" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Gender" class="form-label fw-bold">
                                    <i class="fas fa-venus-mars me-2"></i>@Html.DisplayNameFor(m => m.Gender)
                                </label>
                                <select asp-for="Gender" class="form-select form-select-lg">
                                    <option value="">Chọn giới tính</option>
                                    <option value="Đực">Đực</option>
                                    <option value="Cái">Cái</option>
                                </select>
                                <span asp-validation-for="Gender" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Age" class="form-label fw-bold">
                                    <i class="fas fa-birthday-cake me-2"></i>@Html.DisplayNameFor(m => m.Age)
                                </label>
                                <input asp-for="Age" class="form-control form-control-lg" type="number" min="0" max="50" />
                                <span asp-validation-for="Age" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Weight" class="form-label fw-bold">
                                    <i class="fas fa-weight me-2"></i>@Html.DisplayNameFor(m => m.Weight)
                                </label>
                                <input asp-for="Weight" class="form-control form-control-lg" type="number" step="0.1" min="0.1" max="200" />
                                <span asp-validation-for="Weight" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Description" class="form-label fw-bold">
                                <i class="fas fa-comment me-2"></i>@Html.DisplayNameFor(m => m.Description)
                            </label>
                            <textarea asp-for="Description" class="form-control" rows="4" placeholder="Mô tả thêm về thú cưng (tính cách, sở thích, lưu ý đặc biệt...)"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Thêm Thú Cưng
                            </button>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Quay Lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
