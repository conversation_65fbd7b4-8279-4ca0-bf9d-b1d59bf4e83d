#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MÔ PHỎNG CHẠY CHƯƠNG TRÌNH FORD-FULKERSON
Đ<PERSON>y là script Python mô phỏng kết quả chạy chương trình C++
"""

from collections import deque
import sys

class MaxFlowDemo:
    def __init__(self, vertices):
        self.V = vertices
        self.graph = [[0 for _ in range(vertices)] for _ in range(vertices)]
        self.original = [[0 for _ in range(vertices)] for _ in range(vertices)]
    
    def add_edge(self, u, v, capacity):
        self.graph[u][v] = capacity
        self.original[u][v] = capacity
    
    def display_matrix(self):
        print("\nMa trận kề:")
        print("    ", end="")
        for i in range(self.V):
            print(f"{i:4}", end="")
        print()
        
        for i in range(self.V):
            print(f"{i:2}: ", end="")
            for j in range(self.V):
                print(f"{self.original[i][j]:4}", end="")
            print()
        print()
    
    def bfs(self, source, sink, parent):
        visited = [False] * self.V
        queue = deque([source])
        visited[source] = True
        
        while queue:
            u = queue.popleft()
            
            for v in range(self.V):
                if not visited[v] and self.graph[u][v] > 0:
                    if v == sink:
                        parent[v] = u
                        return True
                    queue.append(v)
                    parent[v] = u
                    visited[v] = True
        
        return False
    
    def display_path(self, parent, source, sink):
        path = []
        current = sink
        while current != -1:
            path.append(current)
            current = parent[current]
        path.reverse()
        
        print(" -> ".join(map(str, path)))
    
    def ford_fulkerson(self, source, sink):
        print("\n=== BẮT ĐẦU THUẬT TOÁN FORD-FULKERSON ===")
        print(f"Nguồn: {source}, Đích: {sink}\n")
        
        # Tạo bản sao ma trận
        self.graph = [row[:] for row in self.original]
        
        parent = [-1] * self.V
        max_flow_value = 0
        iteration = 0
        
        while self.bfs(source, sink, parent):
            iteration += 1
            print(f"--- Lần lặp {iteration} ---")
            
            # Tìm luồng tối thiểu trên đường tăng luồng
            path_flow = float('inf')
            s = sink
            while s != source:
                path_flow = min(path_flow, self.graph[parent[s]][s])
                s = parent[s]
            
            print("Đường tăng luồng tìm được: ", end="")
            self.display_path(parent, source, sink)
            print(f"Luồng tăng thêm: {path_flow}")
            
            # Cập nhật khả năng dư
            v = sink
            while v != source:
                u = parent[v]
                self.graph[u][v] -= path_flow
                self.graph[v][u] += path_flow
                v = parent[v]
            
            max_flow_value += path_flow
            print(f"Tổng luồng hiện tại: {max_flow_value}\n")
        
        print("=== KẾT THÚC THUẬT TOÁN ===")
        print(f"Luồng cực đại từ {source} đến {sink}: {max_flow_value}\n")
        
        return max_flow_value

def main():
    print("╔══════════════════════════════════════════════════════════════════════════════╗")
    print("║                    ĐỒ ÁN NGHIÊN CỨU KHOA HỌC MÁY TÍNH                      ║")
    print("║                                                                              ║")
    print("║           BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ                  ║")
    print("║                                                                              ║")
    print("║                    THUẬT TOÁN FORD-FULKERSON                                ║")
    print("║                        (EDMONDS-KARP)                                       ║")
    print("║                                                                              ║")
    print("╚══════════════════════════════════════════════════════════════════════════════╝")
    
    print("\n=== DEMO THUẬT TOÁN FORD-FULKERSON ===")
    print("Nghiên cứu bài toán luồng cực đại\n")
    
    # Tạo đồ thị mẫu đơn giản
    print("Tạo đồ thị mẫu với 4 đỉnh:")
    g = MaxFlowDemo(4)
    
    # Thêm các cạnh
    g.add_edge(0, 1, 16)
    g.add_edge(0, 2, 13)
    g.add_edge(1, 2, 10)
    g.add_edge(1, 3, 12)
    g.add_edge(2, 1, 4)
    g.add_edge(2, 3, 14)
    
    print("Đồ thị đã tạo:")
    print("0 -> 1: 16")
    print("0 -> 2: 13")
    print("1 -> 2: 10")
    print("1 -> 3: 12")
    print("2 -> 1: 4")
    print("2 -> 3: 14")
    
    g.display_matrix()
    
    print("Tìm luồng cực đại từ đỉnh 0 đến đỉnh 3:")
    max_flow = g.ford_fulkerson(0, 3)
    
    print("KẾT QUẢ CUỐI CÙNG:")
    print(f"Luồng cực đại = {max_flow}")
    print("Kết quả mong đợi = 23")
    
    if max_flow == 23:
        print("✓ THUẬT TOÁN HOẠT ĐỘNG ĐÚNG!")
    else:
        print("✗ CÓ LỖI TRONG THUẬT TOÁN!")
    
    print("\n" + "="*80)
    
    # Test case 2
    print("\n=== TEST CASE 2: ĐỒ THỊ PHỨC TẠP ===")
    print("Đồ thị 6 đỉnh với nhiều đường đi")
    
    g2 = MaxFlowDemo(6)
    g2.add_edge(0, 1, 10)
    g2.add_edge(0, 2, 8)
    g2.add_edge(1, 2, 5)
    g2.add_edge(1, 3, 5)
    g2.add_edge(2, 4, 10)
    g2.add_edge(3, 2, 7)
    g2.add_edge(3, 4, 8)
    g2.add_edge(3, 5, 10)
    g2.add_edge(4, 5, 10)
    
    g2.display_matrix()
    
    print("Tìm luồng cực đại từ đỉnh 0 đến đỉnh 5:")
    max_flow2 = g2.ford_fulkerson(0, 5)
    
    print(f"Kết quả: {max_flow2}")
    print("Kết quả mong đợi: 15")
    
    if max_flow2 == 15:
        print("✓ TEST CASE 2 PASSED!")
    else:
        print("✗ TEST CASE 2 FAILED!")
    
    print("\n=== DEMO HOÀN THÀNH ===")
    print("Chương trình C++ sẽ có giao diện tương tự với thêm:")
    print("- Menu tương tác")
    print("- Nhập đồ thị từ người dùng")
    print("- Tìm lát cắt tối thiểu")
    print("- Nhiều test cases khác")

if __name__ == "__main__":
    main()
