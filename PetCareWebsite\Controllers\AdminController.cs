using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PetCareWebsite.Data;
using PetCareWebsite.Models;
using PetCareWebsite.ViewModels;

namespace PetCareWebsite.Controllers;

[Authorize(Roles = "Admin")]
public class AdminController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;

    public AdminController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
    {
        _context = context;
        _userManager = userManager;
    }

    public async Task<IActionResult> Index()
    {
        var stats = new AdminDashboardViewModel
        {
            TotalUsers = await _context.Users.CountAsync(),
            TotalPets = await _context.Pets.CountAsync(),
            TotalServices = await _context.Services.CountAsync(),
            TotalAppointments = await _context.Appointments.CountAsync(),
            PendingAppointments = await _context.Appointments.CountAsync(a => a.Status == "Pending"),
            CompletedAppointments = await _context.Appointments.CountAsync(a => a.Status == "Completed"),
            TotalRevenue = await _context.Appointments
                .Where(a => a.Status == "Completed")
                .SumAsync(a => a.TotalAmount)
        };

        // Recent appointments
        stats.RecentAppointments = await _context.Appointments
            .Include(a => a.User)
            .Include(a => a.Pet)
            .Include(a => a.Service)
            .OrderByDescending(a => a.CreatedAt)
            .Take(5)
            .ToListAsync();

        return View(stats);
    }

    // Appointments Management
    public async Task<IActionResult> Appointments()
    {
        var appointments = await _context.Appointments
            .Include(a => a.User)
            .Include(a => a.Pet)
            .Include(a => a.Service)
            .OrderByDescending(a => a.AppointmentDate)
            .ToListAsync();

        return View(appointments);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateAppointmentStatus(int id, string status)
    {
        var appointment = await _context.Appointments.FindAsync(id);
        if (appointment != null)
        {
            appointment.Status = status;
            _context.Update(appointment);
            await _context.SaveChangesAsync();
            TempData["Success"] = "Trạng thái lịch hẹn đã được cập nhật.";
        }

        return RedirectToAction(nameof(Appointments));
    }

    // Services Management
    public async Task<IActionResult> Services()
    {
        var services = await _context.Services
            .OrderBy(s => s.Name)
            .ToListAsync();

        return View(services);
    }

    public IActionResult CreateService()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateService(ServiceViewModel model)
    {
        if (ModelState.IsValid)
        {
            var service = new Service
            {
                Name = model.Name,
                Description = model.Description,
                Price = model.Price,
                DurationMinutes = model.DurationMinutes,
                IsActive = model.IsActive
            };

            _context.Services.Add(service);
            await _context.SaveChangesAsync();
            TempData["Success"] = "Dịch vụ đã được tạo thành công.";
            return RedirectToAction(nameof(Services));
        }

        return View(model);
    }

    public async Task<IActionResult> EditService(int id)
    {
        var service = await _context.Services.FindAsync(id);
        if (service == null)
        {
            return NotFound();
        }

        var model = new ServiceViewModel
        {
            Id = service.Id,
            Name = service.Name,
            Description = service.Description,
            Price = service.Price,
            DurationMinutes = service.DurationMinutes,
            IsActive = service.IsActive
        };

        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditService(int id, ServiceViewModel model)
    {
        if (id != model.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            var service = await _context.Services.FindAsync(id);
            if (service == null)
            {
                return NotFound();
            }

            service.Name = model.Name;
            service.Description = model.Description;
            service.Price = model.Price;
            service.DurationMinutes = model.DurationMinutes;
            service.IsActive = model.IsActive;

            _context.Update(service);
            await _context.SaveChangesAsync();
            TempData["Success"] = "Dịch vụ đã được cập nhật thành công.";
            return RedirectToAction(nameof(Services));
        }

        return View(model);
    }

    [HttpPost]
    public async Task<IActionResult> DeleteService(int id)
    {
        var service = await _context.Services.FindAsync(id);
        if (service != null)
        {
            // Check if service has appointments
            var hasAppointments = await _context.Appointments.AnyAsync(a => a.ServiceId == id);
            if (hasAppointments)
            {
                service.IsActive = false;
                _context.Update(service);
                TempData["Info"] = "Dịch vụ đã được vô hiệu hóa do có lịch hẹn liên quan.";
            }
            else
            {
                _context.Services.Remove(service);
                TempData["Success"] = "Dịch vụ đã được xóa thành công.";
            }
            
            await _context.SaveChangesAsync();
        }

        return RedirectToAction(nameof(Services));
    }

    // Users Management
    public async Task<IActionResult> Users()
    {
        var users = await _context.Users
            .Include(u => u.Pets)
            .Include(u => u.Appointments)
            .OrderBy(u => u.FullName)
            .ToListAsync();

        return View(users);
    }

    public async Task<IActionResult> UserDetails(string id)
    {
        var user = await _context.Users
            .Include(u => u.Pets)
            .Include(u => u.Appointments)
            .ThenInclude(a => a.Service)
            .FirstOrDefaultAsync(u => u.Id == id);

        if (user == null)
        {
            return NotFound();
        }

        return View(user);
    }
}
