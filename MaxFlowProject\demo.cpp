/*
 * DEMO ĐƠN GIẢN CHO THUẬT TOÁN FORD-FULKERSON
 * 
 * File này chứa demo đơn giản để kiểm tra thuật toán
 */

#include "Graph.h"
#include <iostream>

using namespace std;

int main() {
    cout << "=== DEMO THUẬT TOÁN FORD-FULKERSON ===\n\n";
    
    // Tạo đồ thị mẫu đơn giản
    cout << "Tạo đồ thị mẫu với 4 đỉnh:\n";
    Graph g(4);
    
    // Thêm các cạnh
    g.addEdge(0, 1, 16);
    g.addEdge(0, 2, 13);
    g.addEdge(1, 2, 10);
    g.addEdge(1, 3, 12);
    g.addEdge(2, 1, 4);
    g.addEdge(2, 3, 14);
    
    cout << "Đồ thị đã tạo:\n";
    g.displayAdjacencyMatrix();
    
    cout << "Tìm luồng cực đại từ đỉnh 0 đến đỉnh 3:\n";
    int maxFlow = g.fordFul<PERSON>on(0, 3);
    
    cout << "\nKết quả: Luồng cực đại = " << maxFlow << "\n";
    
    cout << "\nTìm lát cắt tối thiểu:\n";
    g.findMinCut(0, 3);
    
    cout << "\nDemo hoàn thành!\n";
    
    return 0;
}
