@model RealEstateWebsite.Models.ViewModels.AdminLoginViewModel

@{
    ViewData["Title"] = "Đăng Nhập Admin";
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .login-left {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .login-right {
            padding: 60px 40px;
        }

        .brand-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: white;
        }

        .brand-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .form-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .form-subtitle {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
            color: white;
        }

        .forgot-password {
            text-align: center;
            margin-top: 1.5rem;
        }

        .forgot-password a {
            color: #4e73df;
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        /* Keyframes for floating animation */
        .shape:nth-child(1) {
            animation: float1 6s ease-in-out infinite;
        }
        .shape:nth-child(2) {
            animation: float2 6s ease-in-out infinite 2s;
        }
        .shape:nth-child(3) {
            animation: float3 6s ease-in-out infinite 4s;
        }

        /* Responsive styles */
            .login-left {
                display: none;
            }
            
            .login-right {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="row g-0">
                <!-- Left Side - Branding -->
                <div class="col-lg-6">
                    <div class="login-left position-relative">
                        <div class="floating-shapes">
                            <div class="shape"></div>
                            <div class="shape"></div>
                            <div class="shape"></div>
                        </div>
                        
                        <div class="position-relative" style="z-index: 2;">
                            <div class="brand-logo">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <h1 class="brand-title">Admin Panel</h1>
                            <p class="brand-subtitle">
                                Hệ thống quản trị website bất động sản.<br>
                                Quản lý toàn bộ nội dung và người dùng một cách hiệu quả.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Login Form -->
                <div class="col-lg-6">
                    <div class="login-right">
                        <form class="login-form" asp-action="Login" method="post">
                            <h2 class="form-title">Đăng Nhập</h2>
                            <p class="form-subtitle">Vui lòng nhập thông tin đăng nhập</p>

                            <!-- Error Messages -->
                            @if (!ViewData.ModelState.IsValid)
                            {
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    @Html.ValidationSummary(false, "", new { @class = "mb-0" })
                                </div>
                            }

                            @if (TempData["ErrorMessage"] != null)
                            {
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    @TempData["ErrorMessage"]
                                </div>
                            }

                            <!-- Username Field -->
                            <div class="form-floating">
                                <input asp-for="Username" type="text" class="form-control" id="username" placeholder="admin" required>
                                <label for="username">
                                    <i class="fas fa-user me-2"></i>Tên đăng nhập
                                </label>
                                <span asp-validation-for="Username" class="text-danger"></span>
                            </div>

                            <!-- Password Field -->
                            <div class="form-floating">
                                <input asp-for="Password" type="password" class="form-control" id="password" placeholder="Mật khẩu" required>
                                <label for="password">
                                    <i class="fas fa-lock me-2"></i>Mật khẩu
                                </label>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>

                            <!-- Remember Me -->
                            <div class="form-check mb-3">
                                <input asp-for="RememberMe" class="form-check-input" type="checkbox" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>

                            <!-- Login Button -->
                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Đăng Nhập
                            </button>

                            <!-- Forgot Password -->
                            <div class="forgot-password">
                                <a href="#" onclick="alert('Vui lòng liên hệ quản trị viên để khôi phục mật khẩu.')">
                                    Quên mật khẩu?
                                </a>
                            </div>

                            <!-- Back to Website -->
                            <div class="text-center mt-4">
                                <a asp-controller="Home" asp-action="Index" class="text-muted">
                                    <i class="fas fa-arrow-left me-2"></i>Quay lại website
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto focus on username field
        document.getElementById('username').focus();

        // Form validation
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('Vui lòng nhập đầy đủ thông tin đăng nhập.');
                return false;
            }

            // Show loading state
            const submitBtn = document.querySelector('.btn-login');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng nhập...';
            submitBtn.disabled = true;
        });

        // Demo credentials hint
        console.log('Demo Admin Credentials:');
        console.log('Username: admin');
        console.log('Password: Admin123!');
    </script>
</body>
</html>
