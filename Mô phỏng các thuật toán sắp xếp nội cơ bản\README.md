# Mô phỏng các thuật toán sắp xếp nội cơ bản

## Giới thiệu

Dự án này cài đặt và so sánh hiệu quả của các thuật toán sắp xếp nội cơ bản bao gồm:
- Bubble Sort (Sắ<PERSON> xếp nổi bọt)
- Selection Sort (Sắ<PERSON> xếp chọn)
- Insertion Sort (Sắp xế<PERSON> chèn)
- Quick Sort (Sắp xếp nhanh)
- Merge Sort (Sắp xếp trộn)

Ứng dụng demo: Hệ thống quản lý sinh viên với các chức năng sắp xếp theo nhiều tiêu chí.

## Cấu trúc dự án

```
SortingAlgorithmsProject/
├── Student.h                 # Header file cho class Student
├── Student.cpp              # Implementation của class Student
├── SortingAlgorithms.h      # Header file cho các thuật toán sắp xếp
├── SortingAlgorithms.cpp    # Implementation cơ bản
├── SortingAlgorithms.tpp    # Template implementations
├── main.cpp                 # Chương trình chính
├── Makefile                 # File build
├── README.md               # Hướng dẫn sử dụng
└── BaoCaoDoAn.md           # Báo cáo đồ án chi tiết
```

## Yêu cầu hệ thống

- **Hệ điều hành:** Windows, Linux, macOS
- **Compiler:** GCC 4.8+ hoặc Clang 3.3+ (hỗ trợ C++11)
- **RAM:** Tối thiểu 512MB
- **Dung lượng:** 50MB trống

## Cài đặt và biên dịch

### Trên Linux/macOS:

```bash
# Clone hoặc download dự án
cd SortingAlgorithmsProject

# Biên dịch
make

# Hoặc biên dịch thủ công
g++ -std=c++11 -Wall -O2 -o SortingAlgorithmsDemo main.cpp Student.cpp SortingAlgorithms.cpp
```

### Trên Windows:

```cmd
# Sử dụng MinGW hoặc Visual Studio
g++ -std=c++11 -Wall -O2 -o SortingAlgorithmsDemo.exe main.cpp Student.cpp SortingAlgorithms.cpp

# Hoặc sử dụng Dev-C++, Code::Blocks
```

## Chạy chương trình

```bash
# Trên Linux/macOS
./SortingAlgorithmsDemo

# Trên Windows
SortingAlgorithmsDemo.exe
```

## Hướng dẫn sử dụng

### Menu chính:

1. **Thêm sinh viên:** Nhập thông tin sinh viên mới
2. **Hiển thị danh sách:** Xem tất cả sinh viên
3. **Sắp xếp danh sách:** Chọn tiêu chí và thuật toán sắp xếp
4. **Tìm kiếm sinh viên:** Tìm theo mã SV hoặc tên
5. **So sánh hiệu suất:** Đo và so sánh tất cả thuật toán
6. **Tạo dữ liệu mẫu:** Tự động tạo dữ liệu test
7. **Xóa tất cả dữ liệu:** Reset dữ liệu

### Tiêu chí sắp xếp:

- **Mã sinh viên:** Sắp xếp theo thứ tự alphabet
- **Tên:** Sắp xếp theo tên (A-Z)
- **Điểm trung bình:** Sắp xếp giảm dần (cao đến thấp)
- **Năm sinh:** Sắp xếp tăng dần (cũ đến mới)

### Ví dụ sử dụng:

```
=== HE THONG QUAN LY SINH VIEN ===
1. Them sinh vien
2. Hien thi danh sach sinh vien
3. Sap xep danh sach
4. Tim kiem sinh vien
5. So sanh hieu suat thuat toan
6. Tao du lieu mau
7. Xoa tat ca du lieu
0. Thoat
=================================
Lua chon cua ban: 6

=== TAO DU LIEU MAU ===
Nhap so luong sinh vien muon tao: 1000
Da tao 1000 sinh vien mau!

Lua chon cua ban: 5

=== SO SANH HIEU SUAT THUAT TOAN ===
Dang thuc hien so sanh tren 1000 sinh vien...

=== SO SANH HIEU SUAT CAC THUAT TOAN ===
Thuat toan     Thoi gian   So sanh     Hoan doi    Kich thuoc
-----------------------------------------------------------------
Bubble Sort    2850        499500      249750      1000
Selection Sort 1420        499500      999         1000
Insertion Sort 1180        249750      249750      1000
Quick Sort     85          13862       8934        1000
Merge Sort     125         8704        9976        1000
=================================================================
```

## Tính năng nổi bật

### 1. Đo lường hiệu suất chính xác
- Thời gian thực thi (microseconds)
- Số lần so sánh
- Số lần hoán đổi/di chuyển
- Kích thước dữ liệu

### 2. Hỗ trợ nhiều tiêu chí sắp xếp
- Linh hoạt với function pointer
- Template hỗ trợ nhiều kiểu dữ liệu
- Comparison functions tùy chỉnh

### 3. Giao diện thân thiện
- Menu điều hướng rõ ràng
- Hiển thị kết quả trực quan
- Xử lý lỗi đầu vào

### 4. Tạo dữ liệu test tự động
- Dữ liệu ngẫu nhiên
- Nhiều kích thước khác nhau
- Phù hợp cho thử nghiệm

## Kết quả thử nghiệm

### Hiệu suất trên 1000 phần tử (dữ liệu ngẫu nhiên):

| Thuật toán | Thời gian (μs) | Độ phức tạp | Ổn định |
|------------|----------------|-------------|---------|
| Quick Sort | 85 | O(n log n) | Không |
| Merge Sort | 125 | O(n log n) | Có |
| Insertion Sort | 1180 | O(n²) | Có |
| Selection Sort | 1420 | O(n²) | Không |
| Bubble Sort | 2850 | O(n²) | Có |

### Khuyến nghị sử dụng:

- **Dữ liệu lớn (>1000):** Quick Sort hoặc Merge Sort
- **Dữ liệu nhỏ (<100):** Insertion Sort
- **Cần ổn định:** Merge Sort hoặc Insertion Sort
- **Bộ nhớ hạn chế:** Quick Sort hoặc Insertion Sort

## Troubleshooting

### Lỗi biên dịch:
```bash
# Kiểm tra compiler hỗ trợ C++11
g++ --version

# Thêm flag -std=c++11
g++ -std=c++11 -o program main.cpp Student.cpp SortingAlgorithms.cpp
```

### Lỗi runtime:
- Kiểm tra dữ liệu đầu vào hợp lệ
- Đảm bảo đủ bộ nhớ cho dữ liệu lớn
- Kiểm tra quyền ghi file (nếu có)

### Hiệu suất chậm:
- Sử dụng flag tối ưu hóa: `-O2` hoặc `-O3`
- Giảm kích thước dữ liệu test
- Chọn thuật toán phù hợp

## Tài liệu tham khảo

- **Báo cáo chi tiết:** Xem file `BaoCaoDoAn.md`
- **Source code:** Tất cả file .h và .cpp
- **Makefile:** Hướng dẫn build tự động

## Tác giả

**Đồ án môn:** Cấu trúc dữ liệu và Giải thuật  
**Sinh viên thực hiện:** [Tên sinh viên]  
**MSSV:** [Mã số sinh viên]  
**Lớp:** [Tên lớp]  

## License

Dự án này được phát triển cho mục đích học tập và nghiên cứu.

---

*Cập nhật lần cuối: [Ngày/Tháng/Năm]*
