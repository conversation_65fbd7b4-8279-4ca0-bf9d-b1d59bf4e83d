<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mô Phỏng Thuật Toán Sắp Xếp <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-sort-amount-up"></i> Mô Phỏng Thuật Toán Sắp Xếp Nội</h1>
            <p>Trự<PERSON> quan hóa và so s<PERSON>h hi<PERSON>u suất các thuật toán sắp xếp c<PERSON> bản</p>
        </header>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="control-group">
                <label for="arraySize">Kích thước mảng:</label>
                <input type="range" id="arraySize" min="10" max="100" value="50">
                <span id="arraySizeValue">50</span>
            </div>

            <div class="control-group">
                <label for="sortSpeed">Tốc độ sắp xếp:</label>
                <input type="range" id="sortSpeed" min="1" max="10" value="5">
                <span id="sortSpeedValue">5</span>
            </div>

            <div class="control-group">
                <label for="algorithmSelect">Thuật toán:</label>
                <select id="algorithmSelect">
                    <option value="bubble">Bubble Sort</option>
                    <option value="selection">Selection Sort</option>
                    <option value="insertion">Insertion Sort</option>
                    <option value="quick">Quick Sort</option>
                    <option value="merge">Merge Sort</option>
                </select>
            </div>

            <div class="control-buttons">
                <button id="generateBtn" class="btn btn-primary">
                    <i class="fas fa-random"></i> Tạo Mảng Mới
                </button>
                <button id="sortBtn" class="btn btn-success">
                    <i class="fas fa-play"></i> Bắt Đầu Sắp Xếp
                </button>
                <button id="pauseBtn" class="btn btn-warning" disabled>
                    <i class="fas fa-pause"></i> Tạm Dừng
                </button>
                <button id="resetBtn" class="btn btn-danger">
                    <i class="fas fa-stop"></i> Dừng
                </button>
            </div>
        </div>

        <!-- Visualization Area -->
        <div class="visualization-container">
            <div class="array-container" id="arrayContainer">
                <!-- Array bars will be generated here -->
            </div>
        </div>

        <!-- Statistics Panel -->
        <div class="stats-panel">
            <div class="stat-item">
                <h3>Thống Kê</h3>
            </div>
            <div class="stat-item">
                <label>Số lần so sánh:</label>
                <span id="comparisons">0</span>
            </div>
            <div class="stat-item">
                <label>Số lần hoán đổi:</label>
                <span id="swaps">0</span>
            </div>
            <div class="stat-item">
                <label>Thời gian:</label>
                <span id="timeElapsed">0ms</span>
            </div>
            <div class="stat-item">
                <label>Trạng thái:</label>
                <span id="status">Sẵn sàng</span>
            </div>
        </div>

        <!-- Algorithm Info -->
        <div class="algorithm-info" id="algorithmInfo">
            <h3>Thông Tin Thuật Toán</h3>
            <div class="info-content">
                <div class="complexity-info">
                    <h4>Độ Phức Tạp</h4>
                    <p><strong>Thời gian:</strong> <span id="timeComplexity">O(n²)</span></p>
                    <p><strong>Không gian:</strong> <span id="spaceComplexity">O(1)</span></p>
                    <p><strong>Ổn định:</strong> <span id="stability">Có</span></p>
                </div>
                <div class="description">
                    <h4>Mô Tả</h4>
                    <p id="algorithmDescription">
                        Bubble Sort so sánh các cặp phần tử liền kề và hoán đổi chúng nếu chúng không đúng thứ tự.
                    </p>
                </div>
            </div>
        </div>

        <!-- Comparison Table -->
        <div class="comparison-section">
            <h3><i class="fas fa-chart-bar"></i> So Sánh Thuật Toán</h3>
            <button id="compareAllBtn" class="btn btn-info">
                <i class="fas fa-balance-scale"></i> So Sánh Tất Cả
            </button>
            
            <div class="comparison-table" id="comparisonTable" style="display: none;">
                <table>
                    <thead>
                        <tr>
                            <th>Thuật Toán</th>
                            <th>Thời Gian (ms)</th>
                            <th>So Sánh</th>
                            <th>Hoán Đổi</th>
                            <th>Độ Phức Tạp</th>
                        </tr>
                    </thead>
                    <tbody id="comparisonTableBody">
                        <!-- Comparison results will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Student Management Demo -->
        <div class="student-demo">
            <h3><i class="fas fa-graduation-cap"></i> Demo Quản Lý Sinh Viên</h3>
            <div class="student-controls">
                <button id="generateStudentsBtn" class="btn btn-secondary">
                    <i class="fas fa-users"></i> Tạo Danh Sách Sinh Viên
                </button>
                <select id="sortCriteria">
                    <option value="id">Sắp xếp theo Mã SV</option>
                    <option value="name">Sắp xếp theo Tên</option>
                    <option value="score">Sắp xếp theo Điểm</option>
                    <option value="year">Sắp xếp theo Năm Sinh</option>
                </select>
                <button id="sortStudentsBtn" class="btn btn-primary">
                    <i class="fas fa-sort"></i> Sắp Xếp
                </button>
            </div>
            
            <div class="student-table-container">
                <table class="student-table" id="studentTable">
                    <thead>
                        <tr>
                            <th>STT</th>
                            <th>Mã SV</th>
                            <th>Họ Tên</th>
                            <th>Điểm TB</th>
                            <th>Năm Sinh</th>
                        </tr>
                    </thead>
                    <tbody id="studentTableBody">
                        <!-- Student data will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Code Display -->
        <div class="code-section">
            <h3><i class="fas fa-code"></i> Mã Nguồn Thuật Toán</h3>
            <div class="code-container">
                <pre><code id="algorithmCode">
// Bubble Sort Implementation
function bubbleSort(arr) {
    let n = arr.length;
    for (let i = 0; i < n - 1; i++) {
        for (let j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                // Swap elements
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
            }
        }
    }
    return arr;
}
                </code></pre>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Đồ Án Cấu Trúc Dữ Liệu và Giải Thuật - Mô Phỏng Thuật Toán Sắp Xếp</p>
            <p>Phát triển bởi: [Tên Sinh Viên] - [MSSV]</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Đang thực hiện so sánh...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
