using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models
{
    public class Property
    {
        public int PropertyId { get; set; }

        [Required(ErrorMessage = "Tiêu đề là bắt buộc")]
        [StringLength(200, ErrorMessage = "Tiêu đề không được vượt quá 200 ký tự")]
        public string Title { get; set; } = string.Empty;

        [Required(ErrorMessage = "Mô tả là bắt buộc")]
        [StringLength(2000, ErrorMessage = "Mô tả không được vượt quá 2000 ký tự")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "Giá là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal Price { get; set; }

        [Required(ErrorMessage = "Diện tích là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "Diện tích phải lớn hơn 0")]
        public double Area { get; set; }

        [Required(ErrorMessage = "Địa chỉ là bắt buộc")]
        [StringLength(500, ErrorMessage = "Địa chỉ không được vượt quá 500 ký tự")]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessage = "Thành phố là bắt buộc")]
        [StringLength(100, ErrorMessage = "Thành phố không được vượt quá 100 ký tự")]
        public string City { get; set; } = string.Empty;

        [Required(ErrorMessage = "Quận/Huyện là bắt buộc")]
        [StringLength(100, ErrorMessage = "Quận/Huyện không được vượt quá 100 ký tự")]
        public string District { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "Phường/Xã không được vượt quá 100 ký tự")]
        public string Ward { get; set; } = string.Empty;

        public int Bedrooms { get; set; }
        public int Bathrooms { get; set; }
        public int Floors { get; set; }

        [StringLength(500, ErrorMessage = "Hình ảnh chính không được vượt quá 500 ký tự")]
        public string MainImage { get; set; } = string.Empty;

        public string? Gallery { get; set; } // JSON string of image URLs

        public bool IsFeatured { get; set; }
        public bool IsAvailable { get; set; } = true;

        public PropertyStatus Status { get; set; } = PropertyStatus.Available;
        public PropertyType Type { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }

        // Navigation properties
        public int CategoryId { get; set; }
        public Category Category { get; set; } = null!;

        public ICollection<PropertyImage> Images { get; set; } = new List<PropertyImage>();
        public ICollection<Favorite> Favorites { get; set; } = new List<Favorite>();
        public ICollection<Contact> Contacts { get; set; } = new List<Contact>();

        // Additional properties for SEO
        [StringLength(200)]
        public string? MetaTitle { get; set; }

        [StringLength(500)]
        public string? MetaDescription { get; set; }

        [StringLength(200)]
        public string? MetaKeywords { get; set; }

        // Property features
        public bool HasParking { get; set; }
        public bool HasGarden { get; set; }
        public bool HasSwimmingPool { get; set; }
        public bool HasElevator { get; set; }
        public bool HasBalcony { get; set; }
        public bool HasAirConditioning { get; set; }
        public bool HasSecurity { get; set; }

        // Location coordinates for map
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }

        // Contact information
        [StringLength(100)]
        public string? ContactName { get; set; }

        [StringLength(20)]
        public string? ContactPhone { get; set; }

        [StringLength(100)]
        public string? ContactEmail { get; set; }

        // Virtual tour
        [StringLength(500)]
        public string? VirtualTourUrl { get; set; }

        // Property orientation
        public string? Orientation { get; set; } // Hướng nhà

        // Legal status
        public string? LegalStatus { get; set; } // Tình trạng pháp lý

        // Year built
        public int? YearBuilt { get; set; }
    }

    public enum PropertyStatus
    {
        Available = 1,      // Có sẵn
        Sold = 2,          // Đã bán
        Rented = 3,        // Đã cho thuê
        Pending = 4,       // Đang chờ
        Unavailable = 5    // Không có sẵn
    }

    public enum PropertyType
    {
        Sale = 1,          // Bán
        Rent = 2,          // Cho thuê
        Both = 3           // Cả hai
    }
}
