-- =====================================================
-- Dữ liệu mẫu sản phẩm dược phẩm cho VirtueMart
-- =====================================================

USE pharmacy_virtuemart;

-- =====================================================
-- Dữ liệu mẫu cho bảng sản phẩm VirtueMart
-- =====================================================

-- Lưu ý: Cá<PERSON> bảng VirtueMart sẽ được tạo tự động khi cài đặt
-- <PERSON><PERSON><PERSON> này chỉ thêm dữ liệu mẫu

-- Thêm nhà cung cấp mẫu
INSERT INTO jos_vm_suppliers (supplier_name, contact_person, phone, email, address, tax_code, license_number) VALUES
('Công ty TNHH Dược phẩm An Khang', 'Nguyễn Văn A', '**********', '<EMAIL>', '123 Đường ABC, Quận 1, TP.HCM', '**********', 'DK-001-2024'),
('Công ty CP Dược phẩm Việt Nam', 'Trần Thị B', '**********', '<EMAIL>', '456 Đường XYZ, Hà Nội', '**********', 'DK-002-2024'),
('Nhà phân phối thiết bị y tế MedSupply', 'Lê Văn C', '**********', '<EMAIL>', '789 Đường DEF, Đà Nẵng', '**********', 'DK-003-2024');

-- Dữ liệu mẫu cho thông tin dược phẩm bổ sung
-- (Sẽ được liên kết với product_id từ VirtueMart sau khi tạo sản phẩm)

-- Thông tin thuốc giảm đau
INSERT INTO jos_vm_product_pharma_info (
    product_id, drug_registration_number, active_ingredient, dosage_form, strength,
    indication, contraindication, side_effects, dosage_instruction, storage_condition,
    prescription_required, age_restriction
) VALUES 
(1, 'VN-12345-20', 'Paracetamol', 'Viên nén', '500mg',
'Giảm đau, hạ sốt', 'Dị ứng với paracetamol, suy gan nặng', 
'Buồn nôn, nôn, phát ban da', '1-2 viên/lần, 3-4 lần/ngày', 
'Nơi khô ráo, tránh ánh sáng', 0, 'Trên 12 tuổi'),

(2, 'VN-12346-20', 'Ibuprofen', 'Viên nang', '400mg',
'Giảm đau, chống viêm', 'Loét dạ dày, suy thận, thai kỳ', 
'Đau bụng, chóng mặt, buồn nôn', '1 viên/lần, 2-3 lần/ngày sau ăn', 
'Nhiệt độ phòng, tránh ẩm ướt', 0, 'Trên 18 tuổi'),

(3, 'VN-12347-20', 'Amoxicillin', 'Viên nang', '250mg',
'Kháng sinh điều trị nhiễm khuẩn', 'Dị ứng penicillin', 
'Tiêu chảy, phát ban, buồn nôn', '1-2 viên/lần, 3 lần/ngày', 
'Nơi khô ráo, nhiệt độ dưới 25°C', 1, 'Mọi lứa tuổi'),

(4, 'VN-12348-20', 'Vitamin C', 'Viên sủi', '1000mg',
'Bổ sung vitamin C, tăng sức đề kháng', 'Sỏi thận, dị ứng vitamin C', 
'Hiếm gặp: đau bụng, tiêu chảy', '1 viên/ngày, hòa với nước', 
'Nơi khô ráo, tránh ánh sáng', 0, 'Trên 6 tuổi'),

(5, 'VN-12349-20', 'Omeprazole', 'Viên nang', '20mg',
'Điều trị loét dạ dày, trào ngược dạ dày', 'Dị ứng với omeprazole', 
'Đau đầu, buồn nôn, tiêu chảy', '1 viên/ngày, uống trước ăn', 
'Nhiệt độ phòng, tránh ẩm ướt', 1, 'Trên 18 tuổi');

-- Dữ liệu tồn kho mẫu
INSERT INTO jos_vm_pharma_inventory (
    product_id, batch_number, manufacturing_date, expiry_date, 
    quantity_in_stock, minimum_stock_level, cost_price, selling_price, supplier_id, location
) VALUES 
(1, 'PAR001-2024', '2024-01-15', '2026-01-15', 500, 50, 2000, 3000, 1, 'Kệ A1'),
(2, 'IBU001-2024', '2024-02-10', '2026-02-10', 300, 30, 5000, 7500, 1, 'Kệ A2'),
(3, 'AMO001-2024', '2024-03-05', '2025-03-05', 200, 20, 8000, 12000, 2, 'Kệ B1'),
(4, 'VTC001-2024', '2024-01-20', '2025-12-20', 1000, 100, 15000, 25000, 1, 'Kệ C1'),
(5, 'OME001-2024', '2024-02-15', '2026-02-15', 150, 15, 25000, 40000, 2, 'Kệ B2');

-- Dữ liệu đơn thuốc mẫu
INSERT INTO jos_vm_prescriptions (
    prescription_number, patient_name, patient_phone, patient_address,
    doctor_name, doctor_license, clinic_hospital, prescription_date, status
) VALUES 
('DT001-2024', 'Nguyễn Văn Nam', '**********', '123 Đường A, Quận 1, TP.HCM',
'BS. Trần Văn Bình', 'BS-001-2024', 'Bệnh viện Chợ Rẫy', '2024-07-15', 'completed'),

('DT002-2024', 'Lê Thị Hoa', '**********', '456 Đường B, Quận 3, TP.HCM',
'BS. Nguyễn Thị Lan', 'BS-002-2024', 'Phòng khám Đa khoa ABC', '2024-07-16', 'processing'),

('DT003-2024', 'Phạm Văn Đức', '**********', '789 Đường C, Quận 5, TP.HCM',
'BS. Lê Văn Minh', 'BS-003-2024', 'Bệnh viện Nhân dân 115', '2024-07-16', 'pending');

-- Chi tiết đơn thuốc
INSERT INTO jos_vm_prescription_items (
    prescription_id, product_id, quantity, dosage_instruction, duration, price, total_price
) VALUES 
(1, 3, 20, '1 viên x 3 lần/ngày sau ăn', '7 ngày', 12000, 240000),
(1, 5, 14, '1 viên/ngày trước ăn sáng', '14 ngày', 40000, 560000),

(2, 1, 10, '1-2 viên khi đau', '5 ngày', 3000, 30000),
(2, 4, 7, '1 viên/ngày hòa với nước', '7 ngày', 25000, 175000),

(3, 2, 15, '1 viên x 2 lần/ngày sau ăn', '7 ngày', 7500, 112500);

-- Cập nhật tổng tiền đơn thuốc
UPDATE jos_vm_prescriptions SET total_amount = 800000 WHERE id = 1;
UPDATE jos_vm_prescriptions SET total_amount = 205000 WHERE id = 2;
UPDATE jos_vm_prescriptions SET total_amount = 112500 WHERE id = 3;

-- Dữ liệu cảnh báo tương tác thuốc
INSERT INTO jos_vm_drug_interactions (
    drug1_id, drug2_id, interaction_type, description, recommendation, severity_level
) VALUES 
(2, 5, 'moderate', 
'Ibuprofen có thể làm giảm hiệu quả của Omeprazole và tăng nguy cơ loét dạ dày',
'Theo dõi triệu chứng đau bụng, uống Ibuprofen sau ăn', 2),

(3, 1, 'minor', 
'Amoxicillin và Paracetamol có thể dùng chung nhưng cần theo dõi chức năng gan',
'Kiểm tra chức năng gan nếu dùng kéo dài', 1);

-- =====================================================
-- Stored Procedures hữu ích
-- =====================================================

DELIMITER //

-- Procedure kiểm tra tồn kho
CREATE PROCEDURE CheckLowStock()
BEGIN
    SELECT 
        p.product_name,
        pi.quantity_in_stock,
        pi.minimum_stock_level,
        (pi.minimum_stock_level - pi.quantity_in_stock) as shortage
    FROM jos_vm_pharma_inventory pi
    JOIN jos_vm_products p ON pi.product_id = p.virtuemart_product_id
    WHERE pi.quantity_in_stock <= pi.minimum_stock_level
    AND pi.status = 'active'
    ORDER BY shortage DESC;
END //

-- Procedure kiểm tra hạn sử dụng
CREATE PROCEDURE CheckExpiringProducts(IN days_ahead INT)
BEGIN
    SELECT 
        p.product_name,
        pi.batch_number,
        pi.expiry_date,
        pi.quantity_in_stock,
        DATEDIFF(pi.expiry_date, CURDATE()) as days_to_expiry
    FROM jos_vm_pharma_inventory pi
    JOIN jos_vm_products p ON pi.product_id = p.virtuemart_product_id
    WHERE pi.expiry_date <= DATE_ADD(CURDATE(), INTERVAL days_ahead DAY)
    AND pi.status = 'active'
    ORDER BY pi.expiry_date ASC;
END //

-- Procedure tìm kiếm tương tác thuốc
CREATE PROCEDURE CheckDrugInteractions(IN drug1 INT, IN drug2 INT)
BEGIN
    SELECT 
        di.interaction_type,
        di.description,
        di.recommendation,
        di.severity_level
    FROM jos_vm_drug_interactions di
    WHERE (di.drug1_id = drug1 AND di.drug2_id = drug2)
       OR (di.drug1_id = drug2 AND di.drug2_id = drug1);
END //

DELIMITER ;

-- =====================================================
-- Triggers để tự động cập nhật
-- =====================================================

-- Trigger cập nhật tổng tiền đơn thuốc
DELIMITER //
CREATE TRIGGER update_prescription_total 
AFTER INSERT ON jos_vm_prescription_items
FOR EACH ROW
BEGIN
    UPDATE jos_vm_prescriptions 
    SET total_amount = (
        SELECT SUM(total_price) 
        FROM jos_vm_prescription_items 
        WHERE prescription_id = NEW.prescription_id
    )
    WHERE id = NEW.prescription_id;
END //

-- Trigger cảnh báo tồn kho thấp
CREATE TRIGGER low_stock_alert 
AFTER UPDATE ON jos_vm_pharma_inventory
FOR EACH ROW
BEGIN
    IF NEW.quantity_in_stock <= NEW.minimum_stock_level THEN
        INSERT INTO jos_vm_system_alerts (alert_type, message, product_id, created_date)
        VALUES ('LOW_STOCK', 
                CONCAT('Sản phẩm ID ', NEW.product_id, ' sắp hết hàng. Tồn kho: ', NEW.quantity_in_stock),
                NEW.product_id, 
                NOW());
    END IF;
END //

DELIMITER ;

COMMIT;
