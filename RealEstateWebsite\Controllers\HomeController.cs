using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstateWebsite.Data;
using RealEstateWebsite.Models;
using RealEstateWebsite.Models.ViewModels;

namespace RealEstateWebsite.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly RealEstateContext _context;

    public HomeController(ILogger<HomeController> logger, RealEstateContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        var viewModel = new HomeViewModel
        {
            FeaturedProperties = await _context.Properties
                .Include(p => p.Category)
                .Where(p => p.IsFeatured && p.IsAvailable)
                .OrderByDescending(p => p.CreatedDate)
                .Take(6)
                .ToListAsync(),

            LatestProperties = await _context.Properties
                .Include(p => p.Category)
                .Where(p => p.IsAvailable)
                .OrderByDescending(p => p.CreatedDate)
                .Take(8)
                .ToListAsync(),

            Categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync(),

            Statistics = new HomeStatistics
            {
                TotalProperties = await _context.Properties.CountAsync(),
                PropertiesForSale = await _context.Properties.CountAsync(p => p.Type == PropertyType.Sale),
                PropertiesForRent = await _context.Properties.CountAsync(p => p.Type == PropertyType.Rent),
                SoldProperties = await _context.Properties.CountAsync(p => p.Status == PropertyStatus.Sold),
                HappyCustomers = 1250, // Static for demo
                YearsExperience = 15 // Static for demo
            }
        };

        return View(viewModel);
    }

    public IActionResult About()
    {
        return View();
    }

    public IActionResult Contact()
    {
        return View();
    }

    [HttpPost]
    public async Task<IActionResult> Contact(Contact contact)
    {
        if (ModelState.IsValid)
        {
            contact.CreatedDate = DateTime.Now;
            contact.Status = ContactStatus.New;

            _context.Contacts.Add(contact);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi trong thời gian sớm nhất.";
            return RedirectToAction(nameof(Contact));
        }

        return View(contact);
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
