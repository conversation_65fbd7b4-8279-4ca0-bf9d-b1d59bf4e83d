/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-mwgjkbj2lw] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-mwgjkbj2lw] {
  color: #0077cc;
}

.btn-primary[b-mwgjkbj2lw] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-mwgjkbj2lw], .nav-pills .show > .nav-link[b-mwgjkbj2lw] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-mwgjkbj2lw] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-mwgjkbj2lw] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-mwgjkbj2lw] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-mwgjkbj2lw] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-mwgjkbj2lw] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
