﻿@model IEnumerable<PetCareWebsite.Models.Service>
@{
    ViewData["Title"] = "Trang Chủ";
}

<!-- Hero Section -->
<div class="hero-section text-white py-5 mb-5 rounded-4 glass-effect">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 slide-in-left">
                <h1 class="display-4 fw-bold mb-4 floating">🐾 Chăm Sóc Thú Cưng Tận Tâm</h1>
                <p class="lead mb-4 slide-in-up">
                    Trung tâm chăm sóc thú cưng chuyên nghiệp với đội ngũ bác sĩ thú y giàu kinh nghiệm.
                    Chúng tôi cung cấp dịch vụ chăm sóc sức khỏe toàn diện cho những người bạn bốn chân của bạn.
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="@Url.Action("Index", "Services")" class="btn btn-light btn-lg pulse glow">
                        <span class="bounce-heart">💖</span> Xem <PERSON>
                    </a>
                    @if (User.Identity?.IsAuthenticated != true)
                    {
                        <a href="@Url.Action("Register", "Account")" class="btn btn-outline-light btn-lg wiggle">
                            🎉 Đăng Ký Ngay
                        </a>
                    }
                </div>
            </div>
            <div class="col-lg-6 text-center slide-in-right">
                <div class="hero-image-container">
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>
                    <div class="sparkle"></div>

                    <!-- Main Pet Circle -->
                    <div class="pet-circle main-pet floating">
                        <span style="font-size: 6rem;">🐕</span>
                    </div>

                    <!-- Orbiting Pets -->
                    <div class="pet-circle orbit-1 floating-delayed">
                        <span style="font-size: 3rem;">🐱</span>
                    </div>
                    <div class="pet-circle orbit-2 floating">
                        <span style="font-size: 2.5rem;">🐰</span>
                    </div>
                    <div class="pet-circle orbit-3 floating-delayed">
                        <span style="font-size: 2rem;">🐦</span>
                    </div>
                    <div class="pet-circle orbit-4 floating">
                        <span style="font-size: 2rem;">🐹</span>
                    </div>
                    <div class="pet-circle orbit-5 floating-delayed">
                        <span style="font-size: 1.8rem;">🐢</span>
                    </div>

                    <!-- Floating Hearts -->
                    <div class="floating-heart heart-1">💖</div>
                    <div class="floating-heart heart-2">💕</div>
                    <div class="floating-heart heart-3">💗</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container mb-5">
    <div class="row text-center mb-5">
        <div class="col-12">
            <h2 class="display-5 fw-bold gradient-text mb-3">🌟 Tại Sao Chọn Chúng Tôi?</h2>
            <p class="lead text-muted">Chúng tôi cam kết mang đến dịch vụ chăm sóc thú cưng tốt nhất</p>
        </div>
    </div>
    <div class="row g-4">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm glass-effect">
                <div class="card-body text-center p-4">
                    <div class="bg-gradient-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 pulse" style="width: 80px; height: 80px;">
                        <span style="font-size: 2rem;">👨‍⚕️</span>
                    </div>
                    <h5 class="card-title gradient-text">Bác Sĩ Chuyên Nghiệp</h5>
                    <p class="card-text text-muted">Đội ngũ bác sĩ thú y giàu kinh nghiệm, được đào tạo bài bản</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm glass-effect">
                <div class="card-body text-center p-4">
                    <div class="bg-gradient-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 pulse" style="width: 80px; height: 80px; animation-delay: 0.5s;">
                        <span style="font-size: 2rem;">⏰</span>
                    </div>
                    <h5 class="card-title gradient-text">Phục Vụ 24/7</h5>
                    <p class="card-text text-muted">Luôn sẵn sàng phục vụ trong các trường hợp khẩn cấp</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm glass-effect">
                <div class="card-body text-center p-4">
                    <div class="bg-gradient-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 pulse" style="width: 80px; height: 80px; animation-delay: 1s;">
                        <span style="font-size: 2rem;">🏆</span>
                    </div>
                    <h5 class="card-title gradient-text">Chất Lượng Cao</h5>
                    <p class="card-text text-muted">Sử dụng thiết bị hiện đại và phương pháp điều trị tiên tiến</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Services -->
<div class="container mb-5">
    <div class="row text-center mb-5">
        <div class="col-12">
            <h2 class="display-5 fw-bold gradient-text mb-3">💖 Dịch Vụ Nổi Bật</h2>
            <p class="lead text-muted">Các dịch vụ chăm sóc thú cưng được yêu thích nhất</p>
        </div>
    </div>
    <div class="row g-4">
        @{
            var serviceEmojis = new[] { "🛁", "✂️", "🩺", "💉", "🧖‍♀️", "🦷" };
            var gradients = new[] { "bg-gradient-primary", "bg-gradient-success", "bg-gradient-warning", "bg-gradient-info", "bg-gradient-cute", "bg-gradient-pet" };
        }
        @foreach (var (service, index) in Model.Take(6).Select((s, i) => (s, i)))
        {
            <div class="col-lg-4 col-md-6 slide-in-up" style="animation-delay: @(index * 0.1)s;">
                <div class="card h-100 border-0 shadow-sm service-card hover-lift hover-glow">
                    <div class="card-img-top @gradients[index] d-flex align-items-center justify-content-center text-white" style="height: 200px;">
                        <div class="text-center">
                            <span style="font-size: 4rem;" class="floating bounce-heart">@serviceEmojis[index]</span>
                            <h6 class="mt-2 fw-bold">@service.Name</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title gradient-text">@service.Name</h5>
                        <p class="card-text text-muted">@service.Description.Substring(0, Math.Min(100, service.Description.Length))...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h5 text-success mb-0 bounce-heart">💰 @service.Price.ToString("N0") VNĐ</span>
                            <small class="text-muted pulse">⏱️ @service.DurationMinutes phút</small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <a href="@Url.Action("Details", "Services", new { id = service.Id })" class="btn btn-primary w-100 glow">
                            ✨ Xem Chi Tiết
                        </a>
                    </div>
                </div>
            </div>
        }
    </div>
    <div class="text-center mt-4">
        <a href="@Url.Action("Index", "Services")" class="btn btn-outline-primary btn-lg pulse">
            <i class="fas fa-list me-2"></i>Xem Tất Cả Dịch Vụ
        </a>
    </div>
</div>

<style>
/* Page specific enhancements */
.hero-section {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2d3748 100%) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* All styles are now in dark-theme.css */
</style>
