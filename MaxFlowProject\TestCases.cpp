/*
 * FILE TEST CASES VÀ VÍ DỤ MINH HỌA
 * 
 * File này chứa các test case để kiểm tra tính đúng đắn của thuật toán
 * Ford-Fulkerson và các ví dụ minh họa chi tiết
 */

#include "Graph.h"
#include <cassert>
#include <iostream>

using namespace std;

namespace TestCases {

// Test case 1: Đồ thị đơn giản
void testCase1() {
    cout << "\n=== TEST CASE 1: ĐỒ THỊ ĐƠN GIẢN ===\n";
    cout << "Mô tả: Kiểm tra thuật toán với đồ thị 4 đỉnh cơ bản\n";
    cout << "Kết quả mong đợi: Luồng cực đại = 23\n\n";
    
    Graph g(4);
    g.addEdge(0, 1, 16);
    g.addEdge(0, 2, 13);
    g.addEdge(1, 2, 10);
    g.addEdge(1, 3, 12);
    g.addEdge(2, 1, 4);
    g.addEdge(2, 3, 14);
    
    g.displayAdjacencyMatrix();
    
    int result = g.ford<PERSON><PERSON>on(0, 3);
    cout << "Kết quả thực tế: " << result << "\n";
    
    if (result == 23) {
        cout << "✓ TEST CASE 1 PASSED!\n";
    } else {
        cout << "✗ TEST CASE 1 FAILED!\n";
    }
    
    g.findMinCut(0, 3);
}

// Test case 2: Đồ thị phức tạp
void testCase2() {
    cout << "\n=== TEST CASE 2: ĐỒ THỊ PHỨC TẠP ===\n";
    cout << "Mô tả: Kiểm tra thuật toán với đồ thị 6 đỉnh có nhiều đường đi\n";
    cout << "Kết quả mong đợi: Luồng cực đại = 15\n\n";
    
    Graph g(6);
    g.addEdge(0, 1, 10);
    g.addEdge(0, 2, 8);
    g.addEdge(1, 2, 5);
    g.addEdge(1, 3, 5);
    g.addEdge(2, 4, 10);
    g.addEdge(3, 2, 7);
    g.addEdge(3, 4, 8);
    g.addEdge(3, 5, 10);
    g.addEdge(4, 5, 10);
    
    g.displayAdjacencyMatrix();
    
    int result = g.fordFulkerson(0, 5);
    cout << "Kết quả thực tế: " << result << "\n";
    
    if (result == 15) {
        cout << "✓ TEST CASE 2 PASSED!\n";
    } else {
        cout << "✗ TEST CASE 2 FAILED!\n";
    }
    
    g.findMinCut(0, 5);
}

// Test case 3: Đồ thị tuyến tính
void testCase3() {
    cout << "\n=== TEST CASE 3: ĐỒ THỊ TUYẾN TÍNH ===\n";
    cout << "Mô tả: Đồ thị dạng chuỗi tuyến tính\n";
    cout << "Kết quả mong đợi: Luồng cực đại = 5 (bị giới hạn bởi cạnh nhỏ nhất)\n\n";
    
    Graph g(5);
    g.addEdge(0, 1, 10);
    g.addEdge(1, 2, 5);   // Cạnh nghẽn
    g.addEdge(2, 3, 15);
    g.addEdge(3, 4, 20);
    
    g.displayAdjacencyMatrix();
    
    int result = g.fordFulkerson(0, 4);
    cout << "Kết quả thực tế: " << result << "\n";
    
    if (result == 5) {
        cout << "✓ TEST CASE 3 PASSED!\n";
    } else {
        cout << "✗ TEST CASE 3 FAILED!\n";
    }
    
    g.findMinCut(0, 4);
}

// Test case 4: Đồ thị không có đường đi
void testCase4() {
    cout << "\n=== TEST CASE 4: KHÔNG CÓ ĐƯỜNG ĐI ===\n";
    cout << "Mô tả: Không có đường đi từ nguồn đến đích\n";
    cout << "Kết quả mong đợi: Luồng cực đại = 0\n\n";
    
    Graph g(4);
    g.addEdge(0, 1, 10);
    g.addEdge(2, 3, 15);
    // Không có đường từ 0 đến 3
    
    g.displayAdjacencyMatrix();
    
    int result = g.fordFulkerson(0, 3);
    cout << "Kết quả thực tế: " << result << "\n";
    
    if (result == 0) {
        cout << "✓ TEST CASE 4 PASSED!\n";
    } else {
        cout << "✗ TEST CASE 4 FAILED!\n";
    }
}

// Test case 5: Đồ thị với chu trình
void testCase5() {
    cout << "\n=== TEST CASE 5: ĐỒ THỊ CÓ CHU TRÌNH ===\n";
    cout << "Mô tả: Đồ thị có chu trình, kiểm tra xử lý luồng ngược\n";
    cout << "Kết quả mong đợi: Luồng cực đại = 19\n\n";
    
    Graph g(4);
    g.addEdge(0, 1, 10);
    g.addEdge(0, 2, 10);
    g.addEdge(1, 2, 2);
    g.addEdge(1, 3, 4);
    g.addEdge(2, 1, 6);
    g.addEdge(2, 3, 10);
    
    g.displayAdjacencyMatrix();
    
    int result = g.fordFulkerson(0, 3);
    cout << "Kết quả thực tế: " << result << "\n";
    
    if (result == 19) {
        cout << "✓ TEST CASE 5 PASSED!\n";
    } else {
        cout << "✗ TEST CASE 5 FAILED!\n";
    }
    
    g.findMinCut(0, 3);
}

// Chạy tất cả test cases
void runAllTests() {
    cout << "\n";
    cout << "╔══════════════════════════════════════════════════════════════════════════════╗\n";
    cout << "║                           CHẠY TẤT CẢ TEST CASES                            ║\n";
    cout << "╚══════════════════════════════════════════════════════════════════════════════╝\n";
    
    testCase1();
    cout << "\n" << string(80, '=') << "\n";
    
    testCase2();
    cout << "\n" << string(80, '=') << "\n";
    
    testCase3();
    cout << "\n" << string(80, '=') << "\n";
    
    testCase4();
    cout << "\n" << string(80, '=') << "\n";
    
    testCase5();
    
    cout << "\n";
    cout << "╔══════════════════════════════════════════════════════════════════════════════╗\n";
    cout << "║                         HOÀN THÀNH TẤT CẢ TEST CASES                        ║\n";
    cout << "╚══════════════════════════════════════════════════════════════════════════════╝\n";
}

// Ví dụ minh họa chi tiết từng bước
void detailedExample() {
    cout << "\n";
    cout << "╔══════════════════════════════════════════════════════════════════════════════╗\n";
    cout << "║                        VÍ DỤ MINH HỌA CHI TIẾT                              ║\n";
    cout << "╚══════════════════════════════════════════════════════════════════════════════╝\n";
    
    cout << "\nVí dụ: Mạng phân phối nước trong thành phố\n";
    cout << "- Đỉnh 0: Nhà máy nước (nguồn)\n";
    cout << "- Đỉnh 1: Trạm bơm A\n";
    cout << "- Đỉnh 2: Trạm bơm B\n";
    cout << "- Đỉnh 3: Khu dân cư (đích)\n";
    cout << "- Số trên cạnh: Lưu lượng tối đa (lít/giây)\n\n";
    
    Graph waterNetwork(4);
    waterNetwork.addEdge(0, 1, 20);  // Nhà máy -> Trạm A: 20 l/s
    waterNetwork.addEdge(0, 2, 15);  // Nhà máy -> Trạm B: 15 l/s
    waterNetwork.addEdge(1, 2, 8);   // Trạm A -> Trạm B: 8 l/s
    waterNetwork.addEdge(1, 3, 10);  // Trạm A -> Khu dân cư: 10 l/s
    waterNetwork.addEdge(2, 3, 25);  // Trạm B -> Khu dân cư: 25 l/s
    
    cout << "Sơ đồ mạng phân phối:\n";
    waterNetwork.displayAdjacencyMatrix();
    
    cout << "Câu hỏi: Lưu lượng nước tối đa có thể cung cấp cho khu dân cư là bao nhiêu?\n";
    
    int maxFlow = waterNetwork.fordFulkerson(0, 3);
    
    cout << "\nKết luận:\n";
    cout << "- Lưu lượng nước tối đa: " << maxFlow << " lít/giây\n";
    cout << "- Điều này có nghĩa là dù nhà máy có thể sản xuất nhiều hơn,\n";
    cout << "  nhưng do giới hạn của hệ thống đường ống, chỉ có thể cung cấp " << maxFlow << " l/s\n";
    
    waterNetwork.findMinCut(0, 3);
    cout << "- Lát cắt tối thiểu cho biết điểm nghẽn trong hệ thống\n";
    cout << "- Để tăng lưu lượng, cần nâng cấp các đường ống trong lát cắt này\n";
}

// Menu test cases
void showTestMenu() {
    int choice;
    do {
        cout << "\n";
        cout << "╔═══════════════════════════════════════════════════════════════════════════╗\n";
        cout << "║                            MENU TEST CASES                               ║\n";
        cout << "╠═══════════════════════════════════════════════════════════════════════════╣\n";
        cout << "║  1. Test Case 1: Đồ thị đơn giản                                         ║\n";
        cout << "║  2. Test Case 2: Đồ thị phức tạp                                         ║\n";
        cout << "║  3. Test Case 3: Đồ thị tuyến tính                                       ║\n";
        cout << "║  4. Test Case 4: Không có đường đi                                       ║\n";
        cout << "║  5. Test Case 5: Đồ thị có chu trình                                     ║\n";
        cout << "║  6. Chạy tất cả test cases                                               ║\n";
        cout << "║  7. Ví dụ minh họa chi tiết                                              ║\n";
        cout << "║  0. Quay lại menu chính                                                  ║\n";
        cout << "╚═══════════════════════════════════════════════════════════════════════════╝\n";
        cout << "Nhập lựa chọn: ";
        cin >> choice;
        
        switch (choice) {
            case 1: testCase1(); break;
            case 2: testCase2(); break;
            case 3: testCase3(); break;
            case 4: testCase4(); break;
            case 5: testCase5(); break;
            case 6: runAllTests(); break;
            case 7: detailedExample(); break;
            case 0: cout << "Quay lại menu chính...\n"; break;
            default: cout << "Lựa chọn không hợp lệ!\n";
        }
        
        if (choice != 0) {
            cout << "\nNhấn Enter để tiếp tục...";
            cin.ignore();
            cin.get();
        }
    } while (choice != 0);
}

} // namespace TestCases
