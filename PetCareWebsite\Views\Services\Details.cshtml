@model PetCareWebsite.Models.Service
@{
    ViewData["Title"] = Model.Name;
}

<div class="container">
    <div class="row">
        <!-- Service Image -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="bg-gradient-primary d-flex align-items-center justify-content-center text-white" style="height: 400px;">
                    <div class="text-center">
                        <i class="fas fa-paw fa-5x mb-3"></i>
                        <h4>@Model.Name</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Details -->
        <div class="col-lg-6">
            <div class="mb-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang Chủ</a></li>
                        <li class="breadcrumb-item"><a asp-controller="Services" asp-action="Index">Dịch Vụ</a></li>
                        <li class="breadcrumb-item active" aria-current="page">@Model.Name</li>
                    </ol>
                </nav>
            </div>

            <h1 class="display-5 fw-bold text-primary mb-3">@Model.Name</h1>
            
            <div class="row mb-4">
                <div class="col-6">
                    <div class="card bg-success text-white text-center">
                        <div class="card-body">
                            <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                            <h4 class="mb-0">@Model.Price.ToString("N0") VNĐ</h4>
                            <small>Giá dịch vụ</small>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card bg-info text-white text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h4 class="mb-0">@Model.DurationMinutes phút</h4>
                            <small>Thời gian thực hiện</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h5 class="fw-bold text-dark mb-3">
                    <i class="fas fa-info-circle me-2"></i>Mô Tả Dịch Vụ
                </h5>
                <p class="text-muted lh-lg">@Model.Description</p>
            </div>

            <div class="mb-4">
                <h5 class="fw-bold text-dark mb-3">
                    <i class="fas fa-check-circle me-2"></i>Quy Trình Thực Hiện
                </h5>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        Đăng ký lịch hẹn trước ít nhất 1 ngày
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        Mang theo thú cưng đúng giờ hẹn
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        Bác sĩ thú y sẽ kiểm tra sức khỏe tổng quát
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        Thực hiện dịch vụ theo yêu cầu
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        Tư vấn chăm sóc sau khi hoàn thành
                    </li>
                </ul>
            </div>

            <div class="d-grid gap-2">
                @if (User.Identity?.IsAuthenticated == true)
                {
                    <a href="@Url.Action("Create", "Appointments", new { serviceId = Model.Id })" class="btn btn-primary btn-lg">
                        <i class="fas fa-calendar-plus me-2"></i>Đặt Lịch Hẹn Ngay
                    </a>
                }
                else
                {
                    <a href="@Url.Action("Login", "Account")" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Đăng Nhập Để Đặt Lịch
                    </a>
                }
                <a href="@Url.Action("Index", "Services")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Quay Lại Danh Sách Dịch Vụ
                </a>
            </div>
        </div>
    </div>

    <!-- Related Services -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="fw-bold text-primary mb-4">
                <i class="fas fa-heart me-2"></i>Dịch Vụ Khác
            </h3>
            <div class="text-center">
                <a href="@Url.Action("Index", "Services")" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>Xem Tất Cả Dịch Vụ
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}
</style>
