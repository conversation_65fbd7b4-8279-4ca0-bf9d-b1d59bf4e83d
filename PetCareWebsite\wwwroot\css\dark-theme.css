/* Modern Dark Theme for Pet Care Website */

/* Global Dark Theme */
* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%) !important;
    background-attachment: fixed !important;
    min-height: 100vh !important;
    color: #e0e0e0 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    line-height: 1.6 !important;
}

/* Page Background */
.page-bg {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: -1 !important;
    background: 
        radial-gradient(circle at 20% 80%, rgba(30, 60, 114, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(45, 55, 72, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(26, 32, 44, 0.3) 0%, transparent 50%) !important;
    animation: backgroundShift 30s ease-in-out infinite !important;
}

@keyframes backgroundShift {
    0%, 100% { 
        background: 
            radial-gradient(circle at 20% 80%, rgba(30, 60, 114, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(45, 55, 72, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(26, 32, 44, 0.3) 0%, transparent 50%);
    }
    50% { 
        background: 
            radial-gradient(circle at 80% 20%, rgba(45, 55, 72, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 20% 80%, rgba(26, 32, 44, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(30, 60, 114, 0.3) 0%, transparent 50%);
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: #f8fafc !important;
    font-weight: 600 !important;
}

.text-muted {
    color: #a0aec0 !important;
}

.text-success {
    color: #68d391 !important;
}

.text-primary {
    color: #63b3ed !important;
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #63b3ed 0%, #4299e1 50%, #3182ce 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 600 !important;
}

/* Cards */
.card {
    background: rgba(26, 32, 44, 0.8) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 16px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2) !important;
    border-color: rgba(99, 179, 237, 0.3) !important;
}

.card-title {
    color: #63b3ed !important;
}

.card-text {
    color: #cbd5e0 !important;
}

/* Glass Effect */
.glass-effect {
    background: rgba(26, 32, 44, 0.8) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2d3748 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.hero-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: radial-gradient(circle at center, rgba(99, 179, 237, 0.1) 0%, transparent 70%) !important;
    z-index: 1 !important;
}

/* Service Cards */
.service-card {
    background: rgba(26, 32, 44, 0.9) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 16px !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    position: relative !important;
}

.service-card:hover {
    transform: translateY(-6px) !important;
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3) !important;
    border-color: rgba(99, 179, 237, 0.4) !important;
}

.service-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(99, 179, 237, 0.1), transparent) !important;
    transition: left 0.6s ease !important;
}

.service-card:hover::before {
    left: 100% !important;
}

/* Buttons */
.btn {
    border-radius: 12px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.4) !important;
}

.btn-outline-primary {
    border: 2px solid #4299e1 !important;
    color: #63b3ed !important;
    background: transparent !important;
}

.btn-outline-primary:hover {
    background: #4299e1 !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.3) !important;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
    color: white !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
    transform: translateY(-2px) !important;
}

/* Navbar */
.navbar {
    background: rgba(26, 32, 44, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.navbar-brand {
    background: linear-gradient(135deg, #63b3ed 0%, #4299e1 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 700 !important;
}

.nav-link {
    color: #e2e8f0 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.nav-link:hover {
    color: #63b3ed !important;
    transform: translateY(-1px) !important;
}

/* Dropdown */
.dropdown-menu {
    background: rgba(26, 32, 44, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
}

.dropdown-item {
    color: #e2e8f0 !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: rgba(99, 179, 237, 0.2) !important;
    color: #63b3ed !important;
}

/* Form Controls */
.form-control {
    background: rgba(26, 32, 44, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #e2e8f0 !important;
    border-radius: 12px !important;
}

.form-control:focus {
    background: rgba(26, 32, 44, 0.9) !important;
    border-color: #4299e1 !important;
    box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25) !important;
    color: #f7fafc !important;
}

.form-control::placeholder {
    color: #a0aec0 !important;
}

/* Animations */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.floating {
    animation: float 6s ease-in-out infinite !important;
}

.floating-delayed {
    animation: float 6s ease-in-out infinite !important;
    animation-delay: 2s !important;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 3s ease-in-out infinite !important;
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 10px rgba(99, 179, 237, 0.3); }
    50% { box-shadow: 0 0 20px rgba(99, 179, 237, 0.6), 0 0 30px rgba(99, 179, 237, 0.4); }
}

.glow {
    animation: glow 4s ease-in-out infinite !important;
}

/* Slide Animations */
@keyframes slideInLeft {
    0% { transform: translateX(-30px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out !important;
}

@keyframes slideInRight {
    0% { transform: translateX(30px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out !important;
}

@keyframes slideInUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

.slide-in-up {
    animation: slideInUp 0.8s ease-out !important;
}

/* Gradients for Service Cards */
.bg-gradient-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4fd1c7 0%, #38b2ac 100%) !important;
}

.bg-gradient-cute {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%) !important;
}

.bg-gradient-pet {
    background: linear-gradient(135deg, #ed64a6 0%, #d53f8c 100%) !important;
}
