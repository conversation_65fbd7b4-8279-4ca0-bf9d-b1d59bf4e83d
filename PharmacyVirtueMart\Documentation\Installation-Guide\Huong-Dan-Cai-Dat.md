# Hướng Dẫn Cài Đặt Website Dược Phẩm VirtueMart

## 1. <PERSON><PERSON><PERSON> bị môi trường

### Y<PERSON>u cầu hệ thống:
- **Web Server**: Apache 2.4+ hoặc Nginx 1.18+
- **PHP**: <PERSON><PERSON><PERSON> bản 7.4 - 8.1
- **MySQL**: <PERSON><PERSON><PERSON> bản 5.7+ hoặc MariaDB 10.3+
- **RAM**: T<PERSON><PERSON> thiểu 512MB, khuyến nghị 1GB+
- **Dung lượng**: Tối thiểu 500MB

### PHP Extensions cần thiết:
```
- php-mysql
- php-curl
- php-gd
- php-zip
- php-xml
- php-mbstring
- php-json
- php-openssl
```

## 2. Cài đặt XAMPP/WAMP (Môi trường local)

### Tải và cài đặt XAMPP:
1. Truy cập https://www.apachefriends.org/
2. Tải phiên bản XAMPP phù hợp với hệ điều hành
3. Cài đặt với các component: Apache, MySQL, PHP, phpMyAdmin

### Khởi động services:
```bash
# Khởi động Apache
sudo /opt/lampp/lampp startapache

# Khởi động MySQL
sudo /opt/lampp/lampp startmysql
```

## 3. Tạo cơ sở dữ liệu MySQL

### Truy cập phpMyAdmin:
- URL: http://localhost/phpmyadmin
- Username: root
- Password: (để trống hoặc theo cấu hình)

### Tạo database:
```sql
CREATE DATABASE pharmacy_virtuemart 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Tạo user riêng (khuyến nghị)
CREATE USER 'pharmacy_user'@'localhost' 
IDENTIFIED BY 'pharmacy_password';

GRANT ALL PRIVILEGES ON pharmacy_virtuemart.* 
TO 'pharmacy_user'@'localhost';

FLUSH PRIVILEGES;
```

## 4. Tải và cài đặt Joomla

### Tải Joomla:
1. Truy cập https://www.joomla.org/download.html
2. Tải phiên bản Joomla 4.x mới nhất
3. Giải nén vào thư mục web root (htdocs/www)

### Cài đặt Joomla:
1. Truy cập http://localhost/joomla-folder
2. Chọn ngôn ngữ: Tiếng Việt
3. Điền thông tin cấu hình:
   - **Site Name**: Website Dược Phẩm
   - **Admin Email**: <EMAIL>
   - **Admin Username**: admin
   - **Admin Password**: [mật khẩu mạnh]

### Cấu hình database:
- **Database Type**: MySQLi
- **Host Name**: localhost
- **Username**: pharmacy_user
- **Password**: pharmacy_password
- **Database Name**: pharmacy_virtuemart
- **Table Prefix**: jos_

## 5. Cài đặt VirtueMart

### Tải VirtueMart:
1. Truy cập https://virtuemart.net/downloads
2. Tải phiên bản VirtueMart 4.x
3. Tải AIO (All-in-One) package

### Cài đặt qua Joomla Admin:
1. Đăng nhập Joomla Admin Panel
2. Vào **Extensions** → **Manage** → **Install**
3. Upload file VirtueMart AIO
4. Chờ quá trình cài đặt hoàn tất

### Cấu hình VirtueMart:
1. Vào **Components** → **VirtueMart**
2. Chạy **VirtueMart Installation**
3. Cài đặt Sample Data (dữ liệu mẫu)
4. Cấu hình Shop Information:
   - **Shop Name**: Nhà Thuốc An Khang
   - **Currency**: VND (Vietnamese Dong)
   - **Country**: Vietnam

## 6. Cấu hình bổ sung

### Cài đặt ngôn ngữ Tiếng Việt:
1. **Extensions** → **Languages** → **Install Languages**
2. Tìm và cài đặt Vietnamese language pack
3. Đặt làm ngôn ngữ mặc định

### Cấu hình SEF URLs:
1. **System** → **Global Configuration**
2. **Site** tab → **SEO Settings**
3. Bật **Search Engine Friendly URLs**
4. Bật **Use URL Rewriting**

### Cấu hình Email:
1. **System** → **Global Configuration**
2. **Server** tab → **Mail Settings**
3. Cấu hình SMTP hoặc PHP Mail

## 7. Kiểm tra cài đặt

### Kiểm tra Frontend:
- Truy cập http://localhost/joomla-folder
- Kiểm tra hiển thị trang chủ
- Kiểm tra menu VirtueMart

### Kiểm tra Backend:
- Truy cập http://localhost/joomla-folder/administrator
- Đăng nhập với tài khoản admin
- Kiểm tra VirtueMart component

## 8. Xử lý lỗi thường gặp

### Lỗi database connection:
```
- Kiểm tra thông tin database trong configuration.php
- Đảm bảo MySQL service đang chạy
- Kiểm tra quyền user database
```

### Lỗi file permissions:
```bash
# Linux/Mac
chmod -R 755 joomla-folder/
chmod -R 777 joomla-folder/tmp/
chmod -R 777 joomla-folder/logs/
chmod -R 777 joomla-folder/cache/
chmod -R 777 joomla-folder/images/
```

### Lỗi PHP memory limit:
```php
// Trong .htaccess hoặc php.ini
memory_limit = 256M
max_execution_time = 300
```

## Kết luận
Sau khi hoàn thành các bước trên, bạn đã có một website dược phẩm cơ bản với VirtueMart. 
Bước tiếp theo là tùy chỉnh giao diện và nhập dữ liệu sản phẩm.
