using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstateWebsite.Data;
using RealEstateWebsite.Models;
using RealEstateWebsite.Models.ViewModels;

namespace RealEstateWebsite.Controllers;

public class PropertiesController : Controller
{
    private readonly RealEstateContext _context;

    public PropertiesController(RealEstateContext context)
    {
        _context = context;
    }

    public async Task<IActionResult> Index(PropertySearchViewModel searchModel)
    {
        var query = _context.Properties
            .Include(p => p.Category)
            .Where(p => p.IsAvailable);

        // Apply search filters
        if (!string.IsNullOrEmpty(searchModel.Keyword))
        {
            query = query.Where(p => p.Title.Contains(searchModel.Keyword) ||
                                   p.Description.Contains(searchModel.Keyword) ||
                                   p.Address.Contains(searchModel.Keyword));
        }

        if (searchModel.CategoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == searchModel.CategoryId.Value);
        }

        if (searchModel.Type.HasValue)
        {
            query = query.Where(p => p.Type == searchModel.Type.Value);
        }

        if (!string.IsNullOrEmpty(searchModel.City))
        {
            query = query.Where(p => p.City.Contains(searchModel.City));
        }

        if (!string.IsNullOrEmpty(searchModel.District))
        {
            query = query.Where(p => p.District.Contains(searchModel.District));
        }

        if (searchModel.MinPrice.HasValue)
        {
            query = query.Where(p => p.Price >= searchModel.MinPrice.Value);
        }

        if (searchModel.MaxPrice.HasValue)
        {
            query = query.Where(p => p.Price <= searchModel.MaxPrice.Value);
        }

        if (searchModel.MinArea.HasValue)
        {
            query = query.Where(p => p.Area >= searchModel.MinArea.Value);
        }

        if (searchModel.MaxArea.HasValue)
        {
            query = query.Where(p => p.Area <= searchModel.MaxArea.Value);
        }

        if (searchModel.MinBedrooms.HasValue)
        {
            query = query.Where(p => p.Bedrooms >= searchModel.MinBedrooms.Value);
        }

        if (searchModel.MaxBedrooms.HasValue)
        {
            query = query.Where(p => p.Bedrooms <= searchModel.MaxBedrooms.Value);
        }

        if (searchModel.MinBathrooms.HasValue)
        {
            query = query.Where(p => p.Bathrooms >= searchModel.MinBathrooms.Value);
        }

        if (searchModel.MaxBathrooms.HasValue)
        {
            query = query.Where(p => p.Bathrooms <= searchModel.MaxBathrooms.Value);
        }

        // Advanced filters
        if (searchModel.HasParking == true)
        {
            query = query.Where(p => p.HasParking);
        }

        if (searchModel.HasGarden == true)
        {
            query = query.Where(p => p.HasGarden);
        }

        if (searchModel.HasSwimmingPool == true)
        {
            query = query.Where(p => p.HasSwimmingPool);
        }

        if (searchModel.HasElevator == true)
        {
            query = query.Where(p => p.HasElevator);
        }

        if (searchModel.HasBalcony == true)
        {
            query = query.Where(p => p.HasBalcony);
        }

        if (searchModel.HasAirConditioning == true)
        {
            query = query.Where(p => p.HasAirConditioning);
        }

        if (searchModel.HasSecurity == true)
        {
            query = query.Where(p => p.HasSecurity);
        }

        if (!string.IsNullOrEmpty(searchModel.Orientation))
        {
            query = query.Where(p => p.Orientation == searchModel.Orientation);
        }

        if (searchModel.YearBuiltFrom.HasValue)
        {
            query = query.Where(p => p.YearBuilt >= searchModel.YearBuiltFrom.Value);
        }

        if (searchModel.YearBuiltTo.HasValue)
        {
            query = query.Where(p => p.YearBuilt <= searchModel.YearBuiltTo.Value);
        }

        // Apply sorting
        query = searchModel.SortBy switch
        {
            PropertySortBy.Price => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Price) 
                : query.OrderByDescending(p => p.Price),
            PropertySortBy.Area => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Area) 
                : query.OrderByDescending(p => p.Area),
            PropertySortBy.Title => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Title) 
                : query.OrderByDescending(p => p.Title),
            PropertySortBy.City => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.City) 
                : query.OrderByDescending(p => p.City),
            PropertySortBy.Bedrooms => searchModel.SortDirection == SortDirection.Ascending 
                ? query.OrderBy(p => p.Bedrooms) 
                : query.OrderByDescending(p => p.Bedrooms),
            _ => query.OrderByDescending(p => p.CreatedDate)
        };

        // Get total count for pagination
        var totalItems = await query.CountAsync();

        // Apply pagination
        var properties = await query
            .Skip((searchModel.Page - 1) * searchModel.PageSize)
            .Take(searchModel.PageSize)
            .ToListAsync();

        // Get filter data
        var categories = await _context.Categories
            .Where(c => c.IsActive)
            .OrderBy(c => c.DisplayOrder)
            .ToListAsync();

        var cities = await _context.Properties
            .Where(p => p.IsAvailable)
            .Select(p => p.City)
            .Distinct()
            .OrderBy(c => c)
            .ToListAsync();

        var districts = await _context.Properties
            .Where(p => p.IsAvailable)
            .Select(p => p.District)
            .Distinct()
            .OrderBy(d => d)
            .ToListAsync();

        // Calculate statistics
        var allProperties = await query.ToListAsync();
        var statistics = new SearchStatistics
        {
            TotalFound = totalItems,
            ForSale = allProperties.Count(p => p.Type == PropertyType.Sale),
            ForRent = allProperties.Count(p => p.Type == PropertyType.Rent),
            MinPrice = allProperties.Any() ? allProperties.Min(p => p.Price) : 0,
            MaxPrice = allProperties.Any() ? allProperties.Max(p => p.Price) : 0,
            AveragePrice = allProperties.Any() ? allProperties.Average(p => p.Price) : 0
        };

        var viewModel = new PropertySearchResultViewModel
        {
            Properties = properties,
            SearchCriteria = searchModel,
            Pagination = new PaginationViewModel
            {
                CurrentPage = searchModel.Page,
                TotalPages = (int)Math.Ceiling((double)totalItems / searchModel.PageSize),
                TotalItems = totalItems,
                PageSize = searchModel.PageSize
            },
            Categories = categories,
            Cities = cities,
            Districts = districts,
            Statistics = statistics
        };

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var property = await _context.Properties
            .Include(p => p.Category)
            .Include(p => p.Images)
            .FirstOrDefaultAsync(p => p.PropertyId == id);

        if (property == null)
        {
            return NotFound();
        }

        // Get related properties
        var relatedProperties = await _context.Properties
            .Include(p => p.Category)
            .Where(p => p.CategoryId == property.CategoryId && 
                       p.PropertyId != property.PropertyId && 
                       p.IsAvailable)
            .Take(4)
            .ToListAsync();

        ViewBag.RelatedProperties = relatedProperties;

        return View(property);
    }

    [HttpPost]
    public async Task<IActionResult> AddToFavorites(int propertyId)
    {
        var sessionId = HttpContext.Session.Id;
        
        var existingFavorite = await _context.Favorites
            .FirstOrDefaultAsync(f => f.PropertyId == propertyId && f.SessionId == sessionId);

        if (existingFavorite == null)
        {
            var favorite = new Favorite
            {
                PropertyId = propertyId,
                SessionId = sessionId,
                CreatedDate = DateTime.Now
            };

            _context.Favorites.Add(favorite);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Đã thêm vào danh sách yêu thích!" });
        }

        return Json(new { success = false, message = "Bất động sản này đã có trong danh sách yêu thích!" });
    }

    [HttpPost]
    public async Task<IActionResult> RemoveFromFavorites(int propertyId)
    {
        var sessionId = HttpContext.Session.Id;
        
        var favorite = await _context.Favorites
            .FirstOrDefaultAsync(f => f.PropertyId == propertyId && f.SessionId == sessionId);

        if (favorite != null)
        {
            _context.Favorites.Remove(favorite);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Đã xóa khỏi danh sách yêu thích!" });
        }

        return Json(new { success = false, message = "Không tìm thấy trong danh sách yêu thích!" });
    }

    public async Task<IActionResult> Favorites()
    {
        var sessionId = HttpContext.Session.Id;
        
        var favorites = await _context.Favorites
            .Include(f => f.Property)
            .ThenInclude(p => p.Category)
            .Where(f => f.SessionId == sessionId)
            .OrderByDescending(f => f.CreatedDate)
            .ToListAsync();

        return View(favorites);
    }
}
