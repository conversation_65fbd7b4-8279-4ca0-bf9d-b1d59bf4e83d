#ifndef SORTING_ALGORITHMS_H
#define SORTING_ALGORITHMS_H

#include <vector>
#include <chrono>
#include <functional>
#include "Student.h"

// C<PERSON>u trúc để lưu thống kê hiệu suất
struct SortingStats {
    std::string algorithmName;
    long long executionTime;    // Thời gian thực thi (microseconds)
    long long comparisons;      // Số lần so sánh
    long long swaps;           // Số lần hoán đổi
    int dataSize;              // <PERSON><PERSON>ch thước dữ liệu
    
    SortingStats() : algorithmName(""), executionTime(0), comparisons(0), swaps(0), dataSize(0) {}
    
    SortingStats(const std::string& name, long long time, long long comp, long long sw, int size)
        : algorithmName(name), executionTime(time), comparisons(comp), swaps(sw), dataSize(size) {}
};

class SortingAlgorithms {
private:
    static long long comparisonCount;
    static long long swapCount;
    
public:
    // Reset counters
    static void resetCounters();
    
    // Get counters
    static long long getComparisonCount();
    static long long getSwapCount();
    
    // Utility functions
    template<typename T>
    static void swap(T& a, T& b);
    
    template<typename T>
    static bool compare(const T& a, const T& b, std::function<bool(const T&, const T&)> compareFn);
    
    // Bubble Sort
    template<typename T>
    static SortingStats bubbleSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn = std::less<T>());
    
    // Selection Sort
    template<typename T>
    static SortingStats selectionSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn = std::less<T>());
    
    // Insertion Sort
    template<typename T>
    static SortingStats insertionSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn = std::less<T>());
    
    // Quick Sort
    template<typename T>
    static SortingStats quickSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn = std::less<T>());
    
    // Merge Sort
    template<typename T>
    static SortingStats mergeSort(std::vector<T>& arr, std::function<bool(const T&, const T&)> compareFn = std::less<T>());
    
    // Helper functions for Quick Sort
    template<typename T>
    static void quickSortHelper(std::vector<T>& arr, int low, int high, std::function<bool(const T&, const T&)> compareFn);
    
    template<typename T>
    static int partition(std::vector<T>& arr, int low, int high, std::function<bool(const T&, const T&)> compareFn);
    
    // Helper functions for Merge Sort
    template<typename T>
    static void mergeSortHelper(std::vector<T>& arr, int left, int right, std::function<bool(const T&, const T&)> compareFn);
    
    template<typename T>
    static void merge(std::vector<T>& arr, int left, int mid, int right, std::function<bool(const T&, const T&)> compareFn);
    
    // Utility function to measure execution time
    template<typename Func>
    static long long measureTime(Func func);
    
    // Display sorting statistics
    static void displayStats(const SortingStats& stats);
    static void compareAlgorithms(const std::vector<SortingStats>& statsVector);
    
    // Test data generation
    static std::vector<Student> generateRandomStudents(int count);
    static std::vector<Student> generateSortedStudents(int count, bool ascending = true);
    static std::vector<Student> generateReverseSortedStudents(int count);
};

// Static member initialization
long long SortingAlgorithms::comparisonCount = 0;
long long SortingAlgorithms::swapCount = 0;

#include "SortingAlgorithms.tpp" // Template implementations

#endif // SORTING_ALGORITHMS_H
