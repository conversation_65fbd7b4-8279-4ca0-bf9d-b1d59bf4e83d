# ĐỒ ÁN TỐT NGHIỆP

## NGHIÊN CỨU BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ VÀ CÀI ĐẶT MINH HỌA

---

**Sinh viên thực hiện:** [Tên sinh viên]  
**Mã số sinh viên:** [MSSV]  
**Lớp:** [Tên lớp]  
**Khoa:** Khoa Học Máy Tính  
**Trường:** [Tên trường]  

**Giảng viên hướng dẫn:** [Tên giảng viên]  

**Thời gian thực hiện:** [Thời gian]

---

## MỤC LỤC

**MỞ ĐẦU**
- Tính cấp thiết của đề tài
- Mục tiêu của đề tài
- Đối tượng và phạm vi nghiên cứu
- Phương pháp nghiên cứu
- Bố cục của đồ án

**CHƯƠNG 1: CƠ SỞ LÝ THUYẾT VỀ ĐỒ THỊ VÀ BÀI TOÁN LUỒNG CỰC ĐẠI**

**CHƯƠNG 2: THUẬT TOÁN FORD-FULKERSON VÀ CÁC CẢI TIẾN**

**CHƯƠNG 3: CÀI ĐẶT CHƯƠNG TRÌNH MINH HỌA**

**KẾT LUẬN VÀ HƯỚNG PHÁT TRIỂN**

**TÀI LIỆU THAM KHẢO**

---

# MỞ ĐẦU

## Tính cấp thiết của đề tài

Trong thời đại công nghệ thông tin phát triển mạnh mẽ như hiện nay, bài toán luồng cực đại (Maximum Flow Problem) đóng vai trò quan trọng trong nhiều lĩnh vực ứng dụng thực tế:

**Trong mạng máy tính và viễn thông:**
- Tối ưu hóa băng thông mạng Internet
- Định tuyến dữ liệu hiệu quả
- Quản lý tài nguyên mạng trong các hệ thống phân tán
- Thiết kế và phân tích độ tin cậy của mạng

**Trong vận tải và logistics:**
- Tối ưu hóa lưu lượng giao thông đô thị
- Thiết kế hệ thống đường bộ, đường sắt
- Quản lý chuỗi cung ứng và phân phối hàng hóa
- Lập kế hoạch vận chuyển tối ưu

**Trong lập kế hoạch sản xuất:**
- Tối ưu hóa dây chuyền sản xuất
- Quản lý nguồn lực và nguyên vật liệu
- Phân bổ công việc trong hệ thống sản xuất

**Trong tài chính và kinh tế:**
- Phân tích rủi ro trong đầu tư
- Tối ưu hóa danh mục đầu tư
- Quản lý dòng tiền trong doanh nghiệp

Việc nghiên cứu và hiểu sâu về bài toán luồng cực đại không chỉ có ý nghĩa lý thuyết mà còn mang tính ứng dụng cao, giúp giải quyết nhiều vấn đề thực tế trong cuộc sống.

## Mục tiêu của đề tài

Đồ án này được thực hiện với các mục tiêu cụ thể sau:

**1. Nghiên cứu cơ sở lý thuyết:**
- Tìm hiểu sâu về lý thuyết đồ thị và các khái niệm cơ bản
- Nghiên cứu bài toán luồng cực đại và các tính chất toán học
- Phân tích mạng luồng, lát cắt và các định lý liên quan

**2. Nghiên cứu thuật toán:**
- Tìm hiểu sâu về thuật toán Ford-Fulkerson và nguyên lý hoạt động
- Nghiên cứu các cải tiến của thuật toán, đặc biệt là Edmonds-Karp
- Phân tích độ phức tạp và tính đúng đắn của các thuật toán

**3. Cài đặt và thực nghiệm:**
- Xây dựng chương trình minh họa hoàn chỉnh bằng ngôn ngữ C++
- Thiết kế giao diện thân thiện cho phép nhập dữ liệu đồ thị
- Hiển thị kết quả tìm luồng cực đại một cách trực quan
- Kiểm thử chương trình với các bộ dữ liệu khác nhau

**4. Đánh giá và ứng dụng:**
- Đánh giá hiệu quả của thuật toán qua thực nghiệm
- Đề xuất các ứng dụng thực tế của bài toán
- Phân tích ưu nhược điểm và hướng phát triển

## Đối tượng và phạm vi nghiên cứu

**Đối tượng nghiên cứu:**
- Bài toán luồng cực đại trên đồ thị có hướng
- Đồ thị có trọng số (khả năng thông qua - capacity)
- Thuật toán Ford-Fulkerson và các biến thể

**Phạm vi nghiên cứu:**
- Tập trung chính vào phương pháp Ford-Fulkerson
- Cài đặt chi tiết thuật toán Edmonds-Karp sử dụng BFS
- Nghiên cứu định lý Max-Flow Min-Cut
- Ứng dụng trong các bài toán thực tế cơ bản

**Giới hạn của đề tài:**
- Không nghiên cứu các thuật toán phức tạp khác như Dinic, Push-Relabel
- Chưa xây dựng giao diện đồ họa trực quan
- Tập trung vào đồ thị có kích thước vừa phải (dưới 1000 đỉnh)

## Phương pháp nghiên cứu

**1. Nghiên cứu lý thuyết:**
- Tổng hợp và phân tích tài liệu từ sách giáo khoa chuyên ngành
- Nghiên cứu các bài báo khoa học về thuật toán đồ thị
- Tham khảo các tài liệu trực tuyến uy tín

**2. Nghiên cứu thực nghiệm:**
- Cài đặt thuật toán bằng ngôn ngữ lập trình C++
- Thiết kế và thực hiện các test case
- Kiểm thử và đánh giá hiệu năng thuật toán
- So sánh kết quả với tính toán lý thuyết

**3. Phương pháp phân tích:**
- Phân tích độ phức tạp thời gian và không gian
- Đánh giá tính đúng đắn thông qua chứng minh toán học
- Phân tích ưu nhược điểm của từng phương pháp

## Bố cục của đồ án

Đồ án được tổ chức thành 3 chương chính:

**Chương 1** trình bày cơ sở lý thuyết về đồ thị và bài toán luồng cực đại, bao gồm các khái niệm cơ bản, định nghĩa mạng luồng, và phát biểu bài toán.

**Chương 2** nghiên cứu chi tiết thuật toán Ford-Fulkerson và cải tiến Edmonds-Karp, bao gồm ý tưởng cốt lõi, định lý Max-Flow Min-Cut, và phân tích độ phức tạp.

**Chương 3** trình bày quá trình cài đặt chương trình minh họa, từ phân tích yêu cầu, thiết kế hệ thống, đến kết quả thực nghiệm và đánh giá.

Cuối cùng là phần kết luận tổng hợp kết quả đạt được, hạn chế và hướng phát triển trong tương lai.
