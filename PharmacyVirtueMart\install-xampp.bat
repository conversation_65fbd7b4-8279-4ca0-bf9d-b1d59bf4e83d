@echo off
echo ========================================
echo    XAMPP INSTALLATION FOR PHARMACY WEBSITE
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... OK
) else (
    echo ERROR: Please run this script as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Step 1: Checking system requirements...
echo - Windows 10/11: OK
echo - Available disk space: Checking...

REM Check available disk space (need at least 1GB)
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set freespace=%%a
echo - Free space on C: drive: %freespace% bytes

echo.
echo Step 2: Download XAMPP (if not already downloaded)
echo Please download XAMPP from: https://www.apachefriends.org/download.html
echo Choose: XAMPP for Windows (PHP 8.0.x or 8.1.x)
echo.

set /p downloaded="Have you downloaded XAMPP installer? (y/n): "
if /i "%downloaded%" neq "y" (
    echo Please download XAMPP first, then run this script again.
    pause
    exit /b 1
)

echo.
echo Step 3: Locate XAMPP installer
set /p installer_path="Enter full path to XAMPP installer (e.g., C:\Users\<USER>\Downloads\xampp-windows-x64-8.0.30-0-VC15-installer.exe): "

if not exist "%installer_path%" (
    echo ERROR: Installer file not found at: %installer_path%
    pause
    exit /b 1
)

echo.
echo Step 4: Installing XAMPP...
echo This may take 5-10 minutes...
echo.

REM Run XAMPP installer silently
"%installer_path%" --mode unattended --launchapps 0 --installdir "C:\xampp"

REM Wait for installation to complete
timeout /t 30 /nobreak

echo.
echo Step 5: Configuring XAMPP for Pharmacy Website...

REM Create pharmacy website directory
if not exist "C:\xampp\htdocs\pharmacy" (
    mkdir "C:\xampp\htdocs\pharmacy"
    echo Created pharmacy website directory
)

REM Configure PHP settings for Joomla
echo Configuring PHP settings...
echo max_execution_time = 300 >> "C:\xampp\php\php.ini"
echo memory_limit = 256M >> "C:\xampp\php\php.ini"
echo upload_max_filesize = 32M >> "C:\xampp\php\php.ini"
echo post_max_size = 32M >> "C:\xampp\php\php.ini"

echo.
echo Step 6: Starting XAMPP services...

REM Start Apache
"C:\xampp\apache\bin\httpd.exe" -k install
"C:\xampp\apache\bin\httpd.exe" -k start

REM Start MySQL
"C:\xampp\mysql\bin\mysqld.exe" --install
net start mysql

echo.
echo ========================================
echo    XAMPP INSTALLATION COMPLETED!
echo ========================================
echo.
echo Next steps:
echo 1. Open XAMPP Control Panel: C:\xampp\xampp-control.exe
echo 2. Start Apache and MySQL services
echo 3. Open browser and go to: http://localhost/
echo 4. Access phpMyAdmin: http://localhost/phpmyadmin
echo.
echo For Pharmacy Website setup:
echo 1. Download Joomla 4.x from: https://www.joomla.org/download.html
echo 2. Extract to: C:\xampp\htdocs\pharmacy\
echo 3. Create database in phpMyAdmin
echo 4. Run Joomla installation
echo 5. Install VirtueMart component
echo.

pause
