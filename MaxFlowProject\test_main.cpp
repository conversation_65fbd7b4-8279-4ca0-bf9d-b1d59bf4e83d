/*
 * CHƯƠNG TRÌNH TEST CASES CHO ĐỒ ÁN LUỒNG CỰC ĐẠI
 * 
 * File này chứa main function để chạy các test cases
 * độc lập với chương trình chính
 */

#include "Graph.h"
#include <iostream>
#include <clocale>

// Include TestCases namespace
#include "TestCases.cpp"

using namespace std;

int main() {
    // Thiết lập locale cho tiếng Việt
    setlocale(LC_ALL, "");
    
    cout << "\n";
    cout << "╔══════════════════════════════════════════════════════════════════════════════╗\n";
    cout << "║                    CHƯƠNG TRÌNH TEST THUẬT TOÁN                             ║\n";
    cout << "║                        FORD-FULKERSON                                       ║\n";
    cout << "║                                                                              ║\n";
    cout << "║              Kiểm tra tính đúng đắn của thuật toán                          ║\n";
    cout << "╚══════════════════════════════════════════════════════════════════════════════╝\n";
    
    // Hiển thị menu test
    TestCases::showTestMenu();
    
    return 0;
}
