html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom Pet Care Styles */
.hero-section {
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="paws" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23paws)"/></svg>');
  opacity: 0.3;
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

.service-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0,0,0,0.2);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s;
}

.service-card:hover::before {
  left: 100%;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: bold;
}

.card {
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.btn {
  border-radius: 30px;
  font-weight: 600;
  padding: 12px 30px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-lg {
  padding: 18px 40px;
  font-size: 1.1rem;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-cute {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.bg-gradient-pet {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.text-primary {
  color: #007bff !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

/* Animation for icons */
.fas {
  transition: all 0.3s ease;
}

.card:hover .fas {
  transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 0 !important;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .display-5 {
    font-size: 1.5rem !important;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #007bff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0056b3;
}

/* Floating Animation */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

.floating-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

/* Pulse Animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Pet Icons Background */
.pet-bg {
  position: relative;
  overflow: hidden;
}

.pet-bg::before {
  content: '🐕 🐱 🐰 🐦 🐹';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3rem;
  opacity: 0.1;
  white-space: nowrap;
  animation: float 8s ease-in-out infinite;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Navbar Enhancement */
.navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar-brand {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Container Enhancement */
.container, .container-fluid {
  position: relative;
  z-index: 1;
}

/* Page Background Patterns */
.page-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% {
    background-image:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  }
  50% {
    background-image:
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 20% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 60% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%);
  }
}

/* Hero Image Container */
.hero-image-container {
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sparkle Effect */
@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

.sparkle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: radial-gradient(circle, #fff 0%, transparent 70%);
  border-radius: 50%;
  animation: sparkle 2s ease-in-out infinite;
}

.sparkle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
.sparkle:nth-child(2) { top: 80%; left: 80%; animation-delay: 0.5s; }
.sparkle:nth-child(3) { top: 60%; left: 30%; animation-delay: 1s; }
.sparkle:nth-child(4) { top: 30%; left: 70%; animation-delay: 1.5s; }

/* Enhanced Button Hover Effects */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  border: none;
}

.btn-success:hover {
  background: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(17, 153, 142, 0.4);
}

/* Loading Animation */
@keyframes loading {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading {
  animation: loading 2s linear infinite;
}

/* Text Glow Effect */
.text-glow {
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Card Hover Glow */
.card:hover {
  box-shadow: 0 15px 35px rgba(0,0,0,0.15), 0 0 20px rgba(102, 126, 234, 0.2);
}

/* Form Enhancements */
.form-control {
  border-radius: 15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

.form-select {
  border-radius: 15px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: rgba(255, 255, 255, 1);
}

/* Navbar Link Hover */
.nav-link:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Enhanced Rounded Corners */
.rounded-4 {
  border-radius: 1.5rem !important;
}

/* Pet Card Special Effects */
.pet-card {
  position: relative;
  overflow: hidden;
}

.pet-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.5s;
  opacity: 0;
}

.pet-card:hover::after {
  animation: shine 0.5s ease-in-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

/* Bouncing Hearts */
@keyframes bounceHeart {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.bounce-heart {
  animation: bounceHeart 1.5s ease-in-out infinite;
}

/* Rainbow Text Effect */
@keyframes rainbow {
  0% { color: #ff0000; }
  16% { color: #ff8000; }
  33% { color: #ffff00; }
  50% { color: #00ff00; }
  66% { color: #0080ff; }
  83% { color: #8000ff; }
  100% { color: #ff0000; }
}

.rainbow-text {
  animation: rainbow 3s linear infinite;
}

/* Wiggle Animation */
@keyframes wiggle {
  0%, 7% { transform: rotateZ(0); }
  15% { transform: rotateZ(-15deg); }
  20% { transform: rotateZ(10deg); }
  25% { transform: rotateZ(-10deg); }
  30% { transform: rotateZ(6deg); }
  35% { transform: rotateZ(-4deg); }
  40%, 100% { transform: rotateZ(0); }
}

.wiggle {
  animation: wiggle 2s ease-in-out infinite;
}

/* Glow Effect */
@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
  50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8), 0 0 30px rgba(102, 126, 234, 0.6); }
}

.glow {
  animation: glow 2s ease-in-out infinite;
}

/* Typewriter Effect */
@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #667eea;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #667eea; }
}

/* Slide In Animations */
@keyframes slideInLeft {
  0% { transform: translateX(-100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  0% { transform: translateX(100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
  0% { transform: translateY(100%); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.8s ease-out;
}

/* Hover Effects for Cards */
.hover-lift:hover {
  transform: translateY(-10px) scale(1.02);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-glow:hover {
  box-shadow: 0 15px 35px rgba(0,0,0,0.1), 0 0 25px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

/* Pet Circles */
.pet-circle {
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.main-pet {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}

.orbit-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  right: 15%;
  animation: orbit 8s linear infinite;
}

.orbit-2 {
  width: 70px;
  height: 70px;
  bottom: 25%;
  left: 10%;
  animation: orbit 10s linear infinite reverse;
}

.orbit-3 {
  width: 60px;
  height: 60px;
  top: 15%;
  left: 20%;
  animation: orbit 12s linear infinite;
}

.orbit-4 {
  width: 60px;
  height: 60px;
  bottom: 15%;
  right: 25%;
  animation: orbit 9s linear infinite reverse;
}

.orbit-5 {
  width: 55px;
  height: 55px;
  top: 60%;
  right: 10%;
  animation: orbit 11s linear infinite;
}

@keyframes orbit {
  0% { transform: rotate(0deg) translateX(20px) rotate(0deg); }
  100% { transform: rotate(360deg) translateX(20px) rotate(-360deg); }
}

/* Floating Hearts */
.floating-heart {
  position: absolute;
  font-size: 1.5rem;
  animation: floatHeart 4s ease-in-out infinite;
  z-index: 3;
}

.heart-1 {
  top: 30%;
  left: 30%;
  animation-delay: 0s;
}

.heart-2 {
  top: 70%;
  right: 30%;
  animation-delay: 1.5s;
}

.heart-3 {
  bottom: 40%;
  left: 60%;
  animation-delay: 3s;
}

@keyframes floatHeart {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

/* Enhanced Hero Image Container */
.hero-image-container {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  z-index: 1;
}

/* Container Enhancements */
.container {
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  border-radius: 30px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.container:hover::before {
  opacity: 1;
}

/* Dropdown Menu Enhancement */
.dropdown-menu {
  border-radius: 15px;
  border: none;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.dropdown-item {
  border-radius: 10px;
  margin: 5px;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(5px);
}

/* Page Transition Effects */
.page-enter {
  animation: pageEnter 0.8s ease-out;
}

@keyframes pageEnter {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .hero-image-container {
    height: 250px;
  }

  .pet-circle.main-pet {
    width: 100px;
    height: 100px;
  }

  .orbit-1, .orbit-2, .orbit-3, .orbit-4, .orbit-5 {
    width: 50px;
    height: 50px;
  }

  .floating-heart {
    font-size: 1rem;
  }

  .display-4 {
    font-size: 2rem !important;
  }
}