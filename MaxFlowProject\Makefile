# Makefile cho Đồ án Nghiên cứu Bài toán Luồng Cực đại
# Tác giả: [Tên sinh viên]
# Ngày: [Ng<PERSON><PERSON> hoàn thành]

# Compiler và flags
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
DEBUG_FLAGS = -std=c++11 -Wall -Wextra -g -DDEBUG

# Tên file thực thi
TARGET = MaxFlowProgram
DEBUG_TARGET = MaxFlowProgram_debug
TEST_TARGET = TestProgram

# Thư mục
SRC_DIR = .
OBJ_DIR = obj
BIN_DIR = bin

# File nguồn
SOURCES = main.cpp Graph.cpp GraphUtils.cpp
TEST_SOURCES = test_main.cpp Graph.cpp GraphUtils.cpp

# File object
OBJECTS = $(SOURCES:%.cpp=$(OBJ_DIR)/%.o)
TEST_OBJECTS = $(TEST_SOURCES:%.cpp=$(OBJ_DIR)/%.o)

# Tạo thư mục nếu chưa tồn tại
$(shell mkdir -p $(OBJ_DIR) $(BIN_DIR))

# Rule mặc định
all: $(BIN_DIR)/$(TARGET)

# Biên dịch chương trình chính
$(BIN_DIR)/$(TARGET): $(OBJECTS)
	@echo "Linking $(TARGET)..."
	$(CXX) $(OBJECTS) -o $@
	@echo "Build completed successfully!"
	@echo "Run with: ./$(BIN_DIR)/$(TARGET)"

# Biên dịch chương trình debug
debug: CXXFLAGS = $(DEBUG_FLAGS)
debug: $(BIN_DIR)/$(DEBUG_TARGET)

$(BIN_DIR)/$(DEBUG_TARGET): $(OBJECTS)
	@echo "Linking $(DEBUG_TARGET) with debug info..."
	$(CXX) $(OBJECTS) -o $@
	@echo "Debug build completed!"
	@echo "Run with: ./$(BIN_DIR)/$(DEBUG_TARGET)"

# Biên dịch chương trình test
test: $(BIN_DIR)/$(TEST_TARGET)

$(BIN_DIR)/$(TEST_TARGET): $(TEST_OBJECTS)
	@echo "Linking $(TEST_TARGET)..."
	$(CXX) $(TEST_OBJECTS) -o $@
	@echo "Test build completed!"
	@echo "Run tests with: ./$(BIN_DIR)/$(TEST_TARGET)"

# Rule để biên dịch file .cpp thành .o
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp
	@echo "Compiling $<..."
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Chạy chương trình
run: $(BIN_DIR)/$(TARGET)
	@echo "Running $(TARGET)..."
	@echo "================================"
	./$(BIN_DIR)/$(TARGET)

# Chạy chương trình debug
run-debug: $(BIN_DIR)/$(DEBUG_TARGET)
	@echo "Running $(DEBUG_TARGET)..."
	@echo "================================"
	./$(BIN_DIR)/$(DEBUG_TARGET)

# Chạy test cases
run-test: $(BIN_DIR)/$(TEST_TARGET)
	@echo "Running test cases..."
	@echo "================================"
	./$(BIN_DIR)/$(TEST_TARGET)

# Dọn dẹp file object và executable
clean:
	@echo "Cleaning up..."
	rm -rf $(OBJ_DIR)/*.o
	rm -f $(BIN_DIR)/$(TARGET)
	rm -f $(BIN_DIR)/$(DEBUG_TARGET)
	rm -f $(BIN_DIR)/$(TEST_TARGET)
	@echo "Clean completed!"

# Dọn dẹp hoàn toàn
distclean: clean
	rm -rf $(OBJ_DIR)
	rm -rf $(BIN_DIR)
	@echo "Distribution clean completed!"

# Cài đặt (copy file thực thi vào thư mục hệ thống)
install: $(BIN_DIR)/$(TARGET)
	@echo "Installing $(TARGET)..."
	cp $(BIN_DIR)/$(TARGET) /usr/local/bin/
	@echo "Installation completed!"
	@echo "You can now run '$(TARGET)' from anywhere"

# Gỡ cài đặt
uninstall:
	@echo "Uninstalling $(TARGET)..."
	rm -f /usr/local/bin/$(TARGET)
	@echo "Uninstallation completed!"

# Tạo file zip để nộp bài
package:
	@echo "Creating package for submission..."
	zip -r MaxFlowProject_$(shell date +%Y%m%d).zip \
		*.cpp *.h Makefile README.md BaoCaoLyThuyet.md \
		--exclude="*.o" --exclude="$(TARGET)" --exclude="$(DEBUG_TARGET)" --exclude="$(TEST_TARGET)"
	@echo "Package created: MaxFlowProject_$(shell date +%Y%m%d).zip"

# Hiển thị thông tin về Makefile
help:
	@echo "Makefile cho Đồ án Nghiên cứu Bài toán Luồng Cực đại"
	@echo "=================================================="
	@echo "Các lệnh có sẵn:"
	@echo "  make          - Biên dịch chương trình chính"
	@echo "  make debug    - Biên dịch với thông tin debug"
	@echo "  make test     - Biên dịch chương trình test"
	@echo "  make run      - Chạy chương trình chính"
	@echo "  make run-debug- Chạy chương trình debug"
	@echo "  make run-test - Chạy test cases"
	@echo "  make clean    - Xóa file object và executable"
	@echo "  make distclean- Xóa hoàn toàn thư mục build"
	@echo "  make install  - Cài đặt vào hệ thống"
	@echo "  make uninstall- Gỡ cài đặt khỏi hệ thống"
	@echo "  make package  - Tạo file zip để nộp bài"
	@echo "  make help     - Hiển thị thông tin này"
	@echo ""
	@echo "Ví dụ sử dụng:"
	@echo "  make && make run    - Biên dịch và chạy"
	@echo "  make test && make run-test - Biên dịch và chạy test"

# Kiểm tra dependencies
check-deps:
	@echo "Checking dependencies..."
	@which $(CXX) > /dev/null || (echo "Error: $(CXX) not found!" && exit 1)
	@echo "$(CXX) found: $(shell $(CXX) --version | head -1)"
	@echo "All dependencies satisfied!"

# Hiển thị thông tin về dự án
info:
	@echo "Thông tin dự án:"
	@echo "================"
	@echo "Tên: Nghiên cứu bài toán luồng cực đại"
	@echo "Thuật toán: Ford-Fulkerson (Edmonds-Karp)"
	@echo "Ngôn ngữ: C++"
	@echo "Compiler: $(CXX)"
	@echo "Flags: $(CXXFLAGS)"
	@echo "Files: $(SOURCES)"
	@echo "Target: $(TARGET)"

# Phony targets (không phải file thực)
.PHONY: all debug test run run-debug run-test clean distclean install uninstall package help check-deps info

# Dependencies (tự động tạo)
-include $(OBJECTS:.o=.d)
