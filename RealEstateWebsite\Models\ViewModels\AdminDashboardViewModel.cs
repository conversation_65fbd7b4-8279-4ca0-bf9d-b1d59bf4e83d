using RealEstateWebsite.Models;

namespace RealEstateWebsite.Models.ViewModels
{
    public class AdminDashboardViewModel
    {
        public DashboardStatistics Statistics { get; set; } = new DashboardStatistics();
        public List<Property> RecentProperties { get; set; } = new List<Property>();
        public List<Contact> RecentContacts { get; set; } = new List<Contact>();
        public List<User> RecentUsers { get; set; } = new List<User>();
    }

    public class DashboardStatistics
    {
        public int TotalProperties { get; set; }
        public int SoldProperties { get; set; }
        public int TotalUsers { get; set; }
        public int NewContacts { get; set; }
        public int ActiveListings { get; set; }
        public int PendingApprovals { get; set; }
        public decimal TotalRevenue { get; set; }
        public int MonthlyViews { get; set; }
    }

    public class PropertiesListViewModel
    {
        public List<Property> Properties { get; set; } = new List<Property>();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public string SearchTerm { get; set; } = "";
        public string SelectedCategory { get; set; } = "";
        public string SelectedStatus { get; set; } = "";
    }

    public class UsersListViewModel
    {
        public List<User> Users { get; set; } = new List<User>();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public string SearchTerm { get; set; } = "";
    }

    public class ContactsListViewModel
    {
        public List<Contact> Contacts { get; set; } = new List<Contact>();
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public string SearchTerm { get; set; } = "";
    }

    public class ReportsViewModel
    {
        public int TotalProperties { get; set; }
        public int TotalUsers { get; set; }
        public int TotalContacts { get; set; }
        public List<CategoryReport> PropertiesByCategory { get; set; } = new List<CategoryReport>();
        public List<MonthlyReport> MonthlyData { get; set; } = new List<MonthlyReport>();
    }

    public class CategoryReport
    {
        public string CategoryName { get; set; } = "";
        public int PropertyCount { get; set; }
        public decimal TotalValue { get; set; }
    }

    public class MonthlyReport
    {
        public string Month { get; set; } = "";
        public int PropertiesAdded { get; set; }
        public int ContactsReceived { get; set; }
        public decimal Revenue { get; set; }
    }
}
