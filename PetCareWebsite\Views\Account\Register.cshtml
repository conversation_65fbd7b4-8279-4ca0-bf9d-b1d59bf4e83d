@model PetCareWebsite.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng Ký";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0 glass-effect">
                <div class="card-header bg-gradient-success text-white text-center py-4">
                    <h3 class="mb-0 floating">
                        🎉 Đăng Ký Tài Khoản
                    </h3>
                </div>
                <div class="card-body p-5">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="FullName" class="form-label fw-bold">
                                <i class="fas fa-user me-2"></i>@Html.DisplayNameFor(m => m.FullName)
                            </label>
                            <input asp-for="FullName" class="form-control form-control-lg" placeholder="Nhập họ tên đầy đủ" />
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label fw-bold">
                                <i class="fas fa-envelope me-2"></i>@Html.DisplayNameFor(m => m.Email)
                            </label>
                            <input asp-for="Email" class="form-control form-control-lg" placeholder="Nhập địa chỉ email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="PhoneNumber" class="form-label fw-bold">
                                <i class="fas fa-phone me-2"></i>@Html.DisplayNameFor(m => m.PhoneNumber)
                            </label>
                            <input asp-for="PhoneNumber" class="form-control form-control-lg" placeholder="Nhập số điện thoại" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label fw-bold">
                                <i class="fas fa-map-marker-alt me-2"></i>@Html.DisplayNameFor(m => m.Address)
                            </label>
                            <textarea asp-for="Address" class="form-control" rows="3" placeholder="Nhập địa chỉ của bạn"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label fw-bold">
                                    <i class="fas fa-lock me-2"></i>@Html.DisplayNameFor(m => m.Password)
                                </label>
                                <input asp-for="Password" class="form-control form-control-lg" placeholder="Nhập mật khẩu" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label fw-bold">
                                    <i class="fas fa-lock me-2"></i>@Html.DisplayNameFor(m => m.ConfirmPassword)
                                </label>
                                <input asp-for="ConfirmPassword" class="form-control form-control-lg" placeholder="Xác nhận mật khẩu" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Đăng Ký
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" class="text-success fw-bold">Đăng nhập ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
