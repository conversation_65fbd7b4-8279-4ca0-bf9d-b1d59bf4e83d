# PowerShell Script to Download and Install XAMPP for Pharmacy Website
# Run as Administrator

Write-Host "========================================" -ForegroundColor Green
Write-Host "   XAMPP DOWNLOADER FOR PHARMACY WEBSITE" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: Please run PowerShell as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Running as Administrator... OK" -ForegroundColor Green
Write-Host ""

# Set download parameters
$xamppVersion = "8.0.30"
$xamppUrl = "https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/$xamppVersion/xampp-windows-x64-$xamppVersion-0-VC15-installer.exe/download"
$downloadPath = "$env:USERPROFILE\Downloads\xampp-installer.exe"
$installPath = "C:\xampp"

Write-Host "Step 1: Checking system requirements..." -ForegroundColor Cyan
Write-Host "- Windows Version: $((Get-WmiObject Win32_OperatingSystem).Caption)"
Write-Host "- Architecture: $env:PROCESSOR_ARCHITECTURE"

# Check available disk space
$freeSpace = (Get-WmiObject Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB
Write-Host "- Free space on C: drive: $([math]::Round($freeSpace, 2)) GB"

if ($freeSpace -lt 2) {
    Write-Host "ERROR: Not enough disk space. Need at least 2GB free." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 2: Downloading XAMPP..." -ForegroundColor Cyan
Write-Host "- Version: XAMPP $xamppVersion"
Write-Host "- Download URL: $xamppUrl"
Write-Host "- Saving to: $downloadPath"
Write-Host ""

try {
    # Download XAMPP with progress bar
    $webClient = New-Object System.Net.WebClient
    $webClient.DownloadFile($xamppUrl, $downloadPath)
    Write-Host "Download completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to download XAMPP" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please download manually from: https://www.apachefriends.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 3: Installing XAMPP..." -ForegroundColor Cyan
Write-Host "This may take 5-10 minutes. Please wait..." -ForegroundColor Yellow

try {
    # Install XAMPP silently
    $installArgs = "--mode unattended --launchapps 0 --installdir `"$installPath`""
    Start-Process -FilePath $downloadPath -ArgumentList $installArgs -Wait
    Write-Host "XAMPP installation completed!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install XAMPP" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run the installer manually: $downloadPath" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 4: Configuring XAMPP for Pharmacy Website..." -ForegroundColor Cyan

# Create pharmacy website directory
$pharmacyDir = "$installPath\htdocs\pharmacy"
if (!(Test-Path $pharmacyDir)) {
    New-Item -ItemType Directory -Path $pharmacyDir -Force
    Write-Host "- Created pharmacy website directory: $pharmacyDir" -ForegroundColor Green
}

# Configure PHP settings for Joomla
$phpIniPath = "$installPath\php\php.ini"
if (Test-Path $phpIniPath) {
    Write-Host "- Configuring PHP settings for Joomla..." -ForegroundColor Yellow
    
    # Backup original php.ini
    Copy-Item $phpIniPath "$phpIniPath.backup"
    
    # Add Joomla-specific settings
    $phpSettings = @"

; Joomla/VirtueMart Configuration
max_execution_time = 300
memory_limit = 256M
upload_max_filesize = 32M
post_max_size = 32M
max_input_vars = 3000
session.gc_maxlifetime = 1440
"@
    
    Add-Content -Path $phpIniPath -Value $phpSettings
    Write-Host "- PHP configuration updated" -ForegroundColor Green
}

Write-Host ""
Write-Host "Step 5: Starting XAMPP Control Panel..." -ForegroundColor Cyan

# Start XAMPP Control Panel
$xamppControl = "$installPath\xampp-control.exe"
if (Test-Path $xamppControl) {
    Start-Process $xamppControl
    Write-Host "- XAMPP Control Panel started" -ForegroundColor Green
} else {
    Write-Host "- WARNING: XAMPP Control Panel not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   XAMPP INSTALLATION COMPLETED!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. In XAMPP Control Panel, start Apache and MySQL" -ForegroundColor White
Write-Host "2. Open browser and go to: http://localhost/" -ForegroundColor White
Write-Host "3. Access phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
Write-Host ""

Write-Host "FOR PHARMACY WEBSITE SETUP:" -ForegroundColor Cyan
Write-Host "1. Download Joomla 4.x from: https://www.joomla.org/download.html" -ForegroundColor White
Write-Host "2. Extract Joomla to: $pharmacyDir" -ForegroundColor White
Write-Host "3. Create database 'pharmacy_virtuemart' in phpMyAdmin" -ForegroundColor White
Write-Host "4. Run Joomla installation at: http://localhost/pharmacy/" -ForegroundColor White
Write-Host "5. Download and install VirtueMart component" -ForegroundColor White
Write-Host ""

Write-Host "Installation files:" -ForegroundColor Yellow
Write-Host "- XAMPP installed at: $installPath" -ForegroundColor White
Write-Host "- Installer downloaded: $downloadPath" -ForegroundColor White
Write-Host "- Website directory: $pharmacyDir" -ForegroundColor White
Write-Host ""

# Open browser to localhost
Write-Host "Opening browser to test installation..." -ForegroundColor Cyan
Start-Sleep 3
Start-Process "http://localhost/"

Read-Host "Press Enter to exit"
