-- =====================================================
-- Script tạo cơ sở dữ liệu cho Website Dược Phẩm VirtueMart
-- =====================================================

-- Tạo database
CREATE DATABASE IF NOT EXISTS pharmacy_virtuemart 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Sử dụng database
USE pharmacy_virtuemart;

-- Tạo user cho database
CREATE USER IF NOT EXISTS 'pharmacy_user'@'localhost' 
IDENTIFIED BY 'pharmacy_password_2024!';

-- Cấ<PERSON> quyền cho user
GRANT ALL PRIVILEGES ON pharmacy_virtuemart.* 
TO 'pharmacy_user'@'localhost';

FLUSH PRIVILEGES;

-- =====================================================
-- <PERSON><PERSON><PERSON> bổ sung cho quản lý dược phẩm
-- =====================================================

-- Bảng nhà sản xuất dược phẩm
CREATE TABLE IF NOT EXISTS `jos_vm_manufacturers_pharma` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `manufacturer_name` varchar(255) NOT NULL,
    `license_number` varchar(100) UNIQUE,
    `country` varchar(100),
    `contact_info` text,
    `certification` varchar(255),
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `published` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng thông tin dược phẩm bổ sung
CREATE TABLE IF NOT EXISTS `jos_vm_product_pharma_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_id` int(11) NOT NULL,
    `drug_registration_number` varchar(100),
    `active_ingredient` text,
    `dosage_form` varchar(100),
    `strength` varchar(100),
    `indication` text,
    `contraindication` text,
    `side_effects` text,
    `dosage_instruction` text,
    `storage_condition` text,
    `expiry_date` date,
    `prescription_required` tinyint(1) DEFAULT 0,
    `age_restriction` varchar(50),
    `pregnancy_category` varchar(10),
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng danh mục dược phẩm chuyên biệt
CREATE TABLE IF NOT EXISTS `jos_vm_pharma_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `category_name` varchar(255) NOT NULL,
    `category_code` varchar(50) UNIQUE,
    `description` text,
    `requires_prescription` tinyint(1) DEFAULT 0,
    `age_restricted` tinyint(1) DEFAULT 0,
    `special_storage` tinyint(1) DEFAULT 0,
    `parent_id` int(11) DEFAULT 0,
    `ordering` int(11) DEFAULT 0,
    `published` tinyint(1) DEFAULT 1,
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng theo dõi tồn kho dược phẩm
CREATE TABLE IF NOT EXISTS `jos_vm_pharma_inventory` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_id` int(11) NOT NULL,
    `batch_number` varchar(100),
    `manufacturing_date` date,
    `expiry_date` date,
    `quantity_in_stock` int(11) DEFAULT 0,
    `minimum_stock_level` int(11) DEFAULT 0,
    `cost_price` decimal(10,2),
    `selling_price` decimal(10,2),
    `supplier_id` int(11),
    `location` varchar(100),
    `status` enum('active','expired','recalled','damaged') DEFAULT 'active',
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `product_id` (`product_id`),
    KEY `expiry_date` (`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng nhà cung cấp
CREATE TABLE IF NOT EXISTS `jos_vm_suppliers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `supplier_name` varchar(255) NOT NULL,
    `contact_person` varchar(255),
    `phone` varchar(50),
    `email` varchar(255),
    `address` text,
    `tax_code` varchar(50),
    `license_number` varchar(100),
    `payment_terms` text,
    `delivery_terms` text,
    `rating` decimal(3,2) DEFAULT 0.00,
    `status` enum('active','inactive','suspended') DEFAULT 'active',
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng đơn thuốc (prescription)
CREATE TABLE IF NOT EXISTS `jos_vm_prescriptions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `prescription_number` varchar(100) UNIQUE NOT NULL,
    `patient_name` varchar(255) NOT NULL,
    `patient_phone` varchar(50),
    `patient_address` text,
    `doctor_name` varchar(255),
    `doctor_license` varchar(100),
    `clinic_hospital` varchar(255),
    `prescription_date` date,
    `total_amount` decimal(10,2) DEFAULT 0.00,
    `status` enum('pending','processing','completed','cancelled') DEFAULT 'pending',
    `notes` text,
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `modified_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng chi tiết đơn thuốc
CREATE TABLE IF NOT EXISTS `jos_vm_prescription_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `prescription_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `quantity` int(11) NOT NULL,
    `dosage_instruction` text,
    `duration` varchar(100),
    `price` decimal(10,2),
    `total_price` decimal(10,2),
    PRIMARY KEY (`id`),
    KEY `prescription_id` (`prescription_id`),
    KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bảng cảnh báo tương tác thuốc
CREATE TABLE IF NOT EXISTS `jos_vm_drug_interactions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `drug1_id` int(11) NOT NULL,
    `drug2_id` int(11) NOT NULL,
    `interaction_type` enum('major','moderate','minor') DEFAULT 'moderate',
    `description` text,
    `recommendation` text,
    `severity_level` int(1) DEFAULT 1,
    `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `drug1_id` (`drug1_id`),
    KEY `drug2_id` (`drug2_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Indexes để tối ưu hiệu suất
-- =====================================================

-- Index cho tìm kiếm sản phẩm theo thành phần hoạt chất
CREATE INDEX idx_active_ingredient ON jos_vm_product_pharma_info(active_ingredient(100));

-- Index cho tìm kiếm theo số đăng ký thuốc
CREATE INDEX idx_registration_number ON jos_vm_product_pharma_info(drug_registration_number);

-- Index cho theo dõi hạn sử dụng
CREATE INDEX idx_expiry_warning ON jos_vm_pharma_inventory(expiry_date, status);

-- Index cho tìm kiếm đơn thuốc
CREATE INDEX idx_prescription_search ON jos_vm_prescriptions(prescription_number, patient_name(50));

-- =====================================================
-- Views để báo cáo
-- =====================================================

-- View sản phẩm sắp hết hạn
CREATE OR REPLACE VIEW v_expiring_products AS
SELECT 
    p.product_name,
    pi.batch_number,
    pi.expiry_date,
    pi.quantity_in_stock,
    DATEDIFF(pi.expiry_date, CURDATE()) as days_to_expiry
FROM jos_vm_pharma_inventory pi
JOIN jos_vm_products p ON pi.product_id = p.virtuemart_product_id
WHERE pi.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 90 DAY)
AND pi.status = 'active'
ORDER BY pi.expiry_date ASC;

-- View sản phẩm tồn kho thấp
CREATE OR REPLACE VIEW v_low_stock_products AS
SELECT 
    p.product_name,
    pi.quantity_in_stock,
    pi.minimum_stock_level,
    (pi.minimum_stock_level - pi.quantity_in_stock) as shortage
FROM jos_vm_pharma_inventory pi
JOIN jos_vm_products p ON pi.product_id = p.virtuemart_product_id
WHERE pi.quantity_in_stock <= pi.minimum_stock_level
AND pi.status = 'active'
ORDER BY shortage DESC;

-- =====================================================
-- Dữ liệu mẫu cơ bản
-- =====================================================

-- Thêm nhà sản xuất mẫu
INSERT INTO jos_vm_manufacturers_pharma (manufacturer_name, license_number, country, certification) VALUES
('Công ty Dược phẩm Traphaco', 'VN-001-2024', 'Vietnam', 'GMP, WHO-GMP'),
('Công ty Dược Hậu Giang', 'VN-002-2024', 'Vietnam', 'GMP, EU-GMP'),
('Pfizer Inc.', 'US-001-2024', 'United States', 'FDA, EMA'),
('Novartis AG', 'CH-001-2024', 'Switzerland', 'EMA, FDA');

-- Thêm danh mục dược phẩm mẫu
INSERT INTO jos_vm_pharma_categories (category_name, category_code, description, requires_prescription) VALUES
('Thuốc kê đơn', 'PRESCRIPTION', 'Thuốc chỉ bán theo đơn của bác sĩ', 1),
('Thuốc không kê đơn', 'OTC', 'Thuốc bán tự do không cần đơn', 0),
('Thực phẩm chức năng', 'SUPPLEMENT', 'Các sản phẩm bổ sung dinh dưỡng', 0),
('Dụng cụ y tế', 'MEDICAL_DEVICE', 'Thiết bị và dụng cụ y tế', 0);

COMMIT;
