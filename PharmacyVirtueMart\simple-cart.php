<?php
session_start();

$products = [
    1 => ['name' => 'Paracetamol 500mg', 'price' => 3000],
    2 => ['name' => 'Amoxicillin 250mg', 'price' => 12000],
    3 => ['name' => 'Vitamin C 1000mg', 'price' => 25000],
    4 => ['name' => 'Nhiệt kế điện tử', 'price' => 150000]
];

if (!isset($_SESSION['cart'])) $_SESSION['cart'] = [];

// Xử lý cập nhật giỏ hàng
if (isset($_POST['update_cart'])) {
    foreach ($_POST['quantities'] as $product_id => $quantity) {
        if ($quantity <= 0) {
            unset($_SESSION['cart'][$product_id]);
        } else {
            $_SESSION['cart'][$product_id] = (int)$quantity;
        }
    }
    $message = "Giỏ hàng đã được cập nhật!";
}

// Xử lý xóa sản phẩm
if (isset($_GET['remove'])) {
    unset($_SESSION['cart'][(int)$_GET['remove']]);
    $message = "Đã xóa sản phẩm khỏi giỏ hàng!";
}

$total = 0;
foreach ($_SESSION['cart'] as $product_id => $quantity) {
    if (isset($products[$product_id])) {
        $total += $products[$product_id]['price'] * $quantity;
    }
}

$shipping_fee = $total >= 500000 ? 0 : 25000;
$grand_total = $total + $shipping_fee;
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giỏ hàng - Nhà Thuốc An Khang</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root { --primary-color: #2E8B57; --secondary-color: #F0F8FF; }
        .navbar-brand { font-weight: bold; color: var(--primary-color) !important; }
        .btn-primary { background: var(--primary-color); border-color: var(--primary-color); }
        .cart-summary { background: var(--secondary-color); border-radius: 8px; padding: 1.5rem; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-plus-circle me-2"></i>Nhà Thuốc An Khang
            </a>
            <div class="d-flex">
                <a href="full-website.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua hàng
                </a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <h2 class="mb-4"><i class="fas fa-shopping-cart me-2"></i>Giỏ hàng của bạn</h2>
        
        <?php if (isset($message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (empty($_SESSION['cart'])): ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
                <h4>Giỏ hàng trống</h4>
                <p class="text-muted">Bạn chưa có sản phẩm nào trong giỏ hàng.</p>
                <a href="full-website.php" class="btn btn-primary">
                    <i class="fas fa-pills me-2"></i>Mua thuốc ngay
                </a>
            </div>
        <?php else: ?>
            <form method="POST">
                <div class="table-responsive">
                    <table class="table">
                        <thead class="table-light">
                            <tr>
                                <th>Sản phẩm</th>
                                <th>Đơn giá</th>
                                <th>Số lượng</th>
                                <th>Thành tiền</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($_SESSION['cart'] as $product_id => $quantity): ?>
                                <?php if (isset($products[$product_id])): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3" style="width: 60px; height: 60px; background: #28A745; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                                    <i class="fas fa-pills text-white"></i>
                                                </div>
                                                <h6 class="mb-0"><?php echo $products[$product_id]['name']; ?></h6>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-primary fw-bold">
                                                <?php echo number_format($products[$product_id]['price']); ?>₫
                                            </span>
                                        </td>
                                        <td>
                                            <input type="number" name="quantities[<?php echo $product_id; ?>]" 
                                                   value="<?php echo $quantity; ?>" min="0" max="999" 
                                                   class="form-control" style="width: 80px;">
                                        </td>
                                        <td>
                                            <span class="fw-bold">
                                                <?php echo number_format($products[$product_id]['price'] * $quantity); ?>₫
                                            </span>
                                        </td>
                                        <td>
                                            <a href="?remove=<?php echo $product_id; ?>" 
                                               class="btn btn-outline-danger btn-sm"
                                               onclick="return confirm('Bạn có chắc muốn xóa sản phẩm này?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit" name="update_cart" class="btn btn-outline-primary">
                            <i class="fas fa-sync-alt me-2"></i>Cập nhật giỏ hàng
                        </button>
                    </div>
                    <div class="col-md-6">
                        <div class="cart-summary">
                            <h5 class="mb-3">Tóm tắt đơn hàng</h5>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tạm tính:</span>
                                <span><?php echo number_format($total); ?>₫</span>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phí vận chuyển:</span>
                                <span>
                                    <?php if ($shipping_fee == 0): ?>
                                        <span class="text-success">Miễn phí</span>
                                    <?php else: ?>
                                        <?php echo number_format($shipping_fee); ?>₫
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <?php if ($total < 500000 && $total > 0): ?>
                                <div class="alert alert-info py-2 px-3 mb-3">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        Mua thêm <?php echo number_format(500000 - $total); ?>₫ để được miễn phí ship
                                    </small>
                                </div>
                            <?php endif; ?>
                            
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Tổng cộng:</strong>
                                <strong class="text-primary h5"><?php echo number_format($grand_total); ?>₫</strong>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary btn-lg" onclick="alert('Chức năng thanh toán sẽ được phát triển trong phiên bản đầy đủ!')">
                                    <i class="fas fa-credit-card me-2"></i>Thanh toán
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        <?php endif; ?>
    </div>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <h5>Nhà Thuốc An Khang</h5>
            <p class="mb-0">Sức khỏe là vàng - Chúng tôi luôn đồng hành cùng bạn</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
