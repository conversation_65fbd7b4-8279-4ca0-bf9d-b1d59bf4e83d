using Microsoft.EntityFrameworkCore;
using RealEstateWebsite.Models;

namespace RealEstateWebsite.Data
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var context = new RealEstateContext(
                serviceProvider.GetRequiredService<DbContextOptions<RealEstateContext>>());

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Check if we already have data
            if (context.Users.Any())
            {
                return; // DB has been seeded
            }

            // Create Admin User
            var adminUser = new User
            {
                Username = "admin",
                Email = "<EMAIL>",
                FullName = "Quản Trị Viên",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!"),
                Role = UserRole.Admin,
                IsActive = true,
                CreatedDate = DateTime.Now,
                Phone = "**********"
            };

            context.Users.Add(adminUser);

            // Create Categories
            var categories = new List<Category>
            {
                new Category
                {
                    Name = "Nhà Phố",
                    Description = "Nhà phố, nhà riêng",
                    Icon = "fas fa-home",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Category
                {
                    Name = "Chung Cư",
                    Description = "Căn hộ chung cư",
                    Icon = "fas fa-building",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Category
                {
                    Name = "Biệt Thự",
                    Description = "Biệt thự, villa",
                    Icon = "fas fa-crown",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Category
                {
                    Name = "Đất Nền",
                    Description = "Đất nền, đất thổ cư",
                    Icon = "fas fa-map",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Category
                {
                    Name = "Văn Phòng",
                    Description = "Văn phòng, mặt bằng kinh doanh",
                    Icon = "fas fa-briefcase",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Category
                {
                    Name = "Kho Xưởng",
                    Description = "Kho bãi, nhà xưởng",
                    Icon = "fas fa-warehouse",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            };

            context.Categories.AddRange(categories);
            await context.SaveChangesAsync();

            // Create Sample Properties
            var properties = new List<Property>
            {
                new Property
                {
                    Title = "Nhà Phố Hiện Đại Quận 7",
                    Description = "Nhà phố 1 trệt 2 lầu, thiết kế hiện đại, nội thất cao cấp. Vị trí đắc địa gần trường học, bệnh viện.",
                    Price = 10500000000,
                    Area = 120,
                    Bedrooms = 4,
                    Bathrooms = 3,
                    Address = "Đường Nguyễn Thị Thập",
                    City = "TP. Hồ Chí Minh",
                    District = "Quận 7",
                    Type = PropertyType.Sale,
                    Status = PropertyStatus.Available,
                    CategoryId = categories.First(c => c.Name == "Nhà Phố").CategoryId,
                    MainImage = "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                    IsAvailable = true,
                    CreatedDate = DateTime.Now.AddDays(-10)
                },
                new Property
                {
                    Title = "Chung Cư Vinhomes Central Park",
                    Description = "Căn hộ 2 phòng ngủ view sông Sài Gòn, đầy đủ nội thất, tiện ích đẳng cấp 5 sao.",
                    Price = 25000000,
                    Area = 85,
                    Bedrooms = 2,
                    Bathrooms = 2,
                    Address = "Đường Nguyễn Hữu Cảnh",
                    City = "TP. Hồ Chí Minh",
                    District = "Bình Thạnh",
                    Type = PropertyType.Rent,
                    Status = PropertyStatus.Available,
                    CategoryId = categories.First(c => c.Name == "Chung Cư").CategoryId,
                    MainImage = "https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                    IsAvailable = true,
                    CreatedDate = DateTime.Now.AddDays(-8)
                },
                new Property
                {
                    Title = "Biệt Thự Phú Mỹ Hưng",
                    Description = "Biệt thự đơn lập 300m², sân vườn rộng rãi, hồ bơi riêng, an ninh 24/7.",
                    Price = 45000000000,
                    Area = 300,
                    Bedrooms = 5,
                    Bathrooms = 4,
                    Address = "Khu đô thị Phú Mỹ Hưng",
                    City = "TP. Hồ Chí Minh",
                    District = "Quận 7",
                    Type = PropertyType.Sale,
                    Status = PropertyStatus.Available,
                    CategoryId = categories.First(c => c.Name == "Biệt Thự").CategoryId,
                    MainImage = "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                    IsAvailable = true,
                    CreatedDate = DateTime.Now.AddDays(-5)
                },
                new Property
                {
                    Title = "Đất Nền Dự Án Eco City",
                    Description = "Đất nền thổ cư 100%, hạ tầng hoàn thiện, pháp lý rõ ràng, sổ hồng riêng.",
                    Price = 2500000000,
                    Area = 80,
                    Bedrooms = 0,
                    Bathrooms = 0,
                    Address = "Đường Võ Văn Kiệt",
                    City = "TP. Hồ Chí Minh",
                    District = "Bình Chánh",
                    Type = PropertyType.Sale,
                    Status = PropertyStatus.Available,
                    CategoryId = categories.First(c => c.Name == "Đất Nền").CategoryId,
                    MainImage = "https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                    IsAvailable = true,
                    CreatedDate = DateTime.Now.AddDays(-3)
                },
                new Property
                {
                    Title = "Văn Phòng Quận 1",
                    Description = "Văn phòng hạng A, tầng cao view đẹp, đầy đủ tiện ích, bãi đậu xe rộng rãi.",
                    Price = 50000000,
                    Area = 150,
                    Bedrooms = 0,
                    Bathrooms = 2,
                    Address = "Đường Nguyễn Huệ",
                    City = "TP. Hồ Chí Minh",
                    District = "Quận 1",
                    Type = PropertyType.Rent,
                    Status = PropertyStatus.Available,
                    CategoryId = categories.First(c => c.Name == "Văn Phòng").CategoryId,
                    MainImage = "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                    IsAvailable = true,
                    CreatedDate = DateTime.Now.AddDays(-1)
                }
            };

            context.Properties.AddRange(properties);
            await context.SaveChangesAsync();

            // Create Sample Contacts
            var contacts = new List<Contact>
            {
                new Contact
                {
                    FullName = "Nguyễn Văn A",
                    Email = "<EMAIL>",
                    Phone = "0987654321",
                    Message = "Tôi quan tâm đến căn nhà phố ở Quận 7. Xin vui lòng liên hệ tôi.",
                    PropertyId = properties.First().PropertyId,
                    Status = ContactStatus.New,
                    CreatedDate = DateTime.Now.AddDays(-2)
                },
                new Contact
                {
                    FullName = "Trần Thị B",
                    Email = "<EMAIL>",
                    Phone = "**********",
                    Message = "Cho tôi xem thêm thông tin về chung cư Vinhomes Central Park.",
                    PropertyId = properties.Skip(1).First().PropertyId,
                    Status = ContactStatus.New,
                    CreatedDate = DateTime.Now.AddDays(-1)
                }
            };

            context.Contacts.AddRange(contacts);
            await context.SaveChangesAsync();
        }
    }
}
