#include <iostream>
#include <vector>
#include <queue>
#include <climits>
#include <iomanip>

using namespace std;

class SimpleGraph {
private:
    int vertices;
    vector<vector<int>> capacity;
    vector<vector<int>> original;
    
public:
    SimpleGraph(int V) : vertices(V) {
        capacity.resize(V, vector<int>(V, 0));
        original.resize(V, vector<int>(V, 0));
    }
    
    void addEdge(int u, int v, int cap) {
        capacity[u][v] = cap;
        original[u][v] = cap;
    }
    
    void displayMatrix() {
        cout << "\nMa tran ke:\n";
        cout << "    ";
        for (int i = 0; i < vertices; i++) {
            cout << setw(4) << i;
        }
        cout << "\n";
        
        for (int i = 0; i < vertices; i++) {
            cout << setw(2) << i << ": ";
            for (int j = 0; j < vertices; j++) {
                cout << setw(4) << original[i][j];
            }
            cout << "\n";
        }
        cout << "\n";
    }
    
    bool bfs(int source, int sink, vector<int>& parent) {
        vector<bool> visited(vertices, false);
        queue<int> q;
        
        q.push(source);
        visited[source] = true;
        parent[source] = -1;
        
        while (!q.empty()) {
            int u = q.front();
            q.pop();
            
            for (int v = 0; v < vertices; v++) {
                if (!visited[v] && capacity[u][v] > 0) {
                    if (v == sink) {
                        parent[v] = u;
                        return true;
                    }
                    q.push(v);
                    parent[v] = u;
                    visited[v] = true;
                }
            }
        }
        
        return false;
    }
    
    void displayPath(const vector<int>& parent, int source, int sink) {
        vector<int> path;
        int current = sink;
        
        while (current != -1) {
            path.push_back(current);
            current = parent[current];
        }
        
        for (int i = path.size() - 1; i >= 0; i--) {
            cout << path[i];
            if (i > 0) cout << " -> ";
        }
        cout << "\n";
    }
    
    int fordFulkerson(int source, int sink) {
        cout << "\n=== BAT DAU THUAT TOAN FORD-FULKERSON ===\n";
        cout << "Nguon: " << source << ", Dich: " << sink << "\n\n";
        
        // Tao ban sao ma tran
        vector<vector<int>> rGraph = original;
        capacity = rGraph;
        
        vector<int> parent(vertices);
        int maxFlowValue = 0;
        int iteration = 0;
        
        while (bfs(source, sink, parent)) {
            iteration++;
            cout << "--- Lan lap " << iteration << " ---\n";
            
            // Tim luong toi thieu tren duong tang luong
            int pathFlow = INT_MAX;
            for (int v = sink; v != source; v = parent[v]) {
                int u = parent[v];
                pathFlow = min(pathFlow, capacity[u][v]);
            }
            
            cout << "Duong tang luong tim duoc: ";
            displayPath(parent, source, sink);
            cout << "Luong tang them: " << pathFlow << "\n";
            
            // Cap nhat kha nang du
            for (int v = sink; v != source; v = parent[v]) {
                int u = parent[v];
                capacity[u][v] -= pathFlow;
                capacity[v][u] += pathFlow;
            }
            
            maxFlowValue += pathFlow;
            cout << "Tong luong hien tai: " << maxFlowValue << "\n\n";
        }
        
        cout << "=== KET THUC THUAT TOAN ===\n";
        cout << "Luong cuc dai tu " << source << " den " << sink << ": " << maxFlowValue << "\n\n";
        
        return maxFlowValue;
    }
};

int main() {
    cout << "=== DEMO THUAT TOAN FORD-FULKERSON ===\n";
    cout << "Nghien cuu bai toan luong cuc dai\n\n";
    
    // Tao do thi mau don gian
    cout << "Tao do thi mau voi 4 dinh:\n";
    SimpleGraph g(4);
    
    // Them cac canh
    g.addEdge(0, 1, 16);
    g.addEdge(0, 2, 13);
    g.addEdge(1, 2, 10);
    g.addEdge(1, 3, 12);
    g.addEdge(2, 1, 4);
    g.addEdge(2, 3, 14);
    
    cout << "Do thi da tao:\n";
    cout << "0 -> 1: 16\n";
    cout << "0 -> 2: 13\n";
    cout << "1 -> 2: 10\n";
    cout << "1 -> 3: 12\n";
    cout << "2 -> 1: 4\n";
    cout << "2 -> 3: 14\n";
    
    g.displayMatrix();
    
    cout << "Tim luong cuc dai tu dinh 0 den dinh 3:\n";
    int maxFlow = g.fordFulkerson(0, 3);
    
    cout << "KET QUA CUOI CUNG:\n";
    cout << "Luong cuc dai = " << maxFlow << "\n";
    cout << "Ket qua mong doi = 23\n";
    
    if (maxFlow == 23) {
        cout << "✓ THUAT TOAN HOAT DONG DUNG!\n";
    } else {
        cout << "✗ CO LOI TRONG THUAT TOAN!\n";
    }
    
    cout << "\n=== DEMO HOAN THANH ===\n";
    
    return 0;
}
