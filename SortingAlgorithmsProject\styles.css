/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 700;
}

.header p {
    font-size: 1.1rem;
    color: #7f8c8d;
    font-weight: 300;
}

/* Control Panel */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: center;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.control-group select {
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s;
}

.control-group select:focus {
    border-color: #667eea;
    outline: none;
}

.control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    grid-column: 1 / -1;
    justify-content: center;
}

/* Button Styles */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
}

.btn-warning {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #a8edea, #fed6e3);
    color: #333;
}

/* Visualization Container */
.visualization-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

.array-container {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    height: 350px;
    gap: 2px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    overflow-x: auto;
}

.array-bar {
    background: linear-gradient(to top, #667eea, #764ba2);
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
    position: relative;
    min-width: 8px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.array-bar.comparing {
    background: linear-gradient(to top, #ff6b6b, #ee5a24);
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.array-bar.swapping {
    background: linear-gradient(to top, #f093fb, #f5576c);
    transform: scale(1.2);
    box-shadow: 0 8px 20px rgba(240, 147, 251, 0.6);
}

.array-bar.sorted {
    background: linear-gradient(to top, #56ab2f, #a8e6cf);
    box-shadow: 0 3px 10px rgba(86, 171, 47, 0.3);
}

.array-bar.pivot {
    background: linear-gradient(to top, #ffa726, #ff7043);
    transform: scale(1.15);
    box-shadow: 0 6px 18px rgba(255, 167, 38, 0.5);
}

/* Statistics Panel */
.stats-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.stat-item h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    grid-column: 1 / -1;
    text-align: center;
    font-size: 1.5rem;
}

.stat-item label {
    display: block;
    font-weight: 500;
    color: #7f8c8d;
    margin-bottom: 5px;
}

.stat-item span {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

/* Algorithm Info */
.algorithm-info {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.algorithm-info h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    text-align: center;
}

.info-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
}

.complexity-info h4,
.description h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.complexity-info p {
    margin-bottom: 8px;
    font-size: 1rem;
}

.description p {
    line-height: 1.6;
    color: #555;
}

/* Comparison Section */
.comparison-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.comparison-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.comparison-table {
    margin-top: 20px;
    overflow-x: auto;
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.comparison-table th,
.comparison-table td {
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
}

.comparison-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
}

.comparison-table tr:hover {
    background: #f8f9fa;
}

/* Student Demo */
.student-demo {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.student-demo h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.student-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.student-table-container {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.student-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.student-table th,
.student-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.student-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.student-table tr:hover {
    background: #f8f9fa;
}

.student-table tr.highlight {
    background: #fff3cd;
    animation: highlight 1s ease-in-out;
}

@keyframes highlight {
    0% { background: #ffeaa7; }
    100% { background: #fff3cd; }
}

/* Code Section */
.code-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.code-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.code-container {
    background: #2d3748;
    border-radius: 10px;
    overflow: hidden;
}

.code-container pre {
    margin: 0;
    padding: 20px;
    overflow-x: auto;
}

.code-container code {
    color: #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 15px;
}

.loading-spinner p {
    color: #2c3e50;
    font-weight: 500;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}

.footer p {
    color: #7f8c8d;
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .control-panel {
        grid-template-columns: 1fr;
    }
    
    .info-content {
        grid-template-columns: 1fr;
    }
    
    .student-controls {
        flex-direction: column;
    }
    
    .array-container {
        height: 250px;
        padding: 10px;
    }
    
    .array-bar {
        min-width: 6px;
        font-size: 0.7rem;
    }
}
