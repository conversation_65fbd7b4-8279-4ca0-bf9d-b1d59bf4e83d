<?php
/**
 * <PERSON><PERSON><PERSON> hình VirtueMart cho Website Dược phẩm
 * 
 * @package    VirtueMart
 * @subpackage Config
 * <AUTHOR> VirtueMart Team
 * @copyright  2025 Pharmacy VirtueMart
 * @license    GNU/GPL
 */

defined('_JEXEC') or die('Restricted access');

/**
 * C<PERSON>u hình cửa hàng dược phẩm
 */
class PharmacyVirtueMartConfig {
    
    /**
     * Thông tin cửa hàng
     */
    public static $shop_config = [
        'shop_name' => 'Nhà Thuốc An Khang',
        'company_name' => 'Công ty TNHH Dược phẩm An Khang',
        'shop_email' => '<EMAIL>',
        'shop_phone' => '1900-1234',
        'shop_address' => '123 Đường ABC, Quận 1, TP. Hồ Chí Minh',
        'shop_description' => 'Nhà thuốc uy tín, chất lượng cao',
        'currency' => 'VND',
        'currency_symbol' => '₫',
        'tax_rate' => 10, // VAT 10%
        'timezone' => 'Asia/Ho_Chi_<PERSON>'
    ];
    
    /**
     * <PERSON><PERSON><PERSON> hình sản phẩm dược phẩm
     */
    public static $product_config = [
        'show_prescription_required' => true,
        'show_drug_registration' => true,
        'show_active_ingredient' => true,
        'show_dosage_form' => true,
        'show_storage_condition' => true,
        'show_expiry_warning' => true,
        'expiry_warning_days' => 90,
        'low_stock_warning' => true,
        'minimum_stock_level' => 10
    ];
    
    /**
     * Cấu hình đơn thuốc kê đơn
     */
    public static $prescription_config = [
        'require_prescription_upload' => true,
        'prescription_validity_days' => 30,
        'require_doctor_verification' => true,
        'require_patient_info' => true,
        'max_prescription_file_size' => '5MB',
        'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf']
    ];
    
    /**
     * Cấu hình thanh toán
     */
    public static $payment_config = [
        'cod_enabled' => true,
        'cod_fee' => 15000,
        'bank_transfer_enabled' => true,
        'credit_card_enabled' => true,
        'ewallet_enabled' => true,
        'minimum_order_amount' => 50000
    ];
    
    /**
     * Cấu hình vận chuyển
     */
    public static $shipping_config = [
        'standard_shipping' => [
            'name' => 'Giao hàng tiêu chuẩn',
            'cost' => 25000,
            'delivery_time' => '2-3 ngày',
            'free_shipping_threshold' => 500000
        ],
        'express_shipping' => [
            'name' => 'Giao hàng nhanh',
            'cost' => 50000,
            'delivery_time' => '1-2 ngày',
            'available_cities' => ['TP.HCM', 'Hà Nội', 'Đà Nẵng']
        ],
        'same_day_delivery' => [
            'name' => 'Giao hàng trong ngày',
            'cost' => 80000,
            'delivery_time' => '4-6 giờ',
            'available_areas' => ['TP.HCM nội thành']
        ],
        'store_pickup' => [
            'name' => 'Nhận tại cửa hàng',
            'cost' => 0,
            'delivery_time' => 'Ngay'
        ]
    ];
    
    /**
     * Cấu hình email
     */
    public static $email_config = [
        'order_confirmation' => true,
        'order_status_update' => true,
        'prescription_verification' => true,
        'low_stock_alert' => true,
        'expiry_warning' => true,
        'admin_email' => '<EMAIL>',
        'pharmacist_email' => '<EMAIL>'
    ];
    
    /**
     * Cấu hình bảo mật
     */
    public static $security_config = [
        'enable_ssl' => true,
        'session_timeout' => 3600, // 1 hour
        'max_login_attempts' => 5,
        'password_min_length' => 8,
        'require_strong_password' => true,
        'enable_captcha' => true,
        'log_admin_actions' => true
    ];
    
    /**
     * Danh mục sản phẩm dược phẩm
     */
    public static $pharmacy_categories = [
        'prescription_drugs' => [
            'name' => 'Thuốc kê đơn',
            'code' => 'PRESCRIPTION',
            'requires_prescription' => true,
            'subcategories' => [
                'antibiotics' => 'Kháng sinh',
                'cardiovascular' => 'Thuốc tim mạch',
                'gastrointestinal' => 'Thuốc tiêu hóa',
                'neurological' => 'Thuốc thần kinh'
            ]
        ],
        'otc_drugs' => [
            'name' => 'Thuốc không kê đơn',
            'code' => 'OTC',
            'requires_prescription' => false,
            'subcategories' => [
                'pain_relief' => 'Thuốc giảm đau',
                'cold_flu' => 'Thuốc cảm cúm',
                'stomach' => 'Thuốc dạ dày',
                'topical' => 'Thuốc ngoài da'
            ]
        ],
        'supplements' => [
            'name' => 'Thực phẩm chức năng',
            'code' => 'SUPPLEMENT',
            'requires_prescription' => false,
            'subcategories' => [
                'vitamins' => 'Vitamin & Khoáng chất',
                'herbal' => 'Thảo dược',
                'children' => 'Dinh dưỡng trẻ em'
            ]
        ],
        'medical_devices' => [
            'name' => 'Dụng cụ y tế',
            'code' => 'MEDICAL_DEVICE',
            'requires_prescription' => false,
            'subcategories' => [
                'measuring' => 'Thiết bị đo',
                'bandages' => 'Băng gạc',
                'first_aid' => 'Dụng cụ sơ cứu'
            ]
        ]
    ];
    
    /**
     * Cấu hình báo cáo
     */
    public static $report_config = [
        'daily_sales_report' => true,
        'inventory_report' => true,
        'expiry_report' => true,
        'prescription_report' => true,
        'customer_report' => true,
        'supplier_report' => true,
        'auto_email_reports' => true,
        'report_recipients' => [
            '<EMAIL>',
            '<EMAIL>'
        ]
    ];
    
    /**
     * Cấu hình tích điểm
     */
    public static $loyalty_config = [
        'enable_loyalty_program' => true,
        'points_per_vnd' => 0.001, // 1 điểm cho 1000 VND
        'points_to_vnd_ratio' => 0.1, // 1 điểm = 0.1 VND
        'birthday_bonus_points' => 500,
        'referral_bonus_points' => 1000,
        'points_expiry_months' => 12
    ];
    
    /**
     * Lấy cấu hình theo key
     */
    public static function getConfig($key, $default = null) {
        $configs = [
            'shop' => self::$shop_config,
            'product' => self::$product_config,
            'prescription' => self::$prescription_config,
            'payment' => self::$payment_config,
            'shipping' => self::$shipping_config,
            'email' => self::$email_config,
            'security' => self::$security_config,
            'categories' => self::$pharmacy_categories,
            'report' => self::$report_config,
            'loyalty' => self::$loyalty_config
        ];
        
        return isset($configs[$key]) ? $configs[$key] : $default;
    }
    
    /**
     * Kiểm tra sản phẩm có cần đơn thuốc không
     */
    public static function requiresPrescription($category_code) {
        $categories = self::$pharmacy_categories;
        foreach ($categories as $category) {
            if ($category['code'] === $category_code) {
                return $category['requires_prescription'];
            }
        }
        return false;
    }
    
    /**
     * Tính phí vận chuyển
     */
    public static function calculateShippingFee($method, $order_total, $location = '') {
        $shipping = self::$shipping_config;
        
        if (!isset($shipping[$method])) {
            return 0;
        }
        
        $config = $shipping[$method];
        $fee = $config['cost'];
        
        // Miễn phí vận chuyển cho đơn hàng lớn
        if ($method === 'standard_shipping' && 
            isset($config['free_shipping_threshold']) &&
            $order_total >= $config['free_shipping_threshold']) {
            $fee = 0;
        }
        
        return $fee;
    }
    
    /**
     * Kiểm tra tính khả dụng của phương thức vận chuyển
     */
    public static function isShippingMethodAvailable($method, $location = '') {
        $shipping = self::$shipping_config;
        
        if (!isset($shipping[$method])) {
            return false;
        }
        
        $config = $shipping[$method];
        
        // Kiểm tra khu vực giao hàng
        if (isset($config['available_cities']) && !empty($location)) {
            return in_array($location, $config['available_cities']);
        }
        
        if (isset($config['available_areas']) && !empty($location)) {
            return in_array($location, $config['available_areas']);
        }
        
        return true;
    }
}

/**
 * Helper functions cho VirtueMart
 */
class PharmacyVirtueMartHelper {
    
    /**
     * Format giá tiền VND
     */
    public static function formatPrice($amount) {
        return number_format($amount, 0, ',', '.') . ' ₫';
    }
    
    /**
     * Kiểm tra hạn sử dụng
     */
    public static function checkExpiryDate($expiry_date, $warning_days = 90) {
        $expiry = new DateTime($expiry_date);
        $now = new DateTime();
        $warning_date = $now->add(new DateInterval('P' . $warning_days . 'D'));
        
        return $expiry <= $warning_date;
    }
    
    /**
     * Tạo mã đơn hàng
     */
    public static function generateOrderNumber() {
        return 'DH' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * Validate file upload đơn thuốc
     */
    public static function validatePrescriptionFile($file) {
        $config = PharmacyVirtueMartConfig::getConfig('prescription');
        
        // Kiểm tra kích thước file
        $max_size = self::convertToBytes($config['max_prescription_file_size']);
        if ($file['size'] > $max_size) {
            return ['error' => 'File quá lớn. Tối đa ' . $config['max_prescription_file_size']];
        }
        
        // Kiểm tra định dạng file
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_ext, $config['allowed_file_types'])) {
            return ['error' => 'Định dạng file không được hỗ trợ'];
        }
        
        return ['success' => true];
    }
    
    /**
     * Convert size string to bytes
     */
    private static function convertToBytes($size_str) {
        $size_str = trim($size_str);
        $last = strtolower($size_str[strlen($size_str)-1]);
        $size = (int) $size_str;
        
        switch($last) {
            case 'g': $size *= 1024;
            case 'm': $size *= 1024;
            case 'k': $size *= 1024;
        }
        
        return $size;
    }
}
?>
