@model RealEstateWebsite.Models.ViewModels.PropertySearchResultViewModel

@{
    ViewData["Title"] = "Tìm <PERSON> Bất Động Sản";
}

<div class="container my-5">
    <!-- Search Form -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-search"></i> Tì<PERSON> Bất Động Sản</h4>
        </div>
        <div class="card-body">
            <form asp-action="Index" method="get">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Từ khóa</label>
                        <input type="text" name="Keyword" value="@Model.SearchCriteria.Keyword" class="form-control" placeholder="Nhập từ khóa...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Loại BDS</label>
                        <select name="CategoryId" class="form-select">
                            <option value="">Tất cả</option>
                            @foreach (var category in Model.Categories)
                            {
                                <option value="@category.CategoryId" selected="@(Model.SearchCriteria.CategoryId == category.CategoryId)">
                                    @category.Name
                                </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Thành phố</label>
                        <select name="City" class="form-select">
                            <option value="">Tất cả</option>
                            @foreach (var city in Model.Cities)
                            {
                                <option value="@city" selected="@(Model.SearchCriteria.City == city)">@city</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Giá từ</label>
                        <select name="MinPrice" class="form-select">
                            <option value="">Không giới hạn</option>
                            <option value="500000000">500 triệu</option>
                            <option value="1000000000">1 tỷ</option>
                            <option value="2000000000">2 tỷ</option>
                            <option value="5000000000">5 tỷ</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Giá đến</label>
                        <select name="MaxPrice" class="form-select">
                            <option value="">Không giới hạn</option>
                            <option value="1000000000">1 tỷ</option>
                            <option value="2000000000">2 tỷ</option>
                            <option value="5000000000">5 tỷ</option>
                            <option value="10000000000">10 tỷ</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div class="row">
        <div class="col-md-9">
            <!-- Results Header -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Tìm thấy @Model.Pagination.TotalItems bất động sản</h5>
                <div class="d-flex align-items-center">
                    <span class="me-2">Sắp xếp:</span>
                    <select class="form-select form-select-sm" style="width: auto;" onchange="changeSorting(this.value)">
                        <option value="CreatedDate_Desc">Mới nhất</option>
                        <option value="Price_Asc">Giá thấp đến cao</option>
                        <option value="Price_Desc">Giá cao đến thấp</option>
                        <option value="Area_Desc">Diện tích lớn nhất</option>
                    </select>
                </div>
            </div>

            <!-- Properties List -->
            @if (Model.Properties.Any())
            {
                <div class="row">
                    @foreach (var property in Model.Properties)
                    {
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 shadow-sm">
                                <div class="position-relative">
                                    <img src="@(!string.IsNullOrEmpty(property.MainImage) ? property.MainImage : "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80")" 
                                         class="card-img-top" alt="@property.Title" style="height: 200px; object-fit: cover;">
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-primary">@(property.Type == PropertyType.Sale ? "Bán" : "Cho thuê")</span>
                                    </div>
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <button class="btn btn-sm btn-light rounded-circle" onclick="toggleFavorite(@property.PropertyId)">
                                            <i class="far fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <a asp-action="Details" asp-route-id="@property.PropertyId" class="text-decoration-none">
                                            @property.Title
                                        </a>
                                    </h6>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-map-marker-alt"></i> @property.Address, @property.District, @property.City
                                    </p>
                                    <div class="row text-center mb-2">
                                        <div class="col-4">
                                            <small class="text-muted">Phòng ngủ</small><br>
                                            <strong>@property.Bedrooms</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">Phòng tắm</small><br>
                                            <strong>@property.Bathrooms</strong>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">Diện tích</small><br>
                                            <strong>@property.Area m²</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="text-primary mb-0">@property.Price.ToString("N0") VNĐ</h5>
                                        <a asp-action="Details" asp-route-id="@property.PropertyId" class="btn btn-outline-primary btn-sm">
                                            Chi tiết
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (Model.Pagination.TotalPages > 1)
                {
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            @if (Model.Pagination.HasPreviousPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" asp-action="Index" asp-route-page="@(Model.Pagination.CurrentPage - 1)">Trước</a>
                                </li>
                            }
                            
                            @for (int i = Math.Max(1, Model.Pagination.CurrentPage - 2); i <= Math.Min(Model.Pagination.TotalPages, Model.Pagination.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.Pagination.CurrentPage ? "active" : "")">
                                    <a class="page-link" asp-action="Index" asp-route-page="@i">@i</a>
                                </li>
                            }
                            
                            @if (Model.Pagination.HasNextPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" asp-action="Index" asp-route-page="@(Model.Pagination.CurrentPage + 1)">Sau</a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>Không tìm thấy bất động sản nào</h4>
                    <p class="text-muted">Hãy thử thay đổi tiêu chí tìm kiếm</p>
                </div>
            }
        </div>

        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Thống kê tìm kiếm</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Tổng tìm thấy</small><br>
                        <strong>@Model.Statistics.TotalFound bất động sản</strong>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Nhà đất bán</small><br>
                        <strong>@Model.Statistics.ForSale</strong>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Nhà đất cho thuê</small><br>
                        <strong>@Model.Statistics.ForRent</strong>
                    </div>
                    @if (Model.Statistics.MinPrice.HasValue && Model.Statistics.MaxPrice.HasValue)
                    {
                        <div class="mb-3">
                            <small class="text-muted">Khoảng giá</small><br>
                            <strong>@Model.Statistics.MinPrice.Value.ToString("N0") - @Model.Statistics.MaxPrice.Value.ToString("N0") VNĐ</strong>
                        </div>
                    }
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card shadow-sm mt-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">Danh mục phổ biến</h6>
                </div>
                <div class="card-body">
                    @foreach (var category in Model.Categories.Take(5))
                    {
                        <a asp-action="Index" asp-route-categoryId="@category.CategoryId" class="d-block text-decoration-none mb-2">
                            <i class="@category.Icon me-2"></i>@category.Name
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function changeSorting(value) {
            const [sortBy, sortDirection] = value.split('_');
            const url = new URL(window.location);
            url.searchParams.set('SortBy', sortBy);
            url.searchParams.set('SortDirection', sortDirection);
            window.location.href = url.toString();
        }

        function toggleFavorite(propertyId) {
            // Add favorite functionality here
            alert('Tính năng yêu thích sẽ được thêm sau!');
        }
    </script>
}
