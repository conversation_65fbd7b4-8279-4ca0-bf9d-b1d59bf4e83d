@model RealEstateWebsite.Models.ViewModels.UserLoginViewModel

@{
    ViewData["Title"] = "Đăng Nhập";
}

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow border-0">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-in-alt"></i> Đăng Nhập
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Error Message -->
                    @if (ViewData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> @ViewData["ErrorMessage"]
                        </div>
                    }

                    <!-- Success Message -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
                        </div>
                    }

                    <form asp-action="Login" method="post">
                        <div class="mb-3">
                            <label asp-for="UsernameOrEmail" class="form-label">
                                <i class="fas fa-user"></i> Tên đăng nhập hoặc Email
                            </label>
                            <input asp-for="UsernameOrEmail" class="form-control form-control-lg" 
                                   placeholder="Nhập tên đăng nhập hoặc email" autofocus>
                            <span asp-validation-for="UsernameOrEmail" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">
                                <i class="fas fa-lock"></i> Mật khẩu
                            </label>
                            <div class="input-group">
                                <input asp-for="Password" type="password" class="form-control form-control-lg" 
                                       placeholder="Nhập mật khẩu" id="passwordInput">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" type="checkbox">
                            <label asp-for="RememberMe" class="form-check-label">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt"></i> Đăng Nhập
                            </button>
                        </div>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none">Quên mật khẩu?</a>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center">
                    <p class="mb-0">
                        Chưa có tài khoản? 
                        <a asp-action="Register" class="text-primary text-decoration-none fw-bold">
                            Đăng ký ngay
                        </a>
                    </p>
                </div>
            </div>

            <!-- Demo Account Info -->
            <div class="card mt-3 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Tài Khoản Demo</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Khách hàng:</strong><br>
                            <small>Username: customer</small><br>
                            <small>Password: 123456</small>
                        </div>
                        <div class="col-md-6">
                            <strong>Quản trị viên:</strong><br>
                            <small>Username: admin</small><br>
                            <small>Password: admin123</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('passwordInput');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Auto-fill demo accounts
        function fillDemoAccount(username, password) {
            document.querySelector('input[name="UsernameOrEmail"]').value = username;
            document.querySelector('input[name="Password"]').value = password;
        }

        // Add click handlers for demo accounts
        document.addEventListener('DOMContentLoaded', function() {
            const demoCard = document.querySelector('.card.border-info .card-body');
            if (demoCard) {
                demoCard.addEventListener('click', function(e) {
                    if (e.target.closest('.col-md-6:first-child')) {
                        fillDemoAccount('customer', '123456');
                    } else if (e.target.closest('.col-md-6:last-child')) {
                        fillDemoAccount('admin', 'admin123');
                    }
                });
                
                // Add cursor pointer style
                demoCard.style.cursor = 'pointer';
                demoCard.title = 'Click để tự động điền thông tin đăng nhập';
            }
        });
    </script>
}
