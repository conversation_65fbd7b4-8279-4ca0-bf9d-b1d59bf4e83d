/*
 * ĐỒ ÁN NGHIÊN CỨU: BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ
 * 
 * <PERSON><PERSON> tả: Cà<PERSON> đặt thuật to<PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON>) để giải bài toán
 *        tìm luồng cực đại từ đỉnh nguồn đến đỉnh đích trong đồ thị có hướng
 * 
 * Tác giả: [Tên sinh viên]
 * Ngày: [Ng<PERSON><PERSON> hoàn thành]
 * 
 * Thuật toán sử dụng:
 * - Ford-Fulkerson với BFS (Edmonds-Karp)
 * - Đ<PERSON> phức tạp: O(VE²)
 * 
 * Tính năng:
 * - Nhập đồ thị từ người dùng
 * - Tìm luồng cực đại với hiển thị từng bước
 * - Tìm lát cắt tối thiểu
 * - Demo với đồ thị mẫu
 * - Giao diện menu tương tác
 */

#include "Graph.h"
#include <iostream>
#include <string>
#include <clocale>

using namespace std;

// Hàm hiển thị tiêu đề chương trình
void displayTitle() {
    cout << "\n";
    cout << "╔══════════════════════════════════════════════════════════════════════════════╗\n";
    cout << "║                    ĐỒ ÁN NGHIÊN CỨU KHOA HỌC MÁY TÍNH                      ║\n";
    cout << "║                                                                              ║\n";
    cout << "║           BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ                  ║\n";
    cout << "║                                                                              ║\n";
    cout << "║                    THUẬT TOÁN FORD-FULKERSON                                ║\n";
    cout << "║                        (EDMONDS-KARP)                                       ║\n";
    cout << "║                                                                              ║\n";
    cout << "╚══════════════════════════════════════════════════════════════════════════════╝\n";
    cout << "\n";
}

// Hàm hiển thị thông tin tác giả và đồ án
void displayInfo() {
    cout << "┌─────────────────────────────────────────────────────────────────────────────┐\n";
    cout << "│ THÔNG TIN ĐỒ ÁN:                                                           │\n";
    cout << "│                                                                             │\n";
    cout << "│ • Đề tài: Nghiên cứu bài toán luồng cực đại và cài đặt minh họa           │\n";
    cout << "│ • Thuật toán: Ford-Fulkerson với BFS (Edmonds-Karp)                       │\n";
    cout << "│ • Ngôn ngữ: C++                                                            │\n";
    cout << "│ • Độ phức tạp: O(VE²)                                                      │\n";
    cout << "│                                                                             │\n";
    cout << "│ TÍNH NĂNG CHÍNH:                                                           │\n";
    cout << "│ ✓ Tìm luồng cực đại từ nguồn đến đích                                      │\n";
    cout << "│ ✓ Hiển thị từng bước thực hiện thuật toán                                  │\n";
    cout << "│ ✓ Tìm lát cắt tối thiểu (Min-Cut)                                          │\n";
    cout << "│ ✓ Demo với đồ thị mẫu                                                      │\n";
    cout << "│ ✓ Giao diện menu tương tác thân thiện                                      │\n";
    cout << "└─────────────────────────────────────────────────────────────────────────────┘\n";
}

// Hàm menu chính
void mainMenu() {
    int choice;
    Graph* currentGraph = nullptr;
    
    do {
        cout << "\n";
        cout << "╔═══════════════════════════════════════════════════════════════════════════╗\n";
        cout << "║                              MENU CHÍNH                                  ║\n";
        cout << "╠═══════════════════════════════════════════════════════════════════════════╣\n";
        cout << "║  1. Tạo đồ thị mới                                                       ║\n";
        cout << "║  2. Sử dụng đồ thị mẫu                                                   ║\n";
        cout << "║  3. Hướng dẫn sử dụng                                                    ║\n";
        cout << "║  4. Thông tin thuật toán                                                 ║\n";
        cout << "║  0. Thoát chương trình                                                   ║\n";
        cout << "╚═══════════════════════════════════════════════════════════════════════════╝\n";
        cout << "Nhập lựa chọn: ";
        cin >> choice;
        
        switch (choice) {
            case 1: {
                int vertices;
                cout << "\nNhập số đỉnh của đồ thị: ";
                cin >> vertices;
                
                if (vertices <= 0) {
                    cout << "Số đỉnh phải lớn hơn 0!\n";
                    break;
                }
                
                if (currentGraph != nullptr) {
                    delete currentGraph;
                }
                
                currentGraph = new Graph(vertices);
                currentGraph->inputGraph();
                currentGraph->showMenu();
                break;
            }
            
            case 2: {
                if (currentGraph != nullptr) {
                    delete currentGraph;
                }
                
                cout << "\n=== CHỌN ĐỒ THỊ MẪU ===\n";
                cout << "1. Đồ thị đơn giản (4 đỉnh)\n";
                cout << "2. Đồ thị phức tạp (6 đỉnh)\n";
                cout << "3. Mạng giao thông (8 đỉnh)\n";
                cout << "Chọn: ";
                
                int sampleChoice;
                cin >> sampleChoice;
                
                switch (sampleChoice) {
                    case 1:
                        currentGraph = new Graph(GraphUtils::createSampleGraph1());
                        break;
                    case 2:
                        currentGraph = new Graph(GraphUtils::createSampleGraph2());
                        break;
                    case 3:
                        currentGraph = new Graph(GraphUtils::createSampleGraph3());
                        break;
                    default:
                        cout << "Lựa chọn không hợp lệ!\n";
                        continue;
                }
                
                currentGraph->showMenu();
                break;
            }
            
            case 3:
                GraphUtils::displayUsageGuide();
                break;
                
            case 4:
                GraphUtils::displayAlgorithmInfo();
                break;
                
            case 0:
                cout << "\n";
                cout << "╔═══════════════════════════════════════════════════════════════════════════╗\n";
                cout << "║                     CẢM ƠN BẠN ĐÃ SỬ DỤNG CHƯƠNG TRÌNH!                 ║\n";
                cout << "║                                                                           ║\n";
                cout << "║              Chúc bạn thành công trong việc nghiên cứu!                  ║\n";
                cout << "╚═══════════════════════════════════════════════════════════════════════════╝\n";
                break;
                
            default:
                cout << "Lựa chọn không hợp lệ! Vui lòng chọn lại.\n";
        }
    } while (choice != 0);
    
    // Giải phóng bộ nhớ
    if (currentGraph != nullptr) {
        delete currentGraph;
    }
}

// Hàm demo nhanh (tùy chọn)
void quickDemo() {
    cout << "\n=== DEMO NHANH THUẬT TOÁN FORD-FULKERSON ===\n";
    
    // Tạo đồ thị mẫu đơn giản
    Graph demo = GraphUtils::createSampleGraph1();
    
    cout << "Đồ thị demo:\n";
    demo.displayAdjacencyMatrix();
    
    cout << "Tìm luồng cực đại từ đỉnh 0 đến đỉnh 3:\n";
    int maxFlow = demo.fordFulkerson(0, 3);
    
    cout << "Tìm lát cắt tối thiểu:\n";
    demo.findMinCut(0, 3);
    
    cout << "\nDemo hoàn thành! Nhấn Enter để tiếp tục...";
    cin.ignore();
    cin.get();
}

// Hàm main
int main() {
    // Thiết lập locale cho tiếng Việt (nếu hệ thống hỗ trợ)
    setlocale(LC_ALL, "");
    
    // Hiển thị tiêu đề
    displayTitle();
    displayInfo();
    
    // Hỏi người dùng có muốn xem demo nhanh không
    cout << "\nBạn có muốn xem demo nhanh thuật toán không? (y/n): ";
    char choice;
    cin >> choice;
    
    if (choice == 'y' || choice == 'Y') {
        quickDemo();
    }
    
    // Chạy menu chính
    mainMenu();
    
    return 0;
}
