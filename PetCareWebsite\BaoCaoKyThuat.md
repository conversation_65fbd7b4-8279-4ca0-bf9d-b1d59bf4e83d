# BÁO CÁO KỸ THUẬT - WEBSITE CHĂM SÓC THÚ CƯNG

## 1. TỔNG QUAN DỰ ÁN

### 1.1 Thông tin dự án
- **Tên dự án**: Website Chăm Sóc Thú Cưng
- **Công nghệ**: ASP.NET Core MVC (.NET 9.0)
- **Cơ sở dữ liệu**: SQLite với Entity Framework Core
- **Ngôn ngữ**: Tiếng Việt (Vietnamese Localization)
- **Thiết kế**: Responsive, tối ưu cho cả desktop và mobile

### 1.2 Mục tiêu dự án
Xây dựng một website hoàn chỉnh cho trung tâm chăm sóc thú cưng với các chức năng:
- Quản lý dịch vụ chăm sóc thú cưng (12+ dịch vụ)
- Hệ thống đăng ký/đăng nhập người dùng
- Quản lý thông tin thú cưng cá nhân
- Đặt lịch hẹn dịch vụ
- <PERSON>hu vực quản trị viên với dashboard thống kê
- <PERSON><PERSON><PERSON> diệ<PERSON> thân thiện, dễ sử dụng

## 2. KIẾN TRÚC HỆ THỐNG

### 2.1 Kiến trúc tổng thể
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │    Business     │    │      Data       │
│     Layer       │◄──►│     Layer       │◄──►│     Layer       │
│                 │    │                 │    │                 │
│ - Controllers   │    │ - Services      │    │ - DbContext     │
│ - Views         │    │ - ViewModels    │    │ - Models        │
│ - ViewModels    │    │ - Validation    │    │ - Migrations    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Cấu trúc thư mục dự án
```
PetCareWebsite/
├── Controllers/           # Các controller xử lý logic
│   ├── HomeController.cs
│   ├── AccountController.cs
│   ├── ServicesController.cs
│   ├── PetsController.cs
│   ├── AppointmentsController.cs
│   └── AdminController.cs
├── Models/               # Các model dữ liệu
│   └── ErrorViewModel.cs (chứa tất cả models)
├── Views/                # Các view template
│   ├── Home/
│   ├── Account/
│   ├── Services/
│   ├── Pets/
│   ├── Appointments/
│   ├── Admin/
│   └── Shared/
├── ViewModels/           # Các ViewModel
│   └── AccountViewModels.cs
├── Data/                 # Lớp truy cập dữ liệu
│   └── ApplicationDbContext.cs
├── Services/             # Các service
│   └── DbInitializer.cs
├── wwwroot/              # Tài nguyên tĩnh
│   ├── css/
│   ├── js/
│   ├── lib/
│   └── images/
└── Migrations/           # Database migrations
```

## 3. THIẾT KẾ CƠ SỞ DỮ LIỆU

### 3.1 Sơ đồ ERD
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  ApplicationUser│    │      Pet        │    │    Service      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ Id (PK)         │◄──┐│ Id (PK)         │    │ Id (PK)         │
│ FullName        │   └│ UserId (FK)     │    │ Name            │
│ Email           │    │ Name            │    │ Description     │
│ PhoneNumber     │    │ Species         │    │ Price           │
│ Address         │    │ Breed           │    │ DurationMinutes │
│ CreatedAt       │    │ Age             │    │ ImageUrl        │
└─────────────────┘    │ Gender          │    │ IsActive        │
                       │ Weight          │    │ CreatedAt       │
                       │ Description     │    └─────────────────┘
                       │ CreatedAt       │             │
                       └─────────────────┘             │
                                │                      │
                                │    ┌─────────────────┐
                                │    │  Appointment    │
                                │    ├─────────────────┤
                                │    │ Id (PK)         │
                                └───►│ PetId (FK)      │
                                     │ ServiceId (FK)  │◄────┘
                                ┌───►│ UserId (FK)     │
                                │    │ AppointmentDate │
┌─────────────────┐             │    │ AppointmentTime │
│  ApplicationUser│─────────────┘    │ Status          │
│     (Admin)     │                  │ Notes           │
└─────────────────┘                  │ TotalAmount     │
                                     │ CreatedAt       │
                                     └─────────────────┘
```

### 3.2 Mô tả các bảng chính

#### ApplicationUser (Người dùng)
- **Id**: Khóa chính (GUID)
- **FullName**: Họ tên đầy đủ
- **Email**: Email đăng nhập
- **PhoneNumber**: Số điện thoại
- **Address**: Địa chỉ
- **CreatedAt**: Thời gian tạo tài khoản

#### Pet (Thú cưng)
- **Id**: Khóa chính (Auto increment)
- **UserId**: Khóa ngoại tham chiếu đến ApplicationUser
- **Name**: Tên thú cưng
- **Species**: Loài (Chó, Mèo, Chim, v.v.)
- **Breed**: Giống
- **Age**: Tuổi
- **Gender**: Giới tính (Đực/Cái)
- **Weight**: Cân nặng (kg)
- **Description**: Mô tả thêm

#### Service (Dịch vụ)
- **Id**: Khóa chính (Auto increment)
- **Name**: Tên dịch vụ
- **Description**: Mô tả chi tiết
- **Price**: Giá dịch vụ (VNĐ)
- **DurationMinutes**: Thời gian thực hiện (phút)
- **ImageUrl**: Đường dẫn hình ảnh
- **IsActive**: Trạng thái kích hoạt

#### Appointment (Lịch hẹn)
- **Id**: Khóa chính (Auto increment)
- **UserId**: Khóa ngoại tham chiếu đến ApplicationUser
- **PetId**: Khóa ngoại tham chiếu đến Pet
- **ServiceId**: Khóa ngoại tham chiếu đến Service
- **AppointmentDate**: Ngày hẹn
- **AppointmentTime**: Giờ hẹn
- **Status**: Trạng thái (Pending, Confirmed, Completed, Cancelled)
- **Notes**: Ghi chú
- **TotalAmount**: Tổng tiền

## 4. DANH SÁCH DỊCH VỤ

Website cung cấp 12 dịch vụ chăm sóc thú cưng:

1. **Tắm rửa cơ bản** - 150,000 VNĐ (60 phút)
2. **Cắt tỉa lông chuyên nghiệp** - 200,000 VNĐ (90 phút)
3. **Khám sức khỏe tổng quát** - 300,000 VNĐ (45 phút)
4. **Tiêm phòng định kỳ** - 250,000 VNĐ (30 phút)
5. **Spa thư giãn cao cấp** - 400,000 VNĐ (120 phút)
6. **Vệ sinh răng miệng** - 180,000 VNĐ (45 phút)
7. **Cắt móng chân chuyên nghiệp** - 80,000 VNĐ (20 phút)
8. **Điều trị ve rận** - 220,000 VNĐ (60 phút)
9. **Tư vấn dinh dưỡng** - 150,000 VNĐ (30 phút)
10. **Khám chuyên khoa** - 500,000 VNĐ (60 phút)
11. **Chăm sóc sau phẫu thuật** - 350,000 VNĐ (90 phút)
12. **Tắm thuốc trị liệu** - 280,000 VNĐ (75 phút)

## 5. CHỨC NĂNG HỆ THỐNG

### 5.1 Chức năng người dùng
- **Đăng ký/Đăng nhập**: Tạo tài khoản và đăng nhập hệ thống
- **Quản lý thú cưng**: Thêm, sửa, xóa thông tin thú cưng
- **Xem dịch vụ**: Duyệt danh sách và chi tiết các dịch vụ
- **Đặt lịch hẹn**: Đặt lịch hẹn cho thú cưng với dịch vụ mong muốn
- **Quản lý lịch hẹn**: Xem lịch sử và hủy lịch hẹn

### 5.2 Chức năng quản trị viên
- **Dashboard**: Thống kê tổng quan hệ thống
- **Quản lý dịch vụ**: Thêm, sửa, xóa, kích hoạt/vô hiệu hóa dịch vụ
- **Quản lý lịch hẹn**: Xem và cập nhật trạng thái lịch hẹn
- **Quản lý người dùng**: Xem thông tin người dùng và thú cưng
- **Báo cáo**: Thống kê doanh thu và hoạt động

### 5.3 Tài khoản quản trị viên mặc định
- **Username**: admin
- **Password**: admin@123
- **Email**: <EMAIL>
- **Role**: Admin

## 6. CÔNG NGHỆ SỬ DỤNG

### 6.1 Backend
- **Framework**: ASP.NET Core MVC 9.0
- **ORM**: Entity Framework Core
- **Database**: SQLite
- **Authentication**: ASP.NET Core Identity
- **Language**: C# 12.0

### 6.2 Frontend
- **Template Engine**: Razor Pages
- **CSS Framework**: Bootstrap 5
- **Icons**: Font Awesome 6
- **JavaScript**: Vanilla JS + Bootstrap JS
- **Responsive Design**: Mobile-first approach

### 6.3 Packages NuGet
```xml
<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.7" />
<PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.7" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.7" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7" />
```

## 7. BẢO MẬT VÀ PHÂN QUYỀN

### 7.1 Authentication
- Sử dụng ASP.NET Core Identity
- Mã hóa mật khẩu với BCrypt
- Session management tự động

### 7.2 Authorization
- **Role-based**: Admin và User roles
- **Controller-level**: [Authorize] attributes
- **Action-level**: Kiểm tra quyền sở hữu dữ liệu

### 7.3 Validation
- **Client-side**: HTML5 validation + Bootstrap
- **Server-side**: Data Annotations + ModelState
- **Database**: Foreign key constraints

## 8. HƯỚNG DẪN CÀI ĐẶT VÀ SỬ DỤNG

### 8.1 Yêu cầu hệ thống
- .NET 9.0 SDK
- Visual Studio 2022 hoặc VS Code
- SQLite (đã bao gồm trong dự án)

### 8.2 Cài đặt
1. Clone hoặc tải dự án về máy
2. Mở terminal tại thư mục dự án
3. Chạy lệnh: `dotnet restore`
4. Chạy lệnh: `dotnet build`
5. Chạy lệnh: `dotnet run`
6. Truy cập: http://localhost:5000

### 8.3 Sử dụng
1. **Truy cập trang chủ**: Xem thông tin và dịch vụ nổi bật
2. **Đăng ký tài khoản**: Tạo tài khoản người dùng mới
3. **Thêm thú cưng**: Nhập thông tin thú cưng cần chăm sóc
4. **Đặt lịch hẹn**: Chọn dịch vụ và đặt lịch hẹn
5. **Quản trị**: Đăng nhập admin để quản lý hệ thống

## 9. TÍNH NĂNG NỔI BẬT

### 9.1 Giao diện người dùng
- Thiết kế responsive, thân thiện
- Màu sắc phù hợp với chủ đề chăm sóc thú cưng
- Animation và hover effects
- Không có footer (theo yêu cầu)

### 9.2 Trải nghiệm người dùng
- Navigation rõ ràng, dễ sử dụng
- Form validation real-time
- Thông báo success/error
- Loading states và feedback

### 9.3 Tối ưu hóa
- Lazy loading cho hình ảnh
- Minified CSS/JS
- Database indexing
- Caching strategies

## 10. KẾT LUẬN

Website Chăm Sóc Thú Cưng đã được xây dựng thành công với đầy đủ các chức năng yêu cầu:
- ✅ 12+ dịch vụ chăm sóc thú cưng
- ✅ Hệ thống đăng ký/đăng nhập
- ✅ Quản lý thú cưng và đặt lịch hẹn
- ✅ Khu vực quản trị viên hoàn chỉnh
- ✅ Giao diện responsive, tiếng Việt
- ✅ Tài khoản admin mặc định
- ✅ Database design tối ưu
- ✅ Bảo mật và phân quyền

Website sẵn sàng để triển khai và sử dụng trong môi trường thực tế.
