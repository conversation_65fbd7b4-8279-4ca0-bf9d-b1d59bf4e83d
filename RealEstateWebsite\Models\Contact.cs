using System.ComponentModel.DataAnnotations;

namespace RealEstateWebsite.Models
{
    public class Contact
    {
        public int ContactId { get; set; }

        [Required(ErrorMessage = "Họ tên là bắt buộc")]
        [StringLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email là bắt buộc")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [StringLength(100, ErrorMessage = "Email không được vượt quá 100 ký tự")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Số điện thoại là bắt buộc")]
        [StringLength(20, ErrorMessage = "Số điện thoại không được vượt quá 20 ký tự")]
        public string Phone { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "Tiêu đề không được vượt quá 200 ký tự")]
        public string? Subject { get; set; }

        [Required(ErrorMessage = "Nội dung tin nhắn là bắt buộc")]
        [StringLength(2000, ErrorMessage = "Nội dung tin nhắn không được vượt quá 2000 ký tự")]
        public string Message { get; set; } = string.Empty;

        public ContactType Type { get; set; } = ContactType.General;

        public ContactStatus Status { get; set; } = ContactStatus.New;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ResponseDate { get; set; }

        [StringLength(2000)]
        public string? Response { get; set; }

        [StringLength(100)]
        public string? ResponseBy { get; set; }

        // Optional: Link to specific property
        public int? PropertyId { get; set; }
        public Property? Property { get; set; }

        // Additional fields for consultation requests
        [StringLength(100)]
        public string? PreferredContactTime { get; set; }

        public decimal? BudgetMin { get; set; }
        public decimal? BudgetMax { get; set; }

        [StringLength(200)]
        public string? PreferredLocation { get; set; }

        public PropertyType? InterestedPropertyType { get; set; }

        // Notes for admin
        [StringLength(1000)]
        public string? AdminNotes { get; set; }
    }

    public enum ContactType
    {
        General = 1,           // Liên hệ chung
        PropertyInquiry = 2,   // Hỏi về bất động sản
        Consultation = 3,      // Tư vấn
        Complaint = 4,         // Khiếu nại
        Suggestion = 5         // Góp ý
    }

    public enum ContactStatus
    {
        New = 1,              // Mới
        InProgress = 2,       // Đang xử lý
        Responded = 3,        // Đã phản hồi
        Closed = 4,           // Đã đóng
        Spam = 5              // Spam
    }
}
