// MÔ PHỎNG CHẠY CHƯƠNG TRÌNH FORD-FULKERSON
// Script JavaScript để demo thuật toán

class MaxFlowDemo {
    constructor(vertices) {
        this.V = vertices;
        this.graph = Array(vertices).fill().map(() => Array(vertices).fill(0));
        this.original = Array(vertices).fill().map(() => Array(vertices).fill(0));
    }
    
    addEdge(u, v, capacity) {
        this.graph[u][v] = capacity;
        this.original[u][v] = capacity;
    }
    
    displayMatrix() {
        console.log("\nMa trận kề:");
        let header = "    ";
        for (let i = 0; i < this.V; i++) {
            header += i.toString().padStart(4);
        }
        console.log(header);
        
        for (let i = 0; i < this.V; i++) {
            let row = i.toString().padStart(2) + ": ";
            for (let j = 0; j < this.V; j++) {
                row += this.original[i][j].toString().padStart(4);
            }
            console.log(row);
        }
        console.log();
    }
    
    bfs(source, sink, parent) {
        const visited = Array(this.V).fill(false);
        const queue = [source];
        visited[source] = true;
        
        while (queue.length > 0) {
            const u = queue.shift();
            
            for (let v = 0; v < this.V; v++) {
                if (!visited[v] && this.graph[u][v] > 0) {
                    if (v === sink) {
                        parent[v] = u;
                        return true;
                    }
                    queue.push(v);
                    parent[v] = u;
                    visited[v] = true;
                }
            }
        }
        
        return false;
    }
    
    displayPath(parent, source, sink) {
        const path = [];
        let current = sink;
        while (current !== -1) {
            path.push(current);
            current = parent[current];
        }
        path.reverse();
        console.log(path.join(" -> "));
    }
    
    fordFulkerson(source, sink) {
        console.log("\n=== BẮT ĐẦU THUẬT TOÁN FORD-FULKERSON ===");
        console.log(`Nguồn: ${source}, Đích: ${sink}\n`);
        
        // Tạo bản sao ma trận
        this.graph = this.original.map(row => [...row]);
        
        const parent = Array(this.V).fill(-1);
        let maxFlowValue = 0;
        let iteration = 0;
        
        while (this.bfs(source, sink, parent)) {
            iteration++;
            console.log(`--- Lần lặp ${iteration} ---`);
            
            // Tìm luồng tối thiểu trên đường tăng luồng
            let pathFlow = Infinity;
            let s = sink;
            while (s !== source) {
                pathFlow = Math.min(pathFlow, this.graph[parent[s]][s]);
                s = parent[s];
            }
            
            console.log("Đường tăng luồng tìm được: ", end="");
            this.displayPath(parent, source, sink);
            console.log(`Luồng tăng thêm: ${pathFlow}`);
            
            // Cập nhật khả năng dư
            let v = sink;
            while (v !== source) {
                const u = parent[v];
                this.graph[u][v] -= pathFlow;
                this.graph[v][u] += pathFlow;
                v = parent[v];
            }
            
            maxFlowValue += pathFlow;
            console.log(`Tổng luồng hiện tại: ${maxFlowValue}\n`);
        }
        
        console.log("=== KẾT THÚC THUẬT TOÁN ===");
        console.log(`Luồng cực đại từ ${source} đến ${sink}: ${maxFlowValue}\n`);
        
        return maxFlowValue;
    }
}

function main() {
    console.log("╔══════════════════════════════════════════════════════════════════════════════╗");
    console.log("║                    ĐỒ ÁN NGHIÊN CỨU KHOA HỌC MÁY TÍNH                      ║");
    console.log("║                                                                              ║");
    console.log("║           BÀI TOÁN LUỒNG CỰC ĐẠI TRÊN ĐỒ THỊ CÓ TRỌNG SỐ                  ║");
    console.log("║                                                                              ║");
    console.log("║                    THUẬT TOÁN FORD-FULKERSON                                ║");
    console.log("║                        (EDMONDS-KARP)                                       ║");
    console.log("║                                                                              ║");
    console.log("╚══════════════════════════════════════════════════════════════════════════════╝");
    
    console.log("\n=== DEMO THUẬT TOÁN FORD-FULKERSON ===");
    console.log("Nghiên cứu bài toán luồng cực đại\n");
    
    // Tạo đồ thị mẫu đơn giản
    console.log("Tạo đồ thị mẫu với 4 đỉnh:");
    const g = new MaxFlowDemo(4);
    
    // Thêm các cạnh
    g.addEdge(0, 1, 16);
    g.addEdge(0, 2, 13);
    g.addEdge(1, 2, 10);
    g.addEdge(1, 3, 12);
    g.addEdge(2, 1, 4);
    g.addEdge(2, 3, 14);
    
    console.log("Đồ thị đã tạo:");
    console.log("0 -> 1: 16");
    console.log("0 -> 2: 13");
    console.log("1 -> 2: 10");
    console.log("1 -> 3: 12");
    console.log("2 -> 1: 4");
    console.log("2 -> 3: 14");
    
    g.displayMatrix();
    
    console.log("Tìm luồng cực đại từ đỉnh 0 đến đỉnh 3:");
    const maxFlow = g.fordFulkerson(0, 3);
    
    console.log("KẾT QUẢ CUỐI CÙNG:");
    console.log(`Luồng cực đại = ${maxFlow}`);
    console.log("Kết quả mong đợi = 23");
    
    if (maxFlow === 23) {
        console.log("✓ THUẬT TOÁN HOẠT ĐỘNG ĐÚNG!");
    } else {
        console.log("✗ CÓ LỖI TRONG THUẬT TOÁN!");
    }
    
    console.log("\n" + "=".repeat(80));
    
    // Test case 2
    console.log("\n=== TEST CASE 2: ĐỒ THỊ PHỨC TẠP ===");
    console.log("Đồ thị 6 đỉnh với nhiều đường đi");
    
    const g2 = new MaxFlowDemo(6);
    g2.addEdge(0, 1, 10);
    g2.addEdge(0, 2, 8);
    g2.addEdge(1, 2, 5);
    g2.addEdge(1, 3, 5);
    g2.addEdge(2, 4, 10);
    g2.addEdge(3, 2, 7);
    g2.addEdge(3, 4, 8);
    g2.addEdge(3, 5, 10);
    g2.addEdge(4, 5, 10);
    
    g2.displayMatrix();
    
    console.log("Tìm luồng cực đại từ đỉnh 0 đến đỉnh 5:");
    const maxFlow2 = g2.fordFulkerson(0, 5);
    
    console.log(`Kết quả: ${maxFlow2}`);
    console.log("Kết quả mong đợi: 15");
    
    if (maxFlow2 === 15) {
        console.log("✓ TEST CASE 2 PASSED!");
    } else {
        console.log("✗ TEST CASE 2 FAILED!");
    }
    
    console.log("\n=== DEMO HOÀN THÀNH ===");
    console.log("Chương trình C++ sẽ có giao diện tương tự với thêm:");
    console.log("- Menu tương tác");
    console.log("- Nhập đồ thị từ người dùng");
    console.log("- Tìm lát cắt tối thiểu");
    console.log("- Nhiều test cases khác");
}

main();
